import { SetMetadata, applyDecorators } from '@nestjs/common';
import { CacheConfig } from '../services/response-cache.service';

/**
 * 缓存配置元数据键
 */
export const CACHE_CONFIG_KEY = 'cache_config';

/**
 * 缓存装饰器配置接口
 */
export interface CacheDecoratorConfig extends CacheConfig {
  /** 缓存键生成器 */
  keyGenerator?: (method: string, path: string, params?: any, userId?: string | number) => string;
  /** 是否包含用户ID在缓存键中 */
  includeUserId?: boolean;
  /** 是否包含查询参数在缓存键中 */
  includeParams?: boolean;
  /** 自定义缓存条件 */
  condition?: (request: any, response?: any) => boolean;
  /** 缓存标签（用于批量清理） */
  tags?: string[];
}

/**
 * 响应缓存装饰器
 * 为API方法添加自动缓存功能
 */
export function Cache(config?: CacheDecoratorConfig) {
  return applyDecorators(
    SetMetadata(CACHE_CONFIG_KEY, config || {})
  );
}

/**
 * 短期缓存装饰器（1分钟）
 */
export function ShortCache(ttl: number = 60) {
  return Cache({
    defaultTtl: ttl,
    enabled: true,
  });
}

/**
 * 中期缓存装饰器（5分钟）
 */
export function MediumCache(ttl: number = 300) {
  return Cache({
    defaultTtl: ttl,
    enabled: true,
  });
}

/**
 * 长期缓存装饰器（1小时）
 */
export function LongCache(ttl: number = 3600) {
  return Cache({
    defaultTtl: ttl,
    enabled: true,
  });
}

/**
 * 用户相关缓存装饰器
 * 缓存键会包含用户ID
 */
export function UserCache(ttl: number = 300) {
  return Cache({
    defaultTtl: ttl,
    enabled: true,
    includeUserId: true,
  });
}

/**
 * 参数相关缓存装饰器
 * 缓存键会包含查询参数
 */
export function ParamCache(ttl: number = 300) {
  return Cache({
    defaultTtl: ttl,
    enabled: true,
    includeParams: true,
  });
}

/**
 * 完整缓存装饰器
 * 包含用户ID和查询参数
 */
export function FullCache(ttl: number = 300) {
  return Cache({
    defaultTtl: ttl,
    enabled: true,
    includeUserId: true,
    includeParams: true,
  });
}

/**
 * 条件缓存装饰器
 * 只有满足条件时才缓存
 */
export function ConditionalCache(
  condition: (request: any, response?: any) => boolean,
  ttl: number = 300
) {
  return Cache({
    defaultTtl: ttl,
    enabled: true,
    condition,
  });
}

/**
 * 标签缓存装饰器
 * 支持按标签批量清理缓存
 */
export function TaggedCache(tags: string[], ttl: number = 300) {
  return Cache({
    defaultTtl: ttl,
    enabled: true,
    tags,
  });
}

/**
 * 压缩缓存装饰器
 * 启用数据压缩
 */
export function CompressedCache(ttl: number = 300) {
  return Cache({
    defaultTtl: ttl,
    enabled: true,
    compress: true,
  });
}

/**
 * 大数据缓存装饰器
 * 适用于大响应数据
 */
export function LargeDataCache(ttl: number = 300, maxSize: number = 5 * 1024 * 1024) {
  return Cache({
    defaultTtl: ttl,
    enabled: true,
    compress: true,
    maxSize,
  });
}

/**
 * 自定义键缓存装饰器
 */
export function CustomKeyCache(
  keyGenerator: (method: string, path: string, params?: any, userId?: string | number) => string,
  ttl: number = 300
) {
  return Cache({
    defaultTtl: ttl,
    enabled: true,
    keyGenerator,
  });
}

/**
 * 禁用缓存装饰器
 * 明确禁用缓存
 */
export function NoCache() {
  return Cache({
    enabled: false,
  });
}

/**
 * 开发环境缓存装饰器
 * 只在开发环境启用缓存
 */
export function DevCache(ttl: number = 60) {
  return Cache({
    defaultTtl: ttl,
    enabled: process.env.NODE_ENV === 'development',
  });
}

/**
 * 生产环境缓存装饰器
 * 只在生产环境启用缓存
 */
export function ProdCache(ttl: number = 300) {
  return Cache({
    defaultTtl: ttl,
    enabled: process.env.NODE_ENV === 'production',
  });
}

/**
 * 缓存预热装饰器
 * 标记需要预热的缓存
 */
export function WarmupCache(ttl: number = 3600) {
  return Cache({
    defaultTtl: ttl,
    enabled: true,
    tags: ['warmup'],
  });
}

/**
 * API版本缓存装饰器
 * 包含API版本在缓存键中
 */
export function VersionedCache(version: string, ttl: number = 300) {
  return Cache({
    defaultTtl: ttl,
    enabled: true,
    prefix: `response_cache_v${version}`,
  });
}

/**
 * 分页缓存装饰器
 * 专门用于分页数据的缓存
 */
export function PaginatedCache(ttl: number = 300) {
  return Cache({
    defaultTtl: ttl,
    enabled: true,
    includeParams: true,
    tags: ['paginated'],
  });
}

/**
 * 搜索结果缓存装饰器
 * 专门用于搜索结果的缓存
 */
export function SearchCache(ttl: number = 600) {
  return Cache({
    defaultTtl: ttl,
    enabled: true,
    includeParams: true,
    includeUserId: true,
    tags: ['search'],
  });
}

/**
 * 静态数据缓存装饰器
 * 用于很少变化的静态数据
 */
export function StaticCache(ttl: number = 86400) { // 24小时
  return Cache({
    defaultTtl: ttl,
    enabled: true,
    tags: ['static'],
  });
}

/**
 * 实时数据缓存装饰器
 * 用于需要实时性的数据，缓存时间很短
 */
export function RealtimeCache(ttl: number = 10) {
  return Cache({
    defaultTtl: ttl,
    enabled: true,
    tags: ['realtime'],
  });
}

/**
 * 缓存工具类
 */
export class CacheUtils {
  /**
   * 生成基于时间的缓存键
   */
  static timeBasedKey(method: string, path: string, timeWindow: number = 60): string {
    const timeSlot = Math.floor(Date.now() / (timeWindow * 1000));
    return `${method}:${path}:${timeSlot}`;
  }

  /**
   * 生成基于用户角色的缓存键
   */
  static roleBasedKey(method: string, path: string, userRole: string): string {
    return `${method}:${path}:role:${userRole}`;
  }

  /**
   * 生成基于地理位置的缓存键
   */
  static locationBasedKey(method: string, path: string, location: string): string {
    return `${method}:${path}:location:${location}`;
  }

  /**
   * 生成基于语言的缓存键
   */
  static languageBasedKey(method: string, path: string, language: string): string {
    return `${method}:${path}:lang:${language}`;
  }

  /**
   * 生成基于设备类型的缓存键
   */
  static deviceBasedKey(method: string, path: string, deviceType: string): string {
    return `${method}:${path}:device:${deviceType}`;
  }

  /**
   * 检查是否应该缓存响应
   */
  static shouldCacheResponse(response: any): boolean {
    // 不缓存错误响应
    if (response.success === false || response.code >= 400) {
      return false;
    }

    // 不缓存空数据
    if (!response.data) {
      return false;
    }

    // 不缓存过大的响应
    const responseSize = JSON.stringify(response).length;
    if (responseSize > 1024 * 1024) { // 1MB
      return false;
    }

    return true;
  }

  /**
   * 检查是否应该使用缓存
   */
  static shouldUseCache(request: any): boolean {
    // 只缓存GET请求
    if (request.method !== 'GET') {
      return false;
    }

    // 不缓存包含认证头的请求（除非明确指定）
    if (request.headers.authorization && !request.cacheAuth) {
      return false;
    }

    return true;
  }
}
