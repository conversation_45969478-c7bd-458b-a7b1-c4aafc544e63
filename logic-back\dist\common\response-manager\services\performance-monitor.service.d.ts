import { IPerformanceMonitor, PerformanceMetrics, ResponseManagerConfig } from '../interfaces/response-manager.interface';
import { LoggerService } from '../../logger/logger.service';
export declare class PerformanceMonitorService implements IPerformanceMonitor {
    private readonly logger;
    private readonly config;
    private readonly monitoringData;
    private readonly maxHistorySize;
    constructor(logger: LoggerService, config: ResponseManagerConfig);
    startMonitoring(requestId: string): void;
    endMonitoring(requestId: string): PerformanceMetrics;
    recordDbQuery(requestId: string): void;
    recordCacheOperation(requestId: string, hit: boolean): void;
    recordExternalApiCall(requestId: string): void;
    getSystemMetrics(): {
        cpuUsage: number;
        memoryUsage: number;
        freeMemory: number;
        totalMemory: number;
        loadAverage: number[];
        uptime: number;
    };
    getActiveMonitoringCount(): number;
    private calculateMemoryUsage;
    private calculateCpuUsage;
    private getCurrentCpuUsage;
    private getDefaultMetrics;
    private logPerformanceMetrics;
    private startCleanupTimer;
    private cleanupStaleMonitoring;
}
