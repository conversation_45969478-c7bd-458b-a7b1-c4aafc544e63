"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.IpLocationUnifiedResponseTestController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const unified_response_interceptor_1 = require("../../../common/unified-response-manager/interceptors/unified-response.interceptor");
const ip_location_application_service_1 = require("../application/services/ip-location-application.service");
const ip_location_query_service_1 = require("../application/services/ip-location-query.service");
const ip_location_command_service_1 = require("../application/services/ip-location-command.service");
const get_location_by_ip_query_1 = require("../application/queries/get-location-by-ip.query");
const get_user_location_stats_query_1 = require("../application/queries/get-user-location-stats.query");
const assess_login_risk_query_1 = require("../application/queries/assess-login-risk.query");
let IpLocationUnifiedResponseTestController = class IpLocationUnifiedResponseTestController {
    ipLocationService;
    queryService;
    commandService;
    responseManager;
    constructor(ipLocationService, queryService, commandService, responseManager) {
        this.ipLocationService = ipLocationService;
        this.queryService = queryService;
        this.commandService = commandService;
        this.responseManager = responseManager;
    }
    async autoQueryIp(ip = '*******') {
        const query = new get_location_by_ip_query_1.GetLocationByIpQuery(ip, false);
        return await this.queryService.handleGetLocationByIp(query);
    }
    async autoRiskCheck(request) {
        const query = new assess_login_risk_query_1.AssessLoginRiskQuery(parseInt(request.userId), request.ip);
        return await this.queryService.handleAssessLoginRisk(query);
    }
    async decoratedQueryIp(ip = '*******') {
        const query = new get_location_by_ip_query_1.GetLocationByIpQuery(ip, false);
        return await this.queryService.handleGetLocationByIp(query);
    }
    async decoratedRiskCheck(request) {
        const query = new assess_login_risk_query_1.AssessLoginRiskQuery(parseInt(request.userId), request.ip);
        return await this.queryService.handleAssessLoginRisk(query);
    }
    async decoratedUserStats(userId) {
        const query = new get_user_location_stats_query_1.GetUserLocationStatsQuery(parseInt(userId), 30);
        return await this.queryService.handleGetUserLocationStats(query);
    }
    async realtimeCurrentIp() {
        const mockIp = '*************';
        const query = new get_location_by_ip_query_1.GetLocationByIpQuery(mockIp, false);
        return await this.queryService.handleGetLocationByIp(query);
    }
    getString() {
        return 'IP Location 模块返回的字符串';
    }
    getObject() {
        return {
            module: 'IpLocation',
            version: '1.0.0',
            features: ['IP查询', '风险评估', '位置统计'],
            timestamp: new Date()
        };
    }
    getArray() {
        return [
            { ip: '*******', country: '美国' },
            { ip: '*******', country: '美国' },
            { ip: '***************', country: '中国' }
        ];
    }
    throwError() {
        throw new Error('IP Location 模块测试错误');
    }
    getDddSuccess() {
        return {
            success: true,
            data: {
                ip: '*******',
                country: '美国',
                province: '加利福尼亚州',
                city: '山景城'
            },
            message: 'IP地理位置查询成功',
            timestamp: new Date(),
            executionTime: 120,
            fromCache: true,
            cacheKey: 'ip_location:*******'
        };
    }
    getDddError() {
        return {
            success: false,
            data: null,
            message: 'IP地址格式无效',
            errors: ['IP地址不能为空', 'IP地址格式不正确'],
            timestamp: new Date(),
            executionTime: 50
        };
    }
    getHttpResponse() {
        return {
            code: 200,
            msg: '查询成功',
            data: {
                ip: '*******',
                location: '美国'
            }
        };
    }
    getRawResponse() {
        return {
            customFormat: true,
            module: 'IpLocation',
            message: '这是原始返回格式，不会被统一处理',
            data: {
                ip: '127.0.0.1',
                location: 'localhost'
            },
            timestamp: new Date()
        };
    }
    async manualProcess(request) {
        const context = this.responseManager.createContext({ user: { id: 'test_user' } }, 'IpLocationTest', 'ManualProcess');
        try {
            const query = new get_location_by_ip_query_1.GetLocationByIpQuery(request.ip, false);
            const result = await this.queryService.handleGetLocationByIp(query);
            return await this.responseManager.handleResponse(result, context, {
                usePool: true,
                priority: 6
            });
        }
        catch (error) {
            return await this.responseManager.handleError(error, context);
        }
    }
    async getPoolStatus() {
        return await this.responseManager.getPoolStatus();
    }
    async batchQueryIps(request) {
        const results = [];
        for (const ip of request.ips) {
            try {
                const query = new get_location_by_ip_query_1.GetLocationByIpQuery(ip, false);
                const result = await this.queryService.handleGetLocationByIp(query);
                results.push({ ip, result, success: true });
            }
            catch (error) {
                results.push({ ip, error: error.message, success: false });
            }
        }
        return {
            success: true,
            data: results,
            message: `批量查询完成，共处理 ${request.ips.length} 个IP`,
            timestamp: new Date(),
            executionTime: results.length * 100
        };
    }
    async delayedQuery(request) {
        await new Promise(resolve => setTimeout(resolve, request.delay || 1000));
        const query = new get_location_by_ip_query_1.GetLocationByIpQuery(request.ip, false);
        const result = await this.queryService.handleGetLocationByIp(query);
        return {
            ...result,
            message: `延迟 ${request.delay}ms 后查询完成`,
            delayTime: request.delay
        };
    }
};
exports.IpLocationUnifiedResponseTestController = IpLocationUnifiedResponseTestController;
__decorate([
    (0, common_1.Get)('auto/query-ip'),
    (0, swagger_1.ApiOperation)({ summary: '自动处理DDD格式 - IP查询' }),
    __param(0, (0, common_1.Query)('ip')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], IpLocationUnifiedResponseTestController.prototype, "autoQueryIp", null);
__decorate([
    (0, common_1.Post)('auto/risk-check'),
    (0, swagger_1.ApiOperation)({ summary: '自动处理DDD格式 - 风险检查' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], IpLocationUnifiedResponseTestController.prototype, "autoRiskCheck", null);
__decorate([
    (0, common_1.Get)('decorated/query-ip'),
    (0, swagger_1.ApiOperation)({ summary: '装饰器处理 - IP查询' }),
    (0, unified_response_interceptor_1.UnifiedResponse)({
        controller: 'IpLocationTest',
        action: 'QueryIp',
        usePool: false,
        priority: 5
    }),
    __param(0, (0, common_1.Query)('ip')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], IpLocationUnifiedResponseTestController.prototype, "decoratedQueryIp", null);
__decorate([
    (0, common_1.Post)('decorated/risk-check'),
    (0, swagger_1.ApiOperation)({ summary: '装饰器处理 - 风险检查' }),
    (0, unified_response_interceptor_1.HighPerformanceResponse)({
        controller: 'IpLocationTest',
        action: 'RiskCheck'
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], IpLocationUnifiedResponseTestController.prototype, "decoratedRiskCheck", null);
__decorate([
    (0, common_1.Get)('decorated/user-stats/:userId'),
    (0, swagger_1.ApiOperation)({ summary: '装饰器处理 - 用户统计' }),
    (0, unified_response_interceptor_1.BatchResponse)({
        controller: 'IpLocationTest',
        action: 'UserStats'
    }),
    __param(0, (0, common_1.Param)('userId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], IpLocationUnifiedResponseTestController.prototype, "decoratedUserStats", null);
__decorate([
    (0, common_1.Get)('realtime/current-ip'),
    (0, swagger_1.ApiOperation)({ summary: '实时响应 - 获取当前IP' }),
    (0, unified_response_interceptor_1.RealtimeResponse)({
        controller: 'IpLocationTest',
        action: 'GetCurrentIp'
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], IpLocationUnifiedResponseTestController.prototype, "realtimeCurrentIp", null);
__decorate([
    (0, common_1.Get)('types/string'),
    (0, swagger_1.ApiOperation)({ summary: '返回字符串类型' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", String)
], IpLocationUnifiedResponseTestController.prototype, "getString", null);
__decorate([
    (0, common_1.Get)('types/object'),
    (0, swagger_1.ApiOperation)({ summary: '返回普通对象' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Object)
], IpLocationUnifiedResponseTestController.prototype, "getObject", null);
__decorate([
    (0, common_1.Get)('types/array'),
    (0, swagger_1.ApiOperation)({ summary: '返回数组' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Array)
], IpLocationUnifiedResponseTestController.prototype, "getArray", null);
__decorate([
    (0, common_1.Get)('types/error'),
    (0, swagger_1.ApiOperation)({ summary: '抛出错误测试' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], IpLocationUnifiedResponseTestController.prototype, "throwError", null);
__decorate([
    (0, common_1.Get)('mixed/ddd-success'),
    (0, swagger_1.ApiOperation)({ summary: 'DDD成功格式' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Object)
], IpLocationUnifiedResponseTestController.prototype, "getDddSuccess", null);
__decorate([
    (0, common_1.Get)('mixed/ddd-error'),
    (0, swagger_1.ApiOperation)({ summary: 'DDD错误格式' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Object)
], IpLocationUnifiedResponseTestController.prototype, "getDddError", null);
__decorate([
    (0, common_1.Get)('mixed/http-response'),
    (0, swagger_1.ApiOperation)({ summary: 'HttpResponse格式' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Object)
], IpLocationUnifiedResponseTestController.prototype, "getHttpResponse", null);
__decorate([
    (0, common_1.Get)('disabled/raw'),
    (0, swagger_1.ApiOperation)({ summary: '禁用统一响应处理' }),
    (0, unified_response_interceptor_1.DisableUnifiedResponse)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Object)
], IpLocationUnifiedResponseTestController.prototype, "getRawResponse", null);
__decorate([
    (0, common_1.Post)('manual/process'),
    (0, swagger_1.ApiOperation)({ summary: '手动使用响应管理器' }),
    (0, unified_response_interceptor_1.DisableUnifiedResponse)(),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], IpLocationUnifiedResponseTestController.prototype, "manualProcess", null);
__decorate([
    (0, common_1.Get)('monitor/pool-status'),
    (0, swagger_1.ApiOperation)({ summary: '获取请求池状态' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], IpLocationUnifiedResponseTestController.prototype, "getPoolStatus", null);
__decorate([
    (0, common_1.Post)('batch/query-ips'),
    (0, swagger_1.ApiOperation)({ summary: '批量IP查询' }),
    (0, unified_response_interceptor_1.BatchResponse)({
        controller: 'IpLocationTest',
        action: 'BatchQueryIps'
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], IpLocationUnifiedResponseTestController.prototype, "batchQueryIps", null);
__decorate([
    (0, common_1.Post)('async/delayed-query'),
    (0, swagger_1.ApiOperation)({ summary: '延迟查询测试' }),
    (0, unified_response_interceptor_1.UnifiedResponse)({
        controller: 'IpLocationTest',
        action: 'DelayedQuery',
        timeout: 10000
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], IpLocationUnifiedResponseTestController.prototype, "delayedQuery", null);
exports.IpLocationUnifiedResponseTestController = IpLocationUnifiedResponseTestController = __decorate([
    (0, swagger_1.ApiTags)('IP Location 统一响应测试'),
    (0, common_1.Controller)('test/ip-location-unified-response'),
    __param(3, (0, common_1.Inject)('UNIFIED_RESPONSE_MANAGER')),
    __metadata("design:paramtypes", [ip_location_application_service_1.IpLocationApplicationService,
        ip_location_query_service_1.IpLocationQueryService,
        ip_location_command_service_1.IpLocationCommandService, Object])
], IpLocationUnifiedResponseTestController);
//# sourceMappingURL=unified-response-test.controller.js.map