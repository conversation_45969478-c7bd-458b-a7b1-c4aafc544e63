import {
  Controller,
  Get,
  Delete,
  Query,
  Param,
  Post,
  Body,
  UseGuards,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiQuery, ApiParam } from '@nestjs/swagger';
import { ResponseCacheService } from '../services/response-cache.service';
import { LoggerService } from '../../logger/logger.service';
import {
  ApiSuccessResponse,
  ApiResponseDocs,
} from '../decorators/response.decorator';

/**
 * 缓存管理控制器
 * 提供缓存管理的API接口
 */
@ApiTags('缓存管理')
@Controller('cache-management')
// @UseGuards(AdminGuard) // 建议添加管理员权限验证
export class CacheManagementController {
  constructor(
    private readonly cacheService: ResponseCacheService,
    private readonly loggerService: LoggerService,
  ) {}

  /**
   * 获取缓存统计信息
   */
  @Get('stats')
  @ApiOperation({ summary: '获取缓存统计信息' })
  @ApiSuccessResponse('获取缓存统计成功')
  async getCacheStats() {
    const stats = await this.cacheService.getStats();
    
    return {
      ...stats,
      timestamp: new Date().toISOString(),
      formattedSize: this.formatBytes(stats.totalSize),
    };
  }

  /**
   * 检查特定缓存是否存在
   */
  @Get('exists')
  @ApiOperation({ summary: '检查缓存是否存在' })
  @ApiQuery({ name: 'method', description: 'HTTP方法' })
  @ApiQuery({ name: 'path', description: '请求路径' })
  @ApiQuery({ name: 'userId', description: '用户ID', required: false })
  @ApiSuccessResponse('检查缓存存在性成功')
  async checkCacheExists(
    @Query('method') method: string,
    @Query('path') path: string,
    @Query('userId') userId?: string,
  ) {
    const exists = await this.cacheService.exists(
      method,
      path,
      null,
      userId ? parseInt(userId) : undefined
    );

    let ttl = -1;
    if (exists) {
      ttl = await this.cacheService.getTtl(
        method,
        path,
        null,
        userId ? parseInt(userId) : undefined
      );
    }

    return {
      exists,
      ttl,
      expiresAt: ttl > 0 ? new Date(Date.now() + ttl * 1000).toISOString() : null,
    };
  }

  /**
   * 获取缓存内容
   */
  @Get('content')
  @ApiOperation({ summary: '获取缓存内容' })
  @ApiQuery({ name: 'method', description: 'HTTP方法' })
  @ApiQuery({ name: 'path', description: '请求路径' })
  @ApiQuery({ name: 'userId', description: '用户ID', required: false })
  @ApiSuccessResponse('获取缓存内容成功')
  async getCacheContent(
    @Query('method') method: string,
    @Query('path') path: string,
    @Query('userId') userId?: string,
  ) {
    const content = await this.cacheService.get(
      method,
      path,
      null,
      userId ? parseInt(userId) : undefined
    );

    if (!content) {
      return {
        exists: false,
        content: null,
      };
    }

    return {
      exists: true,
      content,
      size: JSON.stringify(content).length,
      formattedSize: this.formatBytes(JSON.stringify(content).length),
    };
  }

  /**
   * 删除特定缓存
   */
  @Delete('item')
  @ApiOperation({ summary: '删除特定缓存' })
  @ApiQuery({ name: 'method', description: 'HTTP方法' })
  @ApiQuery({ name: 'path', description: '请求路径' })
  @ApiQuery({ name: 'userId', description: '用户ID', required: false })
  @ApiSuccessResponse('删除缓存成功')
  async deleteCacheItem(
    @Query('method') method: string,
    @Query('path') path: string,
    @Query('userId') userId?: string,
  ) {
    const deleted = await this.cacheService.delete(
      method,
      path,
      null,
      userId ? parseInt(userId) : undefined
    );

    return {
      deleted,
      message: deleted ? '缓存删除成功' : '缓存不存在或删除失败',
    };
  }

  /**
   * 按模式删除缓存
   */
  @Delete('pattern/:pattern')
  @ApiOperation({ summary: '按模式删除缓存' })
  @ApiParam({ name: 'pattern', description: '匹配模式，如 GET:*/users/*' })
  @ApiSuccessResponse('按模式删除缓存成功')
  async deleteCacheByPattern(@Param('pattern') pattern: string) {
    const deletedCount = await this.cacheService.deleteByPattern(pattern);

    return {
      deletedCount,
      pattern,
      message: `删除了 ${deletedCount} 个缓存项`,
    };
  }

  /**
   * 清空所有缓存
   */
  @Delete('all')
  @ApiOperation({ summary: '清空所有缓存' })
  @ApiSuccessResponse('清空所有缓存成功')
  async clearAllCache() {
    const deletedCount = await this.cacheService.clear();

    this.loggerService.logBusiness(
      'CacheManagement',
      'clear_all_cache',
      {
        deletedCount,
        timestamp: new Date().toISOString(),
      }
    );

    return {
      deletedCount,
      message: `清空了 ${deletedCount} 个缓存项`,
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * 预热缓存
   */
  @Post('warmup')
  @ApiOperation({ summary: '预热缓存' })
  @ApiSuccessResponse('缓存预热成功')
  async warmupCache(@Body() warmupConfig: {
    endpoints: Array<{
      method: string;
      path: string;
      params?: any;
      userId?: number;
    }>;
  }) {
    const results: Array<{
      method: string;
      path: string;
      status: string;
      message: string;
    }> = [];

    for (const endpoint of warmupConfig.endpoints) {
      try {
        // 这里应该调用实际的API来生成缓存
        // 为了演示，我们只是记录预热请求
        results.push({
          method: endpoint.method,
          path: endpoint.path,
          status: 'scheduled',
          message: '预热请求已安排',
        });
      } catch (error: any) {
        results.push({
          method: endpoint.method,
          path: endpoint.path,
          status: 'failed',
          message: error.message || '未知错误',
        });
      }
    }

    this.loggerService.logBusiness(
      'CacheManagement',
      'warmup_cache',
      {
        endpointsCount: warmupConfig.endpoints.length,
        results,
        timestamp: new Date().toISOString(),
      }
    );

    return {
      totalEndpoints: warmupConfig.endpoints.length,
      results,
      message: '缓存预热任务已启动',
    };
  }

  /**
   * 获取缓存健康状态
   */
  @Get('health')
  @ApiOperation({ summary: '获取缓存健康状态' })
  @ApiSuccessResponse('获取缓存健康状态成功')
  async getCacheHealth() {
    try {
      const stats = await this.cacheService.getStats();
      const isHealthy = stats.totalKeys >= 0; // 基本的健康检查

      return {
        healthy: isHealthy,
        stats,
        timestamp: new Date().toISOString(),
        checks: {
          connection: isHealthy,
          keyCount: stats.totalKeys,
          totalSize: stats.totalSize,
          avgTtl: stats.avgTtl,
        },
      };
    } catch (error) {
      return {
        healthy: false,
        error: error.message,
        timestamp: new Date().toISOString(),
      };
    }
  }

  /**
   * 获取缓存配置信息
   */
  @Get('config')
  @ApiOperation({ summary: '获取缓存配置信息' })
  @ApiSuccessResponse('获取缓存配置成功')
  async getCacheConfig() {
    return {
      defaultTtl: 300,
      maxSize: 1024 * 1024,
      compressionEnabled: false,
      environment: process.env.NODE_ENV,
      redisConnected: true, // 这里应该检查实际的Redis连接状态
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * 获取热门缓存键
   */
  @Get('popular')
  @ApiOperation({ summary: '获取热门缓存键' })
  @ApiQuery({ name: 'limit', description: '返回数量限制', required: false })
  @ApiSuccessResponse('获取热门缓存键成功')
  async getPopularCacheKeys(@Query('limit') limit: string = '10') {
    // 这里应该实现基于访问频率的热门缓存键统计
    // 为了演示，返回模拟数据
    const mockPopularKeys = [
      { key: 'GET:/api/users', hits: 1250, lastAccess: new Date() },
      { key: 'GET:/api/posts', hits: 980, lastAccess: new Date() },
      { key: 'GET:/api/categories', hits: 750, lastAccess: new Date() },
    ];

    return {
      popularKeys: mockPopularKeys.slice(0, parseInt(limit)),
      totalTracked: mockPopularKeys.length,
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * 格式化字节数
   */
  private formatBytes(bytes: number): string {
    if (bytes === 0) return '0 Bytes';

    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}
