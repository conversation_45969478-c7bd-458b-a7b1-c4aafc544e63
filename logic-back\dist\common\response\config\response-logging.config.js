"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ResponseLoggingUtils = exports.developmentResponseLoggingConfig = exports.productionResponseLoggingConfig = exports.defaultResponseLoggingConfig = void 0;
exports.getResponseLoggingConfig = getResponseLoggingConfig;
exports.shouldLogPath = shouldLogPath;
exports.shouldLogUserAgent = shouldLogUserAgent;
exports.shouldLogPerformance = shouldLogPerformance;
exports.shouldLogRequestBody = shouldLogRequestBody;
exports.shouldLogResponseBody = shouldLogResponseBody;
exports.shouldLogSensitiveData = shouldLogSensitiveData;
exports.shouldLogProcessingSteps = shouldLogProcessingSteps;
exports.shouldLogTransformations = shouldLogTransformations;
exports.shouldLogCacheOperations = shouldLogCacheOperations;
exports.getLogLevel = getLogLevel;
exports.defaultResponseLoggingConfig = {
    enabled: true,
    logRequestBody: true,
    logResponseBody: true,
    logSensitiveData: process.env.NODE_ENV === 'development',
    performanceThreshold: 1000,
    logSuccessResponses: true,
    logErrorResponses: true,
    logProcessingSteps: process.env.NODE_ENV === 'development',
    logTransformations: process.env.NODE_ENV === 'development',
    logCacheOperations: true,
    excludePaths: [
        '/health',
        '/metrics',
        '/favicon.ico',
        '/api-docs',
        '/swagger',
        '/swagger-ui',
        '/weixin/message',
    ],
    excludeUserAgents: [
        'kube-probe',
        'Prometheus',
        'ELB-HealthChecker',
    ],
    logLevels: {
        success: 'info',
        error: 'error',
        performance: 'warn',
        processing: 'debug',
        transformation: 'debug',
        cache: 'debug',
    },
};
exports.productionResponseLoggingConfig = {
    ...exports.defaultResponseLoggingConfig,
    logRequestBody: false,
    logResponseBody: false,
    logSensitiveData: false,
    logProcessingSteps: false,
    logTransformations: false,
    logLevels: {
        success: 'info',
        error: 'error',
        performance: 'warn',
        processing: 'info',
        transformation: 'info',
        cache: 'info',
    },
};
exports.developmentResponseLoggingConfig = {
    ...exports.defaultResponseLoggingConfig,
    logRequestBody: true,
    logResponseBody: true,
    logSensitiveData: true,
    logProcessingSteps: true,
    logTransformations: true,
    performanceThreshold: 500,
    logLevels: {
        success: 'debug',
        error: 'error',
        performance: 'info',
        processing: 'debug',
        transformation: 'debug',
        cache: 'debug',
    },
};
function getResponseLoggingConfig() {
    const env = process.env.NODE_ENV || 'development';
    switch (env) {
        case 'production':
            return exports.productionResponseLoggingConfig;
        case 'development':
            return exports.developmentResponseLoggingConfig;
        default:
            return exports.defaultResponseLoggingConfig;
    }
}
function shouldLogPath(path, config = getResponseLoggingConfig()) {
    if (!config.enabled) {
        return false;
    }
    return !config.excludePaths.some(excludePath => path.includes(excludePath));
}
function shouldLogUserAgent(userAgent, config = getResponseLoggingConfig()) {
    if (!config.enabled || !userAgent) {
        return true;
    }
    return !config.excludeUserAgents.some(excludeAgent => userAgent.toLowerCase().includes(excludeAgent.toLowerCase()));
}
function shouldLogPerformance(executionTime, config = getResponseLoggingConfig()) {
    return config.enabled && executionTime >= config.performanceThreshold;
}
function shouldLogRequestBody(config = getResponseLoggingConfig()) {
    return config.enabled && config.logRequestBody;
}
function shouldLogResponseBody(config = getResponseLoggingConfig()) {
    return config.enabled && config.logResponseBody;
}
function shouldLogSensitiveData(config = getResponseLoggingConfig()) {
    return config.enabled && config.logSensitiveData;
}
function shouldLogProcessingSteps(config = getResponseLoggingConfig()) {
    return config.enabled && config.logProcessingSteps;
}
function shouldLogTransformations(config = getResponseLoggingConfig()) {
    return config.enabled && config.logTransformations;
}
function shouldLogCacheOperations(config = getResponseLoggingConfig()) {
    return config.enabled && config.logCacheOperations;
}
function getLogLevel(type, config = getResponseLoggingConfig()) {
    return config.logLevels[type];
}
class ResponseLoggingUtils {
    static config = getResponseLoggingConfig();
    static updateConfig(newConfig) {
        this.config = { ...this.config, ...newConfig };
    }
    static getConfig() {
        return this.config;
    }
    static shouldLog(path, userAgent) {
        return shouldLogPath(path, this.config) && shouldLogUserAgent(userAgent, this.config);
    }
    static getLogContext(request, response) {
        const context = {
            method: request.method,
            path: request.path,
            ip: request.ip,
            userAgent: request.get?.('User-Agent'),
            timestamp: new Date().toISOString(),
        };
        if (request.user?.id) {
            context.userId = request.user.id;
        }
        if (response) {
            context.statusCode = response.code || response.statusCode;
            context.success = response.success !== undefined ? response.success : response.statusCode < 400;
        }
        return context;
    }
    static sanitizeData(data) {
        if (!this.config.logSensitiveData) {
            return this.deepSanitize(data);
        }
        return data;
    }
    static deepSanitize(obj) {
        if (!obj || typeof obj !== 'object') {
            return obj;
        }
        const sensitiveFields = [
            'password', 'token', 'secret', 'key', 'authorization',
            'credit_card', 'ssn', 'phone', 'email', 'id_card',
            'cookie', 'session', 'csrf'
        ];
        if (Array.isArray(obj)) {
            return obj.map(item => this.deepSanitize(item));
        }
        const sanitized = {};
        for (const [key, value] of Object.entries(obj)) {
            const lowerKey = key.toLowerCase();
            if (sensitiveFields.some(field => lowerKey.includes(field))) {
                sanitized[key] = '***REDACTED***';
            }
            else {
                sanitized[key] = this.deepSanitize(value);
            }
        }
        return sanitized;
    }
}
exports.ResponseLoggingUtils = ResponseLoggingUtils;
//# sourceMappingURL=response-logging.config.js.map