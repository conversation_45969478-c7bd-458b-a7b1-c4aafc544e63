"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UnifiedResponseInterceptor = exports.UNIFIED_RESPONSE_METADATA = void 0;
exports.UnifiedResponse = UnifiedResponse;
exports.HighPerformanceResponse = HighPerformanceResponse;
exports.RealtimeResponse = RealtimeResponse;
exports.BatchResponse = BatchResponse;
exports.LowPriorityResponse = LowPriorityResponse;
exports.HighPriorityResponse = HighPriorityResponse;
exports.DisableUnifiedResponse = DisableUnifiedResponse;
exports.getUnifiedResponseOptions = getUnifiedResponseOptions;
exports.isUnifiedResponseEnabled = isUnifiedResponseEnabled;
const common_1 = require("@nestjs/common");
const core_1 = require("@nestjs/core");
const operators_1 = require("rxjs/operators");
exports.UNIFIED_RESPONSE_METADATA = 'unified_response_metadata';
let UnifiedResponseInterceptor = class UnifiedResponseInterceptor {
    reflector;
    responseManager;
    constructor(reflector, responseManager) {
        this.reflector = reflector;
        this.responseManager = responseManager;
    }
    intercept(context, next) {
        const options = this.reflector.get(exports.UNIFIED_RESPONSE_METADATA, context.getHandler()) || { enabled: true };
        if (options.enabled === false) {
            return next.handle();
        }
        const request = context.switchToHttp().getRequest();
        const controllerClass = context.getClass();
        const handlerMethod = context.getHandler();
        const requestContext = this.responseManager.createContext(request, options.controller || controllerClass.name.replace('Controller', ''), options.action || handlerMethod.name);
        return next.handle().pipe((0, operators_1.switchMap)(async (result) => {
            try {
                return await this.responseManager.handleResponse(result, requestContext, {
                    usePool: options.usePool,
                    priority: options.priority,
                    timeout: options.timeout
                });
            }
            catch (error) {
                return await this.responseManager.handleError(error, requestContext);
            }
        }), (0, operators_1.catchError)(async (error) => {
            return await this.responseManager.handleError(error, requestContext);
        }));
    }
};
exports.UnifiedResponseInterceptor = UnifiedResponseInterceptor;
exports.UnifiedResponseInterceptor = UnifiedResponseInterceptor = __decorate([
    (0, common_1.Injectable)(),
    __param(1, (0, common_1.Inject)('UNIFIED_RESPONSE_MANAGER')),
    __metadata("design:paramtypes", [core_1.Reflector, Object])
], UnifiedResponseInterceptor);
function UnifiedResponse(options = {}) {
    return function (target, propertyKey, descriptor) {
        Reflect.defineMetadata(exports.UNIFIED_RESPONSE_METADATA, options, descriptor.value);
        return descriptor;
    };
}
function HighPerformanceResponse(options = {}) {
    return UnifiedResponse({
        ...options,
        usePool: true,
        priority: 8
    });
}
function RealtimeResponse(options = {}) {
    return UnifiedResponse({
        ...options,
        usePool: false
    });
}
function BatchResponse(options = {}) {
    return UnifiedResponse({
        ...options,
        usePool: true,
        priority: 2,
        timeout: options.timeout || 60000
    });
}
function LowPriorityResponse(options = {}) {
    return UnifiedResponse({
        ...options,
        priority: 1,
        usePool: true
    });
}
function HighPriorityResponse(options = {}) {
    return UnifiedResponse({
        ...options,
        priority: 9,
        usePool: true
    });
}
function DisableUnifiedResponse() {
    return UnifiedResponse({ enabled: false });
}
function getUnifiedResponseOptions(target) {
    return Reflect.getMetadata(exports.UNIFIED_RESPONSE_METADATA, target);
}
function isUnifiedResponseEnabled(target) {
    const options = Reflect.getMetadata(exports.UNIFIED_RESPONSE_METADATA, target);
    return options?.enabled !== false;
}
//# sourceMappingURL=unified-response.interceptor.js.map