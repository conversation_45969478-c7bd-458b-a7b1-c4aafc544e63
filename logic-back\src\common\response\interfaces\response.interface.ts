/**
 * 通用响应管理器接口定义
 */

/**
 * 基础响应接口
 */
export interface BaseResponse<T = any> {
  /** 状态码 */
  code: number;
  /** 消息 */
  message: string;
  /** 数据 */
  data?: T;
  /** 时间戳 */
  timestamp: Date;
  /** 请求ID */
  requestId?: string;
}

/**
 * 成功响应接口
 */
export interface SuccessResponse<T = any> extends BaseResponse<T> {
  success: true;
  /** 执行时间(毫秒) */
  executionTime?: number;
  /** 缓存信息 */
  cache?: {
    hit: boolean;
    key?: string;
    ttl?: number;
  };
}

/**
 * 错误响应接口
 */
export interface ErrorResponse<T = any> extends BaseResponse<T> {
  success: false;
  /** 错误详情 */
  errors?: string[];
  /** 错误堆栈(仅开发环境) */
  stack?: string;
  /** 错误代码 */
  errorCode?: string;
  /** 错误类型 */
  errorType?: 'VALIDATION' | 'BUSINESS' | 'SYSTEM' | 'NETWORK' | 'AUTH';
}

/**
 * 分页响应接口
 */
export interface PaginatedResponse<T = any> extends SuccessResponse<T[]> {
  pagination: {
    page: number;
    size: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

/**
 * 响应格式枚举
 */
export enum ResponseFormat {
  /** 标准格式 */
  STANDARD = 'standard',
  /** 简化格式 */
  SIMPLE = 'simple',
  /** RESTful格式 */
  RESTFUL = 'restful',
  /** 自定义格式 */
  CUSTOM = 'custom'
}

/**
 * 响应配置接口
 */
export interface ResponseConfig {
  /** 响应格式 */
  format?: ResponseFormat;
  /** 是否包含执行时间 */
  includeExecutionTime?: boolean;
  /** 是否包含请求ID */
  includeRequestId?: boolean;
  /** 是否包含缓存信息 */
  includeCacheInfo?: boolean;
  /** 自定义字段映射 */
  fieldMapping?: {
    code?: string;
    message?: string;
    data?: string;
    timestamp?: string;
  };
}

/**
 * 请求上下文接口
 */
export interface RequestContext {
  /** 请求ID */
  requestId: string;
  /** 开始时间 */
  startTime: number;
  /** 用户ID */
  userId?: string | number;
  /** IP地址 */
  ip?: string;
  /** 用户代理 */
  userAgent?: string;
  /** 请求路径 */
  path: string;
  /** 请求方法 */
  method: string;
  /** 模块名称 */
  module?: string;
  /** 操作名称 */
  operation?: string;
  /** 元数据 */
  metadata?: Record<string, any>;
}

/**
 * 响应管理器接口
 */
export interface IResponseManager {
  /**
   * 创建成功响应
   */
  success<T>(data?: T, message?: string, config?: ResponseConfig): SuccessResponse<T>;

  /**
   * 创建错误响应
   */
  error<T>(message: string, errors?: string[], config?: ResponseConfig): ErrorResponse<T>;

  /**
   * 创建分页响应
   */
  paginated<T>(
    data: T[],
    pagination: {
      page: number;
      size: number;
      total: number;
    },
    message?: string,
    config?: ResponseConfig
  ): PaginatedResponse<T>;

  /**
   * 创建自定义响应
   */
  custom<T>(
    code: number,
    message: string,
    data?: T,
    config?: ResponseConfig
  ): BaseResponse<T>;

  /**
   * 设置请求上下文
   */
  setContext(context: Partial<RequestContext>): void;

  /**
   * 获取请求上下文
   */
  getContext(): RequestContext | null;

  /**
   * 格式化响应
   */
  format<T>(response: BaseResponse<T>, format?: ResponseFormat): any;
}

/**
 * 状态码常量
 */
export const StatusCodes = {
  // 成功状态码
  SUCCESS: 200,
  CREATED: 201,
  ACCEPTED: 202,
  NO_CONTENT: 204,

  // 客户端错误状态码
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  METHOD_NOT_ALLOWED: 405,
  CONFLICT: 409,
  VALIDATION_ERROR: 422,

  // 服务器错误状态码
  INTERNAL_SERVER_ERROR: 500,
  NOT_IMPLEMENTED: 501,
  BAD_GATEWAY: 502,
  SERVICE_UNAVAILABLE: 503,
  GATEWAY_TIMEOUT: 504,
} as const;

/**
 * 默认消息常量
 */
export const DefaultMessages = {
  SUCCESS: '操作成功',
  CREATED: '创建成功',
  UPDATED: '更新成功',
  DELETED: '删除成功',
  NOT_FOUND: '资源不存在',
  UNAUTHORIZED: '未授权访问',
  FORBIDDEN: '权限不足',
  BAD_REQUEST: '请求参数错误',
  VALIDATION_ERROR: '数据验证失败',
  INTERNAL_ERROR: '系统内部错误',
  SERVICE_UNAVAILABLE: '服务暂时不可用',
} as const;
