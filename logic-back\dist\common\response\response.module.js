"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CustomResponseModule = exports.ResponseModule = void 0;
const common_1 = require("@nestjs/common");
const core_1 = require("@nestjs/core");
const logger_module_1 = require("../logger/logger.module");
const response_manager_service_1 = require("./services/response-manager.service");
const response_interceptor_1 = require("./interceptors/response.interceptor");
const response_exception_filter_1 = require("./filters/response-exception.filter");
let ResponseModule = class ResponseModule {
};
exports.ResponseModule = ResponseModule;
exports.ResponseModule = ResponseModule = __decorate([
    (0, common_1.Global)(),
    (0, common_1.Module)({
        imports: [logger_module_1.LoggerModule],
        providers: [
            response_manager_service_1.ResponseManagerService,
            {
                provide: core_1.APP_INTERCEPTOR,
                useClass: response_interceptor_1.GlobalResponseInterceptor,
            },
            {
                provide: core_1.APP_FILTER,
                useClass: response_exception_filter_1.ResponseExceptionFilter,
            },
        ],
        exports: [response_manager_service_1.ResponseManagerService],
    })
], ResponseModule);
let CustomResponseModule = class CustomResponseModule {
};
exports.CustomResponseModule = CustomResponseModule;
exports.CustomResponseModule = CustomResponseModule = __decorate([
    (0, common_1.Module)({
        imports: [logger_module_1.LoggerModule],
        providers: [
            response_manager_service_1.ResponseManagerService,
            response_interceptor_1.ResponseInterceptor,
            response_exception_filter_1.ResponseExceptionFilter,
            response_exception_filter_1.HttpExceptionFilter,
        ],
        exports: [
            response_manager_service_1.ResponseManagerService,
            response_interceptor_1.ResponseInterceptor,
            response_exception_filter_1.ResponseExceptionFilter,
            response_exception_filter_1.HttpExceptionFilter,
        ],
    })
], CustomResponseModule);
//# sourceMappingURL=response.module.js.map