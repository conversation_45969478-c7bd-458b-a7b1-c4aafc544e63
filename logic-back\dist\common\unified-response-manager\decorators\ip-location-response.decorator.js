"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.IP_LOCATION_RESPONSE_METADATA = void 0;
exports.IpLocationResponse = IpLocationResponse;
exports.IpQueryResponse = IpQueryResponse;
exports.RiskAssessmentResponse = RiskAssessmentResponse;
exports.LocationStatsResponse = LocationStatsResponse;
exports.TrustLocationResponse = TrustLocationResponse;
exports.IpLocationHealthResponse = IpLocationHealthResponse;
exports.CurrentIpResponse = CurrentIpResponse;
exports.getIpLocationResponseConfig = getIpLocationResponseConfig;
exports.isIpLocationResponseEnabled = isIpLocationResponseEnabled;
exports.mergeIpLocationResponseConfig = mergeIpLocationResponseConfig;
const common_1 = require("@nestjs/common");
const common_2 = require("@nestjs/common");
const unified_response_interceptor_1 = require("../interceptors/unified-response.interceptor");
exports.IP_LOCATION_RESPONSE_METADATA = 'ip_location_response_metadata';
function IpLocationResponse(config = {}) {
    const unifiedConfig = {
        enabled: true,
        usePool: config.usePool ?? false,
        priority: config.priority ?? 5,
        timeout: config.timeout ?? 10000,
        controller: config.controller || 'IpLocation',
        action: config.action,
        ...config
    };
    return (0, common_1.applyDecorators)((0, common_1.SetMetadata)(exports.IP_LOCATION_RESPONSE_METADATA, config), (0, common_2.UseInterceptors)(unified_response_interceptor_1.UnifiedResponseInterceptor), (0, common_1.SetMetadata)('unified_response_metadata', unifiedConfig));
}
function IpQueryResponse(config = {}) {
    return IpLocationResponse({
        ...config,
        action: 'QueryLocation',
        enableLocationCache: config.enableLocationCache ?? true,
        locationCacheTTL: config.locationCacheTTL ?? 3600,
        logLocationAccess: config.logLocationAccess ?? true
    });
}
function RiskAssessmentResponse(config = {}) {
    return IpLocationResponse({
        ...config,
        action: 'AssessRisk',
        enableRiskAssessment: config.enableRiskAssessment ?? true,
        usePool: config.usePool ?? true,
        priority: config.priority ?? 8,
        logLocationAccess: config.logLocationAccess ?? true
    });
}
function LocationStatsResponse(config = {}) {
    return IpLocationResponse({
        ...config,
        action: 'GetLocationStats',
        usePool: config.usePool ?? true,
        priority: config.priority ?? 3,
        timeout: config.timeout ?? 30000,
        enableLocationCache: config.enableLocationCache ?? true,
        locationCacheTTL: config.locationCacheTTL ?? 1800
    });
}
function TrustLocationResponse(config = {}) {
    return IpLocationResponse({
        ...config,
        action: 'SetTrustedLocation',
        usePool: config.usePool ?? false,
        priority: config.priority ?? 7,
        logLocationAccess: config.logLocationAccess ?? true
    });
}
function IpLocationHealthResponse(config = {}) {
    return IpLocationResponse({
        ...config,
        action: 'HealthCheck',
        usePool: false,
        priority: 10,
        timeout: 5000,
        enableLocationCache: false,
        enableRiskAssessment: false,
        logLocationAccess: false
    });
}
function CurrentIpResponse(config = {}) {
    return IpLocationResponse({
        ...config,
        action: 'GetCurrentIp',
        enableLocationCache: config.enableLocationCache ?? true,
        locationCacheTTL: config.locationCacheTTL ?? 600,
        logLocationAccess: config.logLocationAccess ?? true
    });
}
function getIpLocationResponseConfig(target) {
    return Reflect.getMetadata(exports.IP_LOCATION_RESPONSE_METADATA, target);
}
function isIpLocationResponseEnabled(target) {
    return !!Reflect.getMetadata(exports.IP_LOCATION_RESPONSE_METADATA, target);
}
function mergeIpLocationResponseConfig(base, override) {
    return {
        ...base,
        ...override
    };
}
//# sourceMappingURL=ip-location-response.decorator.js.map