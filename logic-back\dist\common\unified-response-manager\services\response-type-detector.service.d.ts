import { IResponseTypeDetector } from '../interfaces/unified-response.interface';
export declare class ResponseTypeDetectorService implements IResponseTypeDetector {
    detectType(result: any): string;
    isHttpResponse(result: any): boolean;
    isError(result: any): boolean;
    isDddResult(result: any): boolean;
    isPaginatedData(result: any): boolean;
    isEmpty(result: any): boolean;
    getDataDescription(result: any): string;
    extractErrorInfo(error: any): {
        message: string;
        errors: string[];
        code: number;
    };
    normalizeData(result: any): any;
    needsSpecialHandling(result: any): boolean;
    getSuccessStatus(result: any): boolean;
    getResponseMessage(result: any): string;
}
