import { LoggerService } from '../../logger/logger.service';
import { IResponseManager, BaseResponse, SuccessResponse, ErrorResponse, PaginatedResponse, ResponseConfig, RequestContext, ResponseFormat } from '../interfaces/response.interface';
export declare class ResponseManagerService implements IResponseManager {
    private readonly loggerService;
    private context;
    constructor(loggerService: LoggerService);
    success<T>(data?: T, message?: string, config?: ResponseConfig): SuccessResponse<T>;
    error<T>(message?: string, errors?: string[], config?: ResponseConfig): ErrorResponse<T>;
    paginated<T>(data: T[], pagination: {
        page: number;
        size: number;
        total: number;
    }, message?: string, config?: ResponseConfig): PaginatedResponse<T>;
    custom<T>(code: number, message: string, data?: T, config?: ResponseConfig): BaseResponse<T>;
    setContext(context: Partial<RequestContext>): void;
    getContext(): RequestContext | null;
    format<T>(response: BaseResponse<T>, format?: ResponseFormat): any;
    private formatStandard;
    private formatSimple;
    private formatRestful;
    validationError(errors: string[], message?: string): ErrorResponse;
    businessError(message: string, errorCode?: string): ErrorResponse;
    authError(message?: string): ErrorResponse;
    forbiddenError(message?: string): ErrorResponse;
    notFoundError(message?: string): ErrorResponse;
    systemError(message?: string, stack?: string): ErrorResponse;
    private logResponse;
    private isSuccessResponse;
    private isErrorResponse;
    private logResponseTransform;
    logResponseCache(action: 'hit' | 'miss' | 'set' | 'invalidate', cacheKey?: string, ttl?: number): void;
}
