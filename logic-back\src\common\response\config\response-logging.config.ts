/**
 * 响应日志配置
 */

export interface ResponseLoggingConfig {
  /** 是否启用响应日志 */
  enabled: boolean;
  /** 是否记录请求体 */
  logRequestBody: boolean;
  /** 是否记录响应体 */
  logResponseBody: boolean;
  /** 是否记录敏感信息（开发环境） */
  logSensitiveData: boolean;
  /** 性能日志阈值（毫秒） */
  performanceThreshold: number;
  /** 是否记录成功响应 */
  logSuccessResponses: boolean;
  /** 是否记录错误响应 */
  logErrorResponses: boolean;
  /** 是否记录响应处理过程 */
  logProcessingSteps: boolean;
  /** 是否记录响应格式转换 */
  logTransformations: boolean;
  /** 是否记录缓存操作 */
  logCacheOperations: boolean;
  /** 排除的路径 */
  excludePaths: string[];
  /** 排除的用户代理 */
  excludeUserAgents: string[];
  /** 日志级别配置 */
  logLevels: {
    success: 'debug' | 'info' | 'warn' | 'error';
    error: 'debug' | 'info' | 'warn' | 'error';
    performance: 'debug' | 'info' | 'warn' | 'error';
    processing: 'debug' | 'info' | 'warn' | 'error';
    transformation: 'debug' | 'info' | 'warn' | 'error';
    cache: 'debug' | 'info' | 'warn' | 'error';
  };
}

/**
 * 默认响应日志配置
 */
export const defaultResponseLoggingConfig: ResponseLoggingConfig = {
  enabled: true,
  logRequestBody: true,
  logResponseBody: true,
  logSensitiveData: process.env.NODE_ENV === 'development',
  performanceThreshold: 1000, // 1秒
  logSuccessResponses: true,
  logErrorResponses: true,
  logProcessingSteps: process.env.NODE_ENV === 'development',
  logTransformations: process.env.NODE_ENV === 'development',
  logCacheOperations: true,
  excludePaths: [
    '/health',
    '/metrics',
    '/favicon.ico',
    '/api-docs',
    '/swagger',
    '/swagger-ui',
    '/weixin/message',
  ],
  excludeUserAgents: [
    'kube-probe',
    'Prometheus',
    'ELB-HealthChecker',
  ],
  logLevels: {
    success: 'info',
    error: 'error',
    performance: 'warn',
    processing: 'debug',
    transformation: 'debug',
    cache: 'debug',
  },
};

/**
 * 生产环境响应日志配置
 */
export const productionResponseLoggingConfig: ResponseLoggingConfig = {
  ...defaultResponseLoggingConfig,
  logRequestBody: false, // 生产环境不记录请求体
  logResponseBody: false, // 生产环境不记录响应体
  logSensitiveData: false,
  logProcessingSteps: false,
  logTransformations: false,
  logLevels: {
    success: 'info',
    error: 'error',
    performance: 'warn',
    processing: 'info', // 生产环境提高处理日志级别
    transformation: 'info',
    cache: 'info',
  },
};

/**
 * 开发环境响应日志配置
 */
export const developmentResponseLoggingConfig: ResponseLoggingConfig = {
  ...defaultResponseLoggingConfig,
  logRequestBody: true,
  logResponseBody: true,
  logSensitiveData: true,
  logProcessingSteps: true,
  logTransformations: true,
  performanceThreshold: 500, // 开发环境更严格的性能阈值
  logLevels: {
    success: 'debug',
    error: 'error',
    performance: 'info',
    processing: 'debug',
    transformation: 'debug',
    cache: 'debug',
  },
};

/**
 * 获取当前环境的响应日志配置
 */
export function getResponseLoggingConfig(): ResponseLoggingConfig {
  const env = process.env.NODE_ENV || 'development';
  
  switch (env) {
    case 'production':
      return productionResponseLoggingConfig;
    case 'development':
      return developmentResponseLoggingConfig;
    default:
      return defaultResponseLoggingConfig;
  }
}

/**
 * 检查是否应该记录特定路径的日志
 */
export function shouldLogPath(path: string, config: ResponseLoggingConfig = getResponseLoggingConfig()): boolean {
  if (!config.enabled) {
    return false;
  }
  
  return !config.excludePaths.some(excludePath => path.includes(excludePath));
}

/**
 * 检查是否应该记录特定用户代理的日志
 */
export function shouldLogUserAgent(userAgent: string | undefined, config: ResponseLoggingConfig = getResponseLoggingConfig()): boolean {
  if (!config.enabled || !userAgent) {
    return true;
  }
  
  return !config.excludeUserAgents.some(excludeAgent => 
    userAgent.toLowerCase().includes(excludeAgent.toLowerCase())
  );
}

/**
 * 检查是否应该记录性能日志
 */
export function shouldLogPerformance(executionTime: number, config: ResponseLoggingConfig = getResponseLoggingConfig()): boolean {
  return config.enabled && executionTime >= config.performanceThreshold;
}

/**
 * 检查是否应该记录请求体
 */
export function shouldLogRequestBody(config: ResponseLoggingConfig = getResponseLoggingConfig()): boolean {
  return config.enabled && config.logRequestBody;
}

/**
 * 检查是否应该记录响应体
 */
export function shouldLogResponseBody(config: ResponseLoggingConfig = getResponseLoggingConfig()): boolean {
  return config.enabled && config.logResponseBody;
}

/**
 * 检查是否应该记录敏感数据
 */
export function shouldLogSensitiveData(config: ResponseLoggingConfig = getResponseLoggingConfig()): boolean {
  return config.enabled && config.logSensitiveData;
}

/**
 * 检查是否应该记录处理步骤
 */
export function shouldLogProcessingSteps(config: ResponseLoggingConfig = getResponseLoggingConfig()): boolean {
  return config.enabled && config.logProcessingSteps;
}

/**
 * 检查是否应该记录格式转换
 */
export function shouldLogTransformations(config: ResponseLoggingConfig = getResponseLoggingConfig()): boolean {
  return config.enabled && config.logTransformations;
}

/**
 * 检查是否应该记录缓存操作
 */
export function shouldLogCacheOperations(config: ResponseLoggingConfig = getResponseLoggingConfig()): boolean {
  return config.enabled && config.logCacheOperations;
}

/**
 * 获取日志级别
 */
export function getLogLevel(type: keyof ResponseLoggingConfig['logLevels'], config: ResponseLoggingConfig = getResponseLoggingConfig()): string {
  return config.logLevels[type];
}

/**
 * 响应日志工具类
 */
export class ResponseLoggingUtils {
  private static config = getResponseLoggingConfig();

  /**
   * 更新配置
   */
  static updateConfig(newConfig: Partial<ResponseLoggingConfig>) {
    this.config = { ...this.config, ...newConfig };
  }

  /**
   * 获取当前配置
   */
  static getConfig(): ResponseLoggingConfig {
    return this.config;
  }

  /**
   * 检查是否应该记录日志
   */
  static shouldLog(path: string, userAgent?: string): boolean {
    return shouldLogPath(path, this.config) && shouldLogUserAgent(userAgent, this.config);
  }

  /**
   * 获取日志上下文
   */
  static getLogContext(request: any, response?: any): any {
    const context: any = {
      method: request.method,
      path: request.path,
      ip: request.ip,
      userAgent: request.get?.('User-Agent'),
      timestamp: new Date().toISOString(),
    };

    if (request.user?.id) {
      context.userId = request.user.id;
    }

    if (response) {
      context.statusCode = response.code || response.statusCode;
      context.success = response.success !== undefined ? response.success : response.statusCode < 400;
    }

    return context;
  }

  /**
   * 清理敏感数据
   */
  static sanitizeData(data: any): any {
    if (!this.config.logSensitiveData) {
      return this.deepSanitize(data);
    }
    return data;
  }

  /**
   * 深度清理敏感数据
   */
  private static deepSanitize(obj: any): any {
    if (!obj || typeof obj !== 'object') {
      return obj;
    }

    const sensitiveFields = [
      'password', 'token', 'secret', 'key', 'authorization',
      'credit_card', 'ssn', 'phone', 'email', 'id_card',
      'cookie', 'session', 'csrf'
    ];

    if (Array.isArray(obj)) {
      return obj.map(item => this.deepSanitize(item));
    }

    const sanitized: any = {};
    for (const [key, value] of Object.entries(obj)) {
      const lowerKey = key.toLowerCase();
      if (sensitiveFields.some(field => lowerKey.includes(field))) {
        sanitized[key] = '***REDACTED***';
      } else {
        sanitized[key] = this.deepSanitize(value);
      }
    }

    return sanitized;
  }
}
