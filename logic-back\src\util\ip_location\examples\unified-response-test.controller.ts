/**
 * IP Location 统一响应管理器测试控制器
 * 展示IP Location模块与统一响应管理器的集成效果
 */

import { Controller, Get, Post, Body, Param, Query, Inject } from '@nestjs/common';
import { ApiTags, ApiOperation } from '@nestjs/swagger';

// 导入通用装饰器
import {
  UnifiedResponse,
  HighPerformanceResponse,
  RealtimeResponse,
  BatchResponse,
  DisableUnifiedResponse
} from '../../../common/unified-response-manager/interceptors/unified-response.interceptor';

// 导入IP Location服务
import { IpLocationApplicationService } from '../application/services/ip-location-application.service';
import { IpLocationQueryService } from '../application/services/ip-location-query.service';
import { IpLocationCommandService } from '../application/services/ip-location-command.service';

// 导入DTO和查询对象
import { IpQueryRequestDto } from '../application/dto/requests/ip-query.request.dto';
import { RiskCheckRequestDto } from '../application/dto/requests/risk-check.request.dto';
import { GetLocationByIpQuery } from '../application/queries/get-location-by-ip.query';
import { GetUserLocationStatsQuery } from '../application/queries/get-user-location-stats.query';
import { AssessLoginRiskQuery } from '../application/queries/assess-login-risk.query';

/**
 * IP Location 统一响应测试控制器
 */
@ApiTags('IP Location 统一响应测试')
@Controller('test/ip-location-unified-response')
export class IpLocationUnifiedResponseTestController {
  constructor(
    private readonly ipLocationService: IpLocationApplicationService,
    private readonly queryService: IpLocationQueryService,
    private readonly commandService: IpLocationCommandService,
    @Inject('UNIFIED_RESPONSE_MANAGER') private readonly responseManager: any
  ) {}

  /**
   * 测试1: 自动处理DDD格式
   * 不使用装饰器，让全局拦截器自动识别DDD格式
   */
  @Get('auto/query-ip')
  @ApiOperation({ summary: '自动处理DDD格式 - IP查询' })
  async autoQueryIp(@Query('ip') ip: string = '*******') {
    // 返回DDD QueryResult格式，会被自动识别和处理
    const query = new GetLocationByIpQuery(ip as any, false);
    return await this.queryService.handleGetLocationByIp(query);
  }

  @Post('auto/risk-check')
  @ApiOperation({ summary: '自动处理DDD格式 - 风险检查' })
  async autoRiskCheck(@Body() request: { userId: string; ip: string }) {
    // 返回DDD QueryResult格式，会被自动识别和处理
    const query = new AssessLoginRiskQuery(parseInt(request.userId), request.ip as any);
    return await this.queryService.handleAssessLoginRisk(query);
  }

  /**
   * 测试2: 使用通用装饰器
   */
  @Get('decorated/query-ip')
  @ApiOperation({ summary: '装饰器处理 - IP查询' })
  @UnifiedResponse({
    controller: 'IpLocationTest',
    action: 'QueryIp',
    usePool: false,
    priority: 5
  })
  async decoratedQueryIp(@Query('ip') ip: string = '*******') {
    const query = new GetLocationByIpQuery(ip as any, false);
    return await this.queryService.handleGetLocationByIp(query);
  }

  @Post('decorated/risk-check')
  @ApiOperation({ summary: '装饰器处理 - 风险检查' })
  @HighPerformanceResponse({
    controller: 'IpLocationTest',
    action: 'RiskCheck'
  })
  async decoratedRiskCheck(@Body() request: { userId: string; ip: string }) {
    const query = new AssessLoginRiskQuery(parseInt(request.userId), request.ip as any);
    return await this.queryService.handleAssessLoginRisk(query);
  }

  @Get('decorated/user-stats/:userId')
  @ApiOperation({ summary: '装饰器处理 - 用户统计' })
  @BatchResponse({
    controller: 'IpLocationTest',
    action: 'UserStats'
  })
  async decoratedUserStats(@Param('userId') userId: string) {
    const query = new GetUserLocationStatsQuery(parseInt(userId), 30);
    return await this.queryService.handleGetUserLocationStats(query);
  }

  /**
   * 测试3: 实时响应测试
   */
  @Get('realtime/current-ip')
  @ApiOperation({ summary: '实时响应 - 获取当前IP' })
  @RealtimeResponse({
    controller: 'IpLocationTest',
    action: 'GetCurrentIp'
  })
  async realtimeCurrentIp() {
    // 模拟获取当前IP
    const mockIp = '*************';
    const query = new GetLocationByIpQuery(mockIp as any, false);
    return await this.queryService.handleGetLocationByIp(query);
  }

  /**
   * 测试4: 返回不同数据类型
   */
  @Get('types/string')
  @ApiOperation({ summary: '返回字符串类型' })
  getString(): string {
    return 'IP Location 模块返回的字符串';
  }

  @Get('types/object')
  @ApiOperation({ summary: '返回普通对象' })
  getObject(): any {
    return {
      module: 'IpLocation',
      version: '1.0.0',
      features: ['IP查询', '风险评估', '位置统计'],
      timestamp: new Date()
    };
  }

  @Get('types/array')
  @ApiOperation({ summary: '返回数组' })
  getArray(): any[] {
    return [
      { ip: '*******', country: '美国' },
      { ip: '*******', country: '美国' },
      { ip: '***************', country: '中国' }
    ];
  }

  @Get('types/error')
  @ApiOperation({ summary: '抛出错误测试' })
  throwError(): never {
    throw new Error('IP Location 模块测试错误');
  }

  /**
   * 测试5: 混合返回格式
   */
  @Get('mixed/ddd-success')
  @ApiOperation({ summary: 'DDD成功格式' })
  getDddSuccess(): any {
    return {
      success: true,
      data: {
        ip: '*******',
        country: '美国',
        province: '加利福尼亚州',
        city: '山景城'
      },
      message: 'IP地理位置查询成功',
      timestamp: new Date(),
      executionTime: 120,
      fromCache: true,
      cacheKey: 'ip_location:*******'
    };
  }

  @Get('mixed/ddd-error')
  @ApiOperation({ summary: 'DDD错误格式' })
  getDddError(): any {
    return {
      success: false,
      data: null,
      message: 'IP地址格式无效',
      errors: ['IP地址不能为空', 'IP地址格式不正确'],
      timestamp: new Date(),
      executionTime: 50
    };
  }

  @Get('mixed/http-response')
  @ApiOperation({ summary: 'HttpResponse格式' })
  getHttpResponse(): any {
    return {
      code: 200,
      msg: '查询成功',
      data: {
        ip: '*******',
        location: '美国'
      }
    };
  }

  /**
   * 测试6: 禁用统一响应
   */
  @Get('disabled/raw')
  @ApiOperation({ summary: '禁用统一响应处理' })
  @DisableUnifiedResponse()
  getRawResponse(): any {
    return {
      customFormat: true,
      module: 'IpLocation',
      message: '这是原始返回格式，不会被统一处理',
      data: {
        ip: '127.0.0.1',
        location: 'localhost'
      },
      timestamp: new Date()
    };
  }

  /**
   * 测试7: 手动使用响应管理器
   */
  @Post('manual/process')
  @ApiOperation({ summary: '手动使用响应管理器' })
  @DisableUnifiedResponse()
  async manualProcess(@Body() request: { ip: string }) {
    // 创建请求上下文
    const context = this.responseManager.createContext(
      { user: { id: 'test_user' } } as any,
      'IpLocationTest',
      'ManualProcess'
    );

    try {
      // 执行业务逻辑
      const query = new GetLocationByIpQuery(request.ip as any, false);
      const result = await this.queryService.handleGetLocationByIp(query);
      
      // 手动处理响应
      return await this.responseManager.handleResponse(result, context, {
        usePool: true,
        priority: 6
      });
    } catch (error) {
      // 手动处理错误
      return await this.responseManager.handleError(error, context);
    }
  }

  /**
   * 测试8: 监控和状态
   */
  @Get('monitor/pool-status')
  @ApiOperation({ summary: '获取请求池状态' })
  async getPoolStatus() {
    return await this.responseManager.getPoolStatus();
  }

  /**
   * 测试9: 批量IP查询
   */
  @Post('batch/query-ips')
  @ApiOperation({ summary: '批量IP查询' })
  @BatchResponse({
    controller: 'IpLocationTest',
    action: 'BatchQueryIps'
  })
  async batchQueryIps(@Body() request: { ips: string[] }) {
    const results: any[] = [];

    for (const ip of request.ips) {
      try {
        const query = new GetLocationByIpQuery(ip as any, false);
        const result = await this.queryService.handleGetLocationByIp(query);
        results.push({ ip, result, success: true });
      } catch (error) {
        results.push({ ip, error: error.message, success: false });
      }
    }

    return {
      success: true,
      data: results,
      message: `批量查询完成，共处理 ${request.ips.length} 个IP`,
      timestamp: new Date(),
      executionTime: results.length * 100 // 模拟执行时间
    };
  }

  /**
   * 测试10: 异步操作
   */
  @Post('async/delayed-query')
  @ApiOperation({ summary: '延迟查询测试' })
  @UnifiedResponse({
    controller: 'IpLocationTest',
    action: 'DelayedQuery',
    timeout: 10000
  })
  async delayedQuery(@Body() request: { ip: string; delay: number }) {
    // 模拟延迟
    await new Promise(resolve => setTimeout(resolve, request.delay || 1000));
    
    const query = new GetLocationByIpQuery(request.ip as any, false);
    const result = await this.queryService.handleGetLocationByIp(query);
    
    return {
      ...result,
      message: `延迟 ${request.delay}ms 后查询完成`,
      delayTime: request.delay
    };
  }
}
