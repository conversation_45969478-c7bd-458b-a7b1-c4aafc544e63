import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

// 实体
import { UserCommonLocation } from './domain/entities/user-common-location.entity';

// 控制器
import { IpLocationController } from './controllers/ip-location.controller';

// 应用服务
import { IpLocationApplicationService } from './application/services/ip-location-application.service';
import { IpLocationCommandService } from './application/services/ip-location-command.service';
import { IpLocationQueryService } from './application/services/ip-location-query.service';
import { IpLocationFacadeService } from './application/services/ip-location-facade.service';

// 领域服务
import { IpLocationDomainService } from './domain/services/ip-location-domain.service';
import { RiskAssessmentDomainService } from './domain/services/risk-assessment-domain.service';
import { LocationComparisonService } from './domain/services/location-comparison.service';

// 基础设施 - 仓储
import { IpLocationCommandRepository } from './infrastructure/repositories/ip-location-command.repository';

// 基础设施 - 外部服务
import { Ip2RegionService } from './infrastructure/external/ip2region.service';
import { RedisCacheService } from './infrastructure/external/redis-cache.service';

// 工具类（保持向后兼容）
import { IpLocationUtil } from './utils/ip-location.util';
import { RiskAssessmentUtil } from './utils/risk-assessment.util';

// 基础设施依赖
import { RedisModule } from '../database/redis/redis.module';
import { LoggerService } from '../../common/logger/logger.service';
import { ResponseModule } from '../../common/response/response.module';

/**
 * IP地理位置模块
 * 基于DDD架构提供IP地理位置解析、风险评估、用户位置统计等功能
 */
@Module({
  imports: [
    // 注册实体
    TypeOrmModule.forFeature([UserCommonLocation]),

    // 导入Redis模块
    RedisModule,

    // 导入响应管理器模块
    ResponseModule,
  ],
  controllers: [
    IpLocationController,
  ],
  providers: [
    // 应用服务层
    IpLocationApplicationService,
    IpLocationCommandService,
    IpLocationQueryService,
    IpLocationFacadeService,

    // 领域服务层
    IpLocationDomainService,
    RiskAssessmentDomainService,
    LocationComparisonService,

    // 基础设施层 - 仓储
    IpLocationCommandRepository,

    // 基础设施层 - 外部服务
    Ip2RegionService,
    RedisCacheService,

    // 工具类（保持向后兼容）
    IpLocationUtil,
    RiskAssessmentUtil,

    // 基础服务
    LoggerService,
  ],
  exports: [
    // 导出应用服务供其他模块使用
    IpLocationApplicationService,
    IpLocationCommandService,
    IpLocationQueryService,
    IpLocationFacadeService,

    // 导出领域服务
    IpLocationDomainService,
    RiskAssessmentDomainService,

    // 导出基础设施服务
    Ip2RegionService,
    RedisCacheService,

    // 保持向后兼容
    IpLocationUtil,
    RiskAssessmentUtil,
  ],
})
export class IpLocationModule {}
