"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var UnifiedResponseManagerModule_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.UnifiedResponseManagerTestModule = exports.UnifiedResponseManagerFeatureModule = exports.UnifiedResponseManagerModule = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const unified_response_manager_service_1 = require("./services/unified-response-manager.service");
const request_pool_service_1 = require("./services/request-pool.service");
const performance_monitor_service_1 = require("./services/performance-monitor.service");
const response_type_detector_service_1 = require("./services/response-type-detector.service");
const logger_service_1 = require("../logger/logger.service");
const redis_module_1 = require("../../util/database/redis/redis.module");
const DEFAULT_CONFIG = {
    enableRequestPool: true,
    maxConcurrency: 10,
    requestTimeout: 30000,
    enableMetrics: true,
    enableDetailedLogging: true,
    redisKeyPrefix: 'unified_response',
    cleanupInterval: 300000
};
let UnifiedResponseManagerModule = UnifiedResponseManagerModule_1 = class UnifiedResponseManagerModule {
    static forRoot(options = {}) {
        const config = { ...DEFAULT_CONFIG, ...options.config };
        return {
            module: UnifiedResponseManagerModule_1,
            global: options.isGlobal !== false,
            imports: [
                config_1.ConfigModule,
                redis_module_1.RedisModule
            ],
            providers: [
                {
                    provide: 'UNIFIED_RESPONSE_CONFIG',
                    useValue: config
                },
                logger_service_1.LoggerService,
                {
                    provide: 'RESPONSE_TYPE_DETECTOR',
                    useClass: response_type_detector_service_1.ResponseTypeDetectorService
                },
                {
                    provide: 'PERFORMANCE_MONITOR',
                    useClass: performance_monitor_service_1.PerformanceMonitorService
                },
                {
                    provide: 'REQUEST_POOL',
                    useClass: request_pool_service_1.RequestPoolService
                },
                {
                    provide: 'UNIFIED_RESPONSE_MANAGER',
                    useClass: unified_response_manager_service_1.UnifiedResponseManagerService
                },
                unified_response_manager_service_1.UnifiedResponseManagerService,
                request_pool_service_1.RequestPoolService,
                performance_monitor_service_1.PerformanceMonitorService,
                response_type_detector_service_1.ResponseTypeDetectorService
            ],
            exports: [
                'UNIFIED_RESPONSE_CONFIG',
                'UNIFIED_RESPONSE_MANAGER',
                'REQUEST_POOL',
                'PERFORMANCE_MONITOR',
                'RESPONSE_TYPE_DETECTOR',
                unified_response_manager_service_1.UnifiedResponseManagerService,
                request_pool_service_1.RequestPoolService,
                performance_monitor_service_1.PerformanceMonitorService,
                response_type_detector_service_1.ResponseTypeDetectorService
            ]
        };
    }
    static forRootAsync(options) {
        return {
            module: UnifiedResponseManagerModule_1,
            global: options.isGlobal !== false,
            imports: [
                config_1.ConfigModule,
                redis_module_1.RedisModule,
                ...(options.imports || [])
            ],
            providers: [
                {
                    provide: 'UNIFIED_RESPONSE_CONFIG',
                    useFactory: options.useFactory,
                    inject: options.inject || []
                },
                logger_service_1.LoggerService,
                {
                    provide: 'RESPONSE_TYPE_DETECTOR',
                    useClass: response_type_detector_service_1.ResponseTypeDetectorService
                },
                {
                    provide: 'PERFORMANCE_MONITOR',
                    useClass: performance_monitor_service_1.PerformanceMonitorService
                },
                {
                    provide: 'REQUEST_POOL',
                    useClass: request_pool_service_1.RequestPoolService
                },
                {
                    provide: 'UNIFIED_RESPONSE_MANAGER',
                    useClass: unified_response_manager_service_1.UnifiedResponseManagerService
                },
                unified_response_manager_service_1.UnifiedResponseManagerService,
                request_pool_service_1.RequestPoolService,
                performance_monitor_service_1.PerformanceMonitorService,
                response_type_detector_service_1.ResponseTypeDetectorService
            ],
            exports: [
                'UNIFIED_RESPONSE_CONFIG',
                'UNIFIED_RESPONSE_MANAGER',
                'REQUEST_POOL',
                'PERFORMANCE_MONITOR',
                'RESPONSE_TYPE_DETECTOR',
                unified_response_manager_service_1.UnifiedResponseManagerService,
                request_pool_service_1.RequestPoolService,
                performance_monitor_service_1.PerformanceMonitorService,
                response_type_detector_service_1.ResponseTypeDetectorService
            ]
        };
    }
    static forRootWithEnv(options = {}) {
        return this.forRootAsync({
            isGlobal: options.isGlobal,
            imports: [config_1.ConfigModule],
            inject: [config_1.ConfigService],
            useFactory: (configService) => {
                const envConfig = {
                    enableRequestPool: configService.get('UNIFIED_RESPONSE_ENABLE_POOL', true),
                    maxConcurrency: configService.get('UNIFIED_RESPONSE_MAX_CONCURRENCY', 10),
                    requestTimeout: configService.get('UNIFIED_RESPONSE_TIMEOUT', 30000),
                    enableMetrics: configService.get('UNIFIED_RESPONSE_ENABLE_METRICS', true),
                    enableDetailedLogging: configService.get('UNIFIED_RESPONSE_ENABLE_LOGGING', true),
                    redisKeyPrefix: configService.get('UNIFIED_RESPONSE_REDIS_PREFIX', 'unified_response'),
                    cleanupInterval: configService.get('UNIFIED_RESPONSE_CLEANUP_INTERVAL', 300000)
                };
                return { ...DEFAULT_CONFIG, ...envConfig, ...options.config };
            }
        });
    }
};
exports.UnifiedResponseManagerModule = UnifiedResponseManagerModule;
exports.UnifiedResponseManagerModule = UnifiedResponseManagerModule = UnifiedResponseManagerModule_1 = __decorate([
    (0, common_1.Global)(),
    (0, common_1.Module)({})
], UnifiedResponseManagerModule);
let UnifiedResponseManagerFeatureModule = class UnifiedResponseManagerFeatureModule {
};
exports.UnifiedResponseManagerFeatureModule = UnifiedResponseManagerFeatureModule;
exports.UnifiedResponseManagerFeatureModule = UnifiedResponseManagerFeatureModule = __decorate([
    (0, common_1.Module)({
        imports: [redis_module_1.RedisModule],
        providers: [
            logger_service_1.LoggerService,
            response_type_detector_service_1.ResponseTypeDetectorService,
            performance_monitor_service_1.PerformanceMonitorService,
            request_pool_service_1.RequestPoolService,
            unified_response_manager_service_1.UnifiedResponseManagerService
        ],
        exports: [
            unified_response_manager_service_1.UnifiedResponseManagerService,
            request_pool_service_1.RequestPoolService,
            performance_monitor_service_1.PerformanceMonitorService,
            response_type_detector_service_1.ResponseTypeDetectorService
        ]
    })
], UnifiedResponseManagerFeatureModule);
let UnifiedResponseManagerTestModule = class UnifiedResponseManagerTestModule {
};
exports.UnifiedResponseManagerTestModule = UnifiedResponseManagerTestModule;
exports.UnifiedResponseManagerTestModule = UnifiedResponseManagerTestModule = __decorate([
    (0, common_1.Module)({
        imports: [redis_module_1.RedisModule],
        providers: [
            {
                provide: 'UNIFIED_RESPONSE_CONFIG',
                useValue: {
                    ...DEFAULT_CONFIG,
                    enableRequestPool: false,
                    enableMetrics: false,
                    enableDetailedLogging: false
                }
            },
            logger_service_1.LoggerService,
            {
                provide: 'RESPONSE_TYPE_DETECTOR',
                useClass: response_type_detector_service_1.ResponseTypeDetectorService
            },
            {
                provide: 'PERFORMANCE_MONITOR',
                useClass: performance_monitor_service_1.PerformanceMonitorService
            },
            {
                provide: 'REQUEST_POOL',
                useClass: request_pool_service_1.RequestPoolService
            },
            {
                provide: 'UNIFIED_RESPONSE_MANAGER',
                useClass: unified_response_manager_service_1.UnifiedResponseManagerService
            }
        ],
        exports: [
            'UNIFIED_RESPONSE_MANAGER',
            'REQUEST_POOL',
            'PERFORMANCE_MONITOR',
            'RESPONSE_TYPE_DETECTOR'
        ]
    })
], UnifiedResponseManagerTestModule);
//# sourceMappingURL=unified-response-manager.module.js.map