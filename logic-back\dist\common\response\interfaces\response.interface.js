"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DefaultMessages = exports.StatusCodes = exports.ResponseFormat = void 0;
var ResponseFormat;
(function (ResponseFormat) {
    ResponseFormat["STANDARD"] = "standard";
    ResponseFormat["SIMPLE"] = "simple";
    ResponseFormat["RESTFUL"] = "restful";
    ResponseFormat["CUSTOM"] = "custom";
})(ResponseFormat || (exports.ResponseFormat = ResponseFormat = {}));
exports.StatusCodes = {
    SUCCESS: 200,
    CREATED: 201,
    ACCEPTED: 202,
    NO_CONTENT: 204,
    BAD_REQUEST: 400,
    UNAUTHORIZED: 401,
    FORBIDDEN: 403,
    NOT_FOUND: 404,
    METHOD_NOT_ALLOWED: 405,
    CONFLICT: 409,
    VALIDATION_ERROR: 422,
    INTERNAL_SERVER_ERROR: 500,
    NOT_IMPLEMENTED: 501,
    BAD_GATEWAY: 502,
    SERVICE_UNAVAILABLE: 503,
    GATEWAY_TIMEOUT: 504,
};
exports.DefaultMessages = {
    SUCCESS: '操作成功',
    CREATED: '创建成功',
    UPDATED: '更新成功',
    DELETED: '删除成功',
    NOT_FOUND: '资源不存在',
    UNAUTHORIZED: '未授权访问',
    FORBIDDEN: '权限不足',
    BAD_REQUEST: '请求参数错误',
    VALIDATION_ERROR: '数据验证失败',
    INTERNAL_ERROR: '系统内部错误',
    SERVICE_UNAVAILABLE: '服务暂时不可用',
};
//# sourceMappingURL=response.interface.js.map