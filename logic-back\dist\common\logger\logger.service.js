"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LoggerService = void 0;
const common_1 = require("@nestjs/common");
const winston_1 = require("winston");
const nest_winston_1 = require("nest-winston");
const common_2 = require("@nestjs/common");
let LoggerService = class LoggerService {
    logger;
    constructor(logger) {
        this.logger = logger;
    }
    log(message, context) {
        this.logger.info(message, { context });
    }
    error(message, trace, context) {
        this.logger.error(message, { context, trace });
    }
    warn(message, context) {
        this.logger.warn(message, { context });
    }
    debug(message, context) {
        this.logger.debug(message, { context });
    }
    verbose(message, context) {
        this.logger.verbose(message, { context });
    }
    logHttpRequest(req, res, responseTime) {
        const logData = {
            method: req.method,
            url: req.url,
            statusCode: res.statusCode,
            responseTime: `${responseTime}ms`,
            userAgent: req.get('User-Agent'),
            ip: req.ip || req.connection.remoteAddress,
            timestamp: new Date().toISOString()
        };
        this.logger.info('HTTP Request', {
            context: 'HTTP',
            ...logData
        });
    }
    logDatabase(operation, table, data, error) {
        const logData = {
            operation,
            table,
            data: data ? JSON.stringify(data) : undefined,
            error: error ? error.message : undefined,
            timestamp: new Date().toISOString()
        };
        if (error) {
            this.logger.error(`Database ${operation} failed on ${table}`, {
                context: 'Database',
                ...logData,
                trace: error.stack
            });
        }
        else {
            this.logger.info(`Database ${operation} on ${table}`, {
                context: 'Database',
                ...logData
            });
        }
    }
    logPayment(action, data, error) {
        const logData = {
            action,
            data: JSON.stringify(data),
            error: error ? error.message : undefined,
            timestamp: new Date().toISOString()
        };
        if (error) {
            this.logger.error(`Payment ${action} failed`, {
                context: 'Payment',
                ...logData,
                trace: error.stack
            });
        }
        else {
            this.logger.info(`Payment ${action}`, {
                context: 'Payment',
                ...logData
            });
        }
    }
    logAuth(action, userId, details, error) {
        const logData = {
            action,
            userId,
            details: details ? JSON.stringify(details) : undefined,
            error: error ? error.message : undefined,
            timestamp: new Date().toISOString()
        };
        if (error) {
            this.logger.error(`Auth ${action} failed`, {
                context: 'Auth',
                ...logData,
                trace: error.stack
            });
        }
        else {
            this.logger.info(`Auth ${action}`, {
                context: 'Auth',
                ...logData
            });
        }
    }
    logBusiness(module, action, data, error) {
        const logData = {
            module,
            action,
            data: data ? JSON.stringify(data) : undefined,
            error: error ? error.message : undefined,
            timestamp: new Date().toISOString()
        };
        if (error) {
            this.logger.error(`Business ${module}.${action} failed`, {
                context: 'Business',
                ...logData,
                trace: error.stack
            });
        }
        else {
            this.logger.info(`Business ${module}.${action}`, {
                context: 'Business',
                ...logData
            });
        }
    }
    logStartup(message, details) {
        this.logger.info(message, {
            context: 'Startup',
            ...details,
            timestamp: new Date().toISOString()
        });
    }
    logPerformance(operation, duration, details) {
        this.logger.info(`Performance: ${operation} took ${duration}ms`, {
            context: 'Performance',
            operation,
            duration,
            ...details,
            timestamp: new Date().toISOString()
        });
    }
    logSecurity(event, details, severity = 'medium') {
        const logData = {
            event,
            severity,
            details: JSON.stringify(details),
            timestamp: new Date().toISOString()
        };
        if (severity === 'high') {
            this.logger.error(`Security Alert: ${event}`, {
                context: 'Security',
                ...logData
            });
        }
        else {
            this.logger.warn(`Security Event: ${event}`, {
                context: 'Security',
                ...logData
            });
        }
    }
    logApiResponse(data) {
        const logData = {
            requestId: data.requestId,
            method: data.method,
            path: data.path,
            statusCode: data.statusCode,
            success: data.success,
            message: data.message,
            executionTime: data.executionTime ? `${data.executionTime}ms` : undefined,
            userId: data.userId,
            ip: data.ip,
            userAgent: data.userAgent,
            module: data.module,
            operation: data.operation,
            timestamp: new Date().toISOString()
        };
        if (data.success) {
            this.logger.info(`API Response: ${data.method} ${data.path} - ${data.message}`, {
                context: 'ApiResponse',
                ...logData,
                requestBody: data.requestBody ? this.sanitizeData(data.requestBody) : undefined,
                responseData: data.responseData ? this.sanitizeData(data.responseData) : undefined
            });
        }
        else {
            this.logger.error(`API Error: ${data.method} ${data.path} - ${data.message}`, {
                context: 'ApiResponse',
                ...logData,
                errors: data.errors,
                errorType: data.errorType,
                requestBody: data.requestBody ? this.sanitizeData(data.requestBody) : undefined
            });
        }
    }
    logResponseProcessing(data) {
        const logData = {
            requestId: data.requestId,
            stage: data.stage,
            action: data.action,
            duration: data.duration ? `${data.duration}ms` : undefined,
            details: data.details ? this.sanitizeData(data.details) : undefined,
            timestamp: new Date().toISOString()
        };
        if (data.error) {
            this.logger.error(`Response Processing Error: ${data.stage}.${data.action}`, {
                context: 'ResponseProcessing',
                ...logData,
                error: data.error.message,
                trace: data.error.stack
            });
        }
        else {
            this.logger.debug(`Response Processing: ${data.stage}.${data.action}`, {
                context: 'ResponseProcessing',
                ...logData
            });
        }
    }
    logResponseTransform(data) {
        const logData = {
            requestId: data.requestId,
            fromFormat: data.fromFormat,
            toFormat: data.toFormat,
            duration: data.duration ? `${data.duration}ms` : undefined,
            timestamp: new Date().toISOString()
        };
        this.logger.debug(`Response Transform: ${data.fromFormat} -> ${data.toFormat}`, {
            context: 'ResponseTransform',
            ...logData,
            originalData: data.originalData ? this.sanitizeData(data.originalData) : undefined,
            transformedData: data.transformedData ? this.sanitizeData(data.transformedData) : undefined
        });
    }
    logResponseCache(data) {
        const logData = {
            requestId: data.requestId,
            action: data.action,
            cacheKey: data.cacheKey,
            ttl: data.ttl,
            size: data.size,
            timestamp: new Date().toISOString()
        };
        this.logger.debug(`Response Cache: ${data.action}`, {
            context: 'ResponseCache',
            ...logData
        });
    }
    sanitizeData(data) {
        if (!data || typeof data !== 'object') {
            return data;
        }
        const sensitiveFields = [
            'password', 'token', 'secret', 'key', 'authorization',
            'credit_card', 'ssn', 'phone', 'email', 'id_card'
        ];
        const sanitized = JSON.parse(JSON.stringify(data));
        const sanitizeObject = (obj) => {
            if (Array.isArray(obj)) {
                return obj.map(item => sanitizeObject(item));
            }
            if (obj && typeof obj === 'object') {
                const result = {};
                for (const [key, value] of Object.entries(obj)) {
                    const lowerKey = key.toLowerCase();
                    if (sensitiveFields.some(field => lowerKey.includes(field))) {
                        result[key] = '***REDACTED***';
                    }
                    else {
                        result[key] = sanitizeObject(value);
                    }
                }
                return result;
            }
            return obj;
        };
        return sanitizeObject(sanitized);
    }
};
exports.LoggerService = LoggerService;
exports.LoggerService = LoggerService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, common_2.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER)),
    __metadata("design:paramtypes", [winston_1.Logger])
], LoggerService);
//# sourceMappingURL=logger.service.js.map