export interface BaseResponse<T = any> {
    code: number;
    message: string;
    data?: T;
    timestamp: Date;
    requestId?: string;
}
export interface SuccessResponse<T = any> extends BaseResponse<T> {
    success: true;
    executionTime?: number;
    cache?: {
        hit: boolean;
        key?: string;
        ttl?: number;
    };
}
export interface ErrorResponse<T = any> extends BaseResponse<T> {
    success: false;
    errors?: string[];
    stack?: string;
    errorCode?: string;
    errorType?: 'VALIDATION' | 'BUSINESS' | 'SYSTEM' | 'NETWORK' | 'AUTH';
}
export interface PaginatedResponse<T = any> extends SuccessResponse<T[]> {
    pagination: {
        page: number;
        size: number;
        total: number;
        totalPages: number;
        hasNext: boolean;
        hasPrev: boolean;
    };
}
export declare enum ResponseFormat {
    STANDARD = "standard",
    SIMPLE = "simple",
    RESTFUL = "restful",
    CUSTOM = "custom"
}
export interface ResponseConfig {
    format?: ResponseFormat;
    includeExecutionTime?: boolean;
    includeRequestId?: boolean;
    includeCacheInfo?: boolean;
    fieldMapping?: {
        code?: string;
        message?: string;
        data?: string;
        timestamp?: string;
    };
}
export interface RequestContext {
    requestId: string;
    startTime: number;
    userId?: string | number;
    ip?: string;
    userAgent?: string;
    path: string;
    method: string;
    module?: string;
    operation?: string;
    metadata?: Record<string, any>;
}
export interface IResponseManager {
    success<T>(data?: T, message?: string, config?: ResponseConfig): SuccessResponse<T>;
    error<T>(message: string, errors?: string[], config?: ResponseConfig): ErrorResponse<T>;
    paginated<T>(data: T[], pagination: {
        page: number;
        size: number;
        total: number;
    }, message?: string, config?: ResponseConfig): PaginatedResponse<T>;
    custom<T>(code: number, message: string, data?: T, config?: ResponseConfig): BaseResponse<T>;
    setContext(context: Partial<RequestContext>): void;
    getContext(): RequestContext | null;
    format<T>(response: BaseResponse<T>, format?: ResponseFormat): any;
}
export declare const StatusCodes: {
    readonly SUCCESS: 200;
    readonly CREATED: 201;
    readonly ACCEPTED: 202;
    readonly NO_CONTENT: 204;
    readonly BAD_REQUEST: 400;
    readonly UNAUTHORIZED: 401;
    readonly FORBIDDEN: 403;
    readonly NOT_FOUND: 404;
    readonly METHOD_NOT_ALLOWED: 405;
    readonly CONFLICT: 409;
    readonly VALIDATION_ERROR: 422;
    readonly INTERNAL_SERVER_ERROR: 500;
    readonly NOT_IMPLEMENTED: 501;
    readonly BAD_GATEWAY: 502;
    readonly SERVICE_UNAVAILABLE: 503;
    readonly GATEWAY_TIMEOUT: 504;
};
export declare const DefaultMessages: {
    readonly SUCCESS: "操作成功";
    readonly CREATED: "创建成功";
    readonly UPDATED: "更新成功";
    readonly DELETED: "删除成功";
    readonly NOT_FOUND: "资源不存在";
    readonly UNAUTHORIZED: "未授权访问";
    readonly FORBIDDEN: "权限不足";
    readonly BAD_REQUEST: "请求参数错误";
    readonly VALIDATION_ERROR: "数据验证失败";
    readonly INTERNAL_ERROR: "系统内部错误";
    readonly SERVICE_UNAVAILABLE: "服务暂时不可用";
};
