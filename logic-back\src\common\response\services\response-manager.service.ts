import { Injectable, Scope } from '@nestjs/common';
import { v4 as uuidv4 } from 'uuid';
import { LoggerService } from '../../logger/logger.service';
import { ResponseLoggingUtils } from '../config/response-logging.config';
import {
  IResponseManager,
  BaseResponse,
  SuccessResponse,
  ErrorResponse,
  PaginatedResponse,
  ResponseConfig,
  RequestContext,
  ResponseFormat,
  StatusCodes,
  DefaultMessages,
} from '../interfaces/response.interface';

/**
 * 通用响应管理器服务
 * 提供统一的响应格式化和管理功能
 */
@Injectable({ scope: Scope.REQUEST })
export class ResponseManagerService implements IResponseManager {
  private context: RequestContext | null = null;

  constructor(private readonly loggerService: LoggerService) {}

  /**
   * 创建成功响应
   */
  success<T>(
    data?: T,
    message: string = DefaultMessages.SUCCESS,
    config?: ResponseConfig
  ): SuccessResponse<T> {
    const startTime = Date.now();

    const response: SuccessResponse<T> = {
      success: true,
      code: StatusCodes.SUCCESS,
      message,
      data,
      timestamp: new Date(),
    };

    // 添加执行时间
    if (config?.includeExecutionTime !== false && this.context) {
      response.executionTime = Date.now() - this.context.startTime;
    }

    // 添加请求ID
    if (config?.includeRequestId !== false && this.context) {
      response.requestId = this.context.requestId;
    }

    // 记录成功响应日志
    this.logResponse(response, data, Date.now() - startTime);

    return response;
  }

  /**
   * 创建错误响应
   */
  error<T>(
    message: string = DefaultMessages.INTERNAL_ERROR,
    errors?: string[],
    config?: ResponseConfig
  ): ErrorResponse<T> {
    const startTime = Date.now();

    const response: ErrorResponse<T> = {
      success: false,
      code: StatusCodes.INTERNAL_SERVER_ERROR,
      message,
      timestamp: new Date(),
    };

    if (errors && errors.length > 0) {
      response.errors = errors;
    }

    // 添加请求ID
    if (config?.includeRequestId !== false && this.context) {
      response.requestId = this.context.requestId;
    }

    // 记录错误响应日志
    this.logResponse(response, undefined, Date.now() - startTime);

    return response;
  }

  /**
   * 创建分页响应
   */
  paginated<T>(
    data: T[],
    pagination: {
      page: number;
      size: number;
      total: number;
    },
    message: string = DefaultMessages.SUCCESS,
    config?: ResponseConfig
  ): PaginatedResponse<T> {
    const startTime = Date.now();
    const totalPages = Math.ceil(pagination.total / pagination.size);
    const hasNext = pagination.page < totalPages;
    const hasPrev = pagination.page > 1;

    const response: PaginatedResponse<T> = {
      success: true,
      code: StatusCodes.SUCCESS,
      message,
      data,
      timestamp: new Date(),
      pagination: {
        ...pagination,
        totalPages,
        hasNext,
        hasPrev,
      },
    };

    // 添加执行时间
    if (config?.includeExecutionTime !== false && this.context) {
      response.executionTime = Date.now() - this.context.startTime;
    }

    // 添加请求ID
    if (config?.includeRequestId !== false && this.context) {
      response.requestId = this.context.requestId;
    }

    // 记录分页响应日志
    this.logResponse(response, { dataCount: data.length, pagination }, Date.now() - startTime);

    return response;
  }

  /**
   * 创建自定义响应
   */
  custom<T>(
    code: number,
    message: string,
    data?: T,
    config?: ResponseConfig
  ): BaseResponse<T> {
    const response: BaseResponse<T> = {
      code,
      message,
      data,
      timestamp: new Date(),
    };

    // 添加请求ID
    if (config?.includeRequestId !== false && this.context) {
      response.requestId = this.context.requestId;
    }

    return response;
  }

  /**
   * 设置请求上下文
   */
  setContext(context: Partial<RequestContext>): void {
    this.context = {
      requestId: context.requestId || uuidv4(),
      startTime: context.startTime || Date.now(),
      path: context.path || '',
      method: context.method || '',
      ...context,
    };
  }

  /**
   * 获取请求上下文
   */
  getContext(): RequestContext | null {
    return this.context;
  }

  /**
   * 格式化响应
   */
  format<T>(response: BaseResponse<T>, format: ResponseFormat = ResponseFormat.STANDARD): any {
    const startTime = Date.now();
    let transformedResponse: any;

    switch (format) {
      case ResponseFormat.SIMPLE:
        transformedResponse = this.formatSimple(response);
        break;
      case ResponseFormat.RESTFUL:
        transformedResponse = this.formatRestful(response);
        break;
      case ResponseFormat.STANDARD:
      default:
        transformedResponse = this.formatStandard(response);
        break;
    }

    // 记录格式转换日志
    if (format !== ResponseFormat.STANDARD) {
      this.logResponseTransform(response, transformedResponse, format, Date.now() - startTime);
    }

    return transformedResponse;
  }

  /**
   * 标准格式化
   */
  private formatStandard<T>(response: BaseResponse<T>): any {
    return response;
  }

  /**
   * 简化格式化
   */
  private formatSimple<T>(response: BaseResponse<T>): any {
    return {
      code: response.code,
      msg: response.message,
      data: response.data,
    };
  }

  /**
   * RESTful格式化
   */
  private formatRestful<T>(response: BaseResponse<T>): any {
    if ('success' in response && response.success) {
      return response.data;
    } else {
      return {
        error: {
          code: response.code,
          message: response.message,
          timestamp: response.timestamp,
        },
      };
    }
  }

  /**
   * 创建验证错误响应
   */
  validationError(errors: string[], message: string = DefaultMessages.VALIDATION_ERROR): ErrorResponse {
    return {
      success: false,
      code: StatusCodes.VALIDATION_ERROR,
      message,
      errors,
      errorType: 'VALIDATION',
      timestamp: new Date(),
      requestId: this.context?.requestId,
    };
  }

  /**
   * 创建业务错误响应
   */
  businessError(message: string, errorCode?: string): ErrorResponse {
    return {
      success: false,
      code: StatusCodes.BAD_REQUEST,
      message,
      errorCode,
      errorType: 'BUSINESS',
      timestamp: new Date(),
      requestId: this.context?.requestId,
    };
  }

  /**
   * 创建认证错误响应
   */
  authError(message: string = DefaultMessages.UNAUTHORIZED): ErrorResponse {
    return {
      success: false,
      code: StatusCodes.UNAUTHORIZED,
      message,
      errorType: 'AUTH',
      timestamp: new Date(),
      requestId: this.context?.requestId,
    };
  }

  /**
   * 创建权限错误响应
   */
  forbiddenError(message: string = DefaultMessages.FORBIDDEN): ErrorResponse {
    return {
      success: false,
      code: StatusCodes.FORBIDDEN,
      message,
      errorType: 'AUTH',
      timestamp: new Date(),
      requestId: this.context?.requestId,
    };
  }

  /**
   * 创建资源不存在错误响应
   */
  notFoundError(message: string = DefaultMessages.NOT_FOUND): ErrorResponse {
    return {
      success: false,
      code: StatusCodes.NOT_FOUND,
      message,
      errorType: 'BUSINESS',
      timestamp: new Date(),
      requestId: this.context?.requestId,
    };
  }

  /**
   * 创建系统错误响应
   */
  systemError(message: string = DefaultMessages.INTERNAL_ERROR, stack?: string): ErrorResponse {
    const startTime = Date.now();

    const response: ErrorResponse = {
      success: false,
      code: StatusCodes.INTERNAL_SERVER_ERROR,
      message,
      errorType: 'SYSTEM',
      timestamp: new Date(),
      requestId: this.context?.requestId,
    };

    // 仅在开发环境添加堆栈信息
    if (process.env.NODE_ENV === 'development' && stack) {
      response.stack = stack;
    }

    // 记录系统错误日志
    this.logResponse(response, undefined, Date.now() - startTime);

    return response;
  }

  /**
   * 记录响应日志
   */
  private logResponse<T>(response: BaseResponse<T>, originalData?: any, processingTime?: number): void {
    if (!this.context) {
      return;
    }

    // 检查是否应该记录日志
    if (!ResponseLoggingUtils.shouldLog(this.context.path, this.context.userAgent)) {
      return;
    }

    try {
      // 类型安全的属性检查
      const isSuccessResponse = this.isSuccessResponse(response);
      const isErrorResponse = this.isErrorResponse(response);

      // 记录API响应日志
      this.loggerService.logApiResponse({
        requestId: this.context.requestId,
        method: this.context.method,
        path: this.context.path,
        statusCode: response.code,
        success: isSuccessResponse ? response.success : response.code < 400,
        message: response.message,
        executionTime: isSuccessResponse ? response.executionTime : undefined,
        userId: this.context.userId,
        ip: this.context.ip,
        userAgent: this.context.userAgent,
        module: this.context.module,
        operation: this.context.operation,
        responseData: ResponseLoggingUtils.sanitizeData(response.data),
        errors: isErrorResponse ? response.errors : undefined,
        errorType: isErrorResponse ? response.errorType : undefined,
      });

      // 记录响应处理过程日志（如果启用）
      const config = ResponseLoggingUtils.getConfig();
      if (config.logProcessingSteps) {
        this.loggerService.logResponseProcessing({
          requestId: this.context.requestId,
          stage: 'manager',
          action: 'create_response',
          duration: processingTime,
          details: {
            responseType: isSuccessResponse ? (response.success ? 'success' : 'error') : 'custom',
            hasData: !!response.data,
            messageLength: response.message?.length || 0,
            originalDataType: originalData ? typeof originalData : 'none',
          }
        });
      }

      // 记录性能日志（如果执行时间较长）
      const totalExecutionTime = isSuccessResponse && typeof response.executionTime === 'number'
        ? response.executionTime
        : (this.context.startTime ? Date.now() - this.context.startTime : undefined);

      if (totalExecutionTime && typeof totalExecutionTime === 'number' && config.performanceThreshold && totalExecutionTime >= config.performanceThreshold) {
        this.loggerService.logPerformance(
          `${this.context.method} ${this.context.path}`,
          totalExecutionTime,
          {
            requestId: this.context.requestId,
            userId: this.context.userId,
            responseCode: response.code,
            hasError: isSuccessResponse ? !response.success : response.code >= 400,
            processingTime,
            responseSize: JSON.stringify(response).length
          }
        );
      }

    } catch (error) {
      // 日志记录失败不应该影响正常响应
      this.loggerService.error('Failed to log response', error.stack, 'ResponseManager');
    }
  }

  /**
   * 类型守卫：检查是否为成功响应
   */
  private isSuccessResponse<T>(response: BaseResponse<T>): response is SuccessResponse<T> {
    return 'success' in response && 'executionTime' in response;
  }

  /**
   * 类型守卫：检查是否为错误响应
   */
  private isErrorResponse<T>(response: BaseResponse<T>): response is ErrorResponse<T> {
    return 'success' in response && 'errors' in response && 'errorType' in response;
  }

  /**
   * 记录响应格式转换日志
   */
  private logResponseTransform<T>(
    originalResponse: BaseResponse<T>,
    transformedResponse: any,
    format: ResponseFormat,
    processingTime?: number
  ): void {
    if (!this.context) {
      return;
    }

    // 检查是否应该记录转换日志
    const config = ResponseLoggingUtils.getConfig();
    if (!config.logTransformations) {
      return;
    }

    try {
      this.loggerService.logResponseTransform({
        requestId: this.context.requestId,
        fromFormat: 'standard',
        toFormat: format,
        originalData: ResponseLoggingUtils.sanitizeData(originalResponse),
        transformedData: ResponseLoggingUtils.sanitizeData(transformedResponse),
        duration: processingTime
      });
    } catch (error) {
      this.loggerService.error('Failed to log response transform', error.stack, 'ResponseManager');
    }
  }

  /**
   * 记录响应缓存日志
   */
  logResponseCache(action: 'hit' | 'miss' | 'set' | 'invalidate', cacheKey?: string, ttl?: number): void {
    if (!this.context) {
      return;
    }

    // 检查是否应该记录缓存日志
    const config = ResponseLoggingUtils.getConfig();
    if (!config.logCacheOperations) {
      return;
    }

    try {
      this.loggerService.logResponseCache({
        requestId: this.context.requestId,
        action,
        cacheKey,
        ttl
      });
    } catch (error) {
      this.loggerService.error('Failed to log response cache', error.stack, 'ResponseManager');
    }
  }
}
