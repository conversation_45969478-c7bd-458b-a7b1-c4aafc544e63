import { Injectable, Scope } from '@nestjs/common';
import { v4 as uuidv4 } from 'uuid';
import {
  IResponseManager,
  BaseResponse,
  SuccessResponse,
  ErrorResponse,
  PaginatedResponse,
  ResponseConfig,
  RequestContext,
  ResponseFormat,
  StatusCodes,
  DefaultMessages,
} from '../interfaces/response.interface';

/**
 * 通用响应管理器服务
 * 提供统一的响应格式化和管理功能
 */
@Injectable({ scope: Scope.REQUEST })
export class ResponseManagerService implements IResponseManager {
  private context: RequestContext | null = null;

  /**
   * 创建成功响应
   */
  success<T>(
    data?: T,
    message: string = DefaultMessages.SUCCESS,
    config?: ResponseConfig
  ): SuccessResponse<T> {
    const response: SuccessResponse<T> = {
      success: true,
      code: StatusCodes.SUCCESS,
      message,
      data,
      timestamp: new Date(),
    };

    // 添加执行时间
    if (config?.includeExecutionTime !== false && this.context) {
      response.executionTime = Date.now() - this.context.startTime;
    }

    // 添加请求ID
    if (config?.includeRequestId !== false && this.context) {
      response.requestId = this.context.requestId;
    }

    return response;
  }

  /**
   * 创建错误响应
   */
  error<T>(
    message: string = DefaultMessages.INTERNAL_ERROR,
    errors?: string[],
    config?: ResponseConfig
  ): ErrorResponse<T> {
    const response: ErrorResponse<T> = {
      success: false,
      code: StatusCodes.INTERNAL_SERVER_ERROR,
      message,
      timestamp: new Date(),
    };

    if (errors && errors.length > 0) {
      response.errors = errors;
    }

    // 添加请求ID
    if (config?.includeRequestId !== false && this.context) {
      response.requestId = this.context.requestId;
    }

    return response;
  }

  /**
   * 创建分页响应
   */
  paginated<T>(
    data: T[],
    pagination: {
      page: number;
      size: number;
      total: number;
    },
    message: string = DefaultMessages.SUCCESS,
    config?: ResponseConfig
  ): PaginatedResponse<T> {
    const totalPages = Math.ceil(pagination.total / pagination.size);
    const hasNext = pagination.page < totalPages;
    const hasPrev = pagination.page > 1;

    const response: PaginatedResponse<T> = {
      success: true,
      code: StatusCodes.SUCCESS,
      message,
      data,
      timestamp: new Date(),
      pagination: {
        ...pagination,
        totalPages,
        hasNext,
        hasPrev,
      },
    };

    // 添加执行时间
    if (config?.includeExecutionTime !== false && this.context) {
      response.executionTime = Date.now() - this.context.startTime;
    }

    // 添加请求ID
    if (config?.includeRequestId !== false && this.context) {
      response.requestId = this.context.requestId;
    }

    return response;
  }

  /**
   * 创建自定义响应
   */
  custom<T>(
    code: number,
    message: string,
    data?: T,
    config?: ResponseConfig
  ): BaseResponse<T> {
    const response: BaseResponse<T> = {
      code,
      message,
      data,
      timestamp: new Date(),
    };

    // 添加请求ID
    if (config?.includeRequestId !== false && this.context) {
      response.requestId = this.context.requestId;
    }

    return response;
  }

  /**
   * 设置请求上下文
   */
  setContext(context: Partial<RequestContext>): void {
    this.context = {
      requestId: context.requestId || uuidv4(),
      startTime: context.startTime || Date.now(),
      path: context.path || '',
      method: context.method || '',
      ...context,
    };
  }

  /**
   * 获取请求上下文
   */
  getContext(): RequestContext | null {
    return this.context;
  }

  /**
   * 格式化响应
   */
  format<T>(response: BaseResponse<T>, format: ResponseFormat = ResponseFormat.STANDARD): any {
    switch (format) {
      case ResponseFormat.SIMPLE:
        return this.formatSimple(response);
      case ResponseFormat.RESTFUL:
        return this.formatRestful(response);
      case ResponseFormat.STANDARD:
      default:
        return this.formatStandard(response);
    }
  }

  /**
   * 标准格式化
   */
  private formatStandard<T>(response: BaseResponse<T>): any {
    return response;
  }

  /**
   * 简化格式化
   */
  private formatSimple<T>(response: BaseResponse<T>): any {
    return {
      code: response.code,
      msg: response.message,
      data: response.data,
    };
  }

  /**
   * RESTful格式化
   */
  private formatRestful<T>(response: BaseResponse<T>): any {
    if ('success' in response && response.success) {
      return response.data;
    } else {
      return {
        error: {
          code: response.code,
          message: response.message,
          timestamp: response.timestamp,
        },
      };
    }
  }

  /**
   * 创建验证错误响应
   */
  validationError(errors: string[], message: string = DefaultMessages.VALIDATION_ERROR): ErrorResponse {
    return {
      success: false,
      code: StatusCodes.VALIDATION_ERROR,
      message,
      errors,
      errorType: 'VALIDATION',
      timestamp: new Date(),
      requestId: this.context?.requestId,
    };
  }

  /**
   * 创建业务错误响应
   */
  businessError(message: string, errorCode?: string): ErrorResponse {
    return {
      success: false,
      code: StatusCodes.BAD_REQUEST,
      message,
      errorCode,
      errorType: 'BUSINESS',
      timestamp: new Date(),
      requestId: this.context?.requestId,
    };
  }

  /**
   * 创建认证错误响应
   */
  authError(message: string = DefaultMessages.UNAUTHORIZED): ErrorResponse {
    return {
      success: false,
      code: StatusCodes.UNAUTHORIZED,
      message,
      errorType: 'AUTH',
      timestamp: new Date(),
      requestId: this.context?.requestId,
    };
  }

  /**
   * 创建权限错误响应
   */
  forbiddenError(message: string = DefaultMessages.FORBIDDEN): ErrorResponse {
    return {
      success: false,
      code: StatusCodes.FORBIDDEN,
      message,
      errorType: 'AUTH',
      timestamp: new Date(),
      requestId: this.context?.requestId,
    };
  }

  /**
   * 创建资源不存在错误响应
   */
  notFoundError(message: string = DefaultMessages.NOT_FOUND): ErrorResponse {
    return {
      success: false,
      code: StatusCodes.NOT_FOUND,
      message,
      errorType: 'BUSINESS',
      timestamp: new Date(),
      requestId: this.context?.requestId,
    };
  }

  /**
   * 创建系统错误响应
   */
  systemError(message: string = DefaultMessages.INTERNAL_ERROR, stack?: string): ErrorResponse {
    const response: ErrorResponse = {
      success: false,
      code: StatusCodes.INTERNAL_SERVER_ERROR,
      message,
      errorType: 'SYSTEM',
      timestamp: new Date(),
      requestId: this.context?.requestId,
    };

    // 仅在开发环境添加堆栈信息
    if (process.env.NODE_ENV === 'development' && stack) {
      response.stack = stack;
    }

    return response;
  }
}
