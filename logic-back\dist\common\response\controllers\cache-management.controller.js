"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CacheManagementController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const response_cache_service_1 = require("../services/response-cache.service");
const logger_service_1 = require("../../logger/logger.service");
const response_decorator_1 = require("../decorators/response.decorator");
let CacheManagementController = class CacheManagementController {
    cacheService;
    loggerService;
    constructor(cacheService, loggerService) {
        this.cacheService = cacheService;
        this.loggerService = loggerService;
    }
    async getCacheStats() {
        const stats = await this.cacheService.getStats();
        return {
            ...stats,
            timestamp: new Date().toISOString(),
            formattedSize: this.formatBytes(stats.totalSize),
        };
    }
    async checkCacheExists(method, path, userId) {
        const exists = await this.cacheService.exists(method, path, null, userId ? parseInt(userId) : undefined);
        let ttl = -1;
        if (exists) {
            ttl = await this.cacheService.getTtl(method, path, null, userId ? parseInt(userId) : undefined);
        }
        return {
            exists,
            ttl,
            expiresAt: ttl > 0 ? new Date(Date.now() + ttl * 1000).toISOString() : null,
        };
    }
    async getCacheContent(method, path, userId) {
        const content = await this.cacheService.get(method, path, null, userId ? parseInt(userId) : undefined);
        if (!content) {
            return {
                exists: false,
                content: null,
            };
        }
        return {
            exists: true,
            content,
            size: JSON.stringify(content).length,
            formattedSize: this.formatBytes(JSON.stringify(content).length),
        };
    }
    async deleteCacheItem(method, path, userId) {
        const deleted = await this.cacheService.delete(method, path, null, userId ? parseInt(userId) : undefined);
        return {
            deleted,
            message: deleted ? '缓存删除成功' : '缓存不存在或删除失败',
        };
    }
    async deleteCacheByPattern(pattern) {
        const deletedCount = await this.cacheService.deleteByPattern(pattern);
        return {
            deletedCount,
            pattern,
            message: `删除了 ${deletedCount} 个缓存项`,
        };
    }
    async clearAllCache() {
        const deletedCount = await this.cacheService.clear();
        this.loggerService.logBusiness('CacheManagement', 'clear_all_cache', {
            deletedCount,
            timestamp: new Date().toISOString(),
        });
        return {
            deletedCount,
            message: `清空了 ${deletedCount} 个缓存项`,
            timestamp: new Date().toISOString(),
        };
    }
    async warmupCache(warmupConfig) {
        const results = [];
        for (const endpoint of warmupConfig.endpoints) {
            try {
                results.push({
                    method: endpoint.method,
                    path: endpoint.path,
                    status: 'scheduled',
                    message: '预热请求已安排',
                });
            }
            catch (error) {
                results.push({
                    method: endpoint.method,
                    path: endpoint.path,
                    status: 'failed',
                    message: error.message || '未知错误',
                });
            }
        }
        this.loggerService.logBusiness('CacheManagement', 'warmup_cache', {
            endpointsCount: warmupConfig.endpoints.length,
            results,
            timestamp: new Date().toISOString(),
        });
        return {
            totalEndpoints: warmupConfig.endpoints.length,
            results,
            message: '缓存预热任务已启动',
        };
    }
    async getCacheHealth() {
        try {
            const stats = await this.cacheService.getStats();
            const isHealthy = stats.totalKeys >= 0;
            return {
                healthy: isHealthy,
                stats,
                timestamp: new Date().toISOString(),
                checks: {
                    connection: isHealthy,
                    keyCount: stats.totalKeys,
                    totalSize: stats.totalSize,
                    avgTtl: stats.avgTtl,
                },
            };
        }
        catch (error) {
            return {
                healthy: false,
                error: error.message,
                timestamp: new Date().toISOString(),
            };
        }
    }
    async getCacheConfig() {
        return {
            defaultTtl: 300,
            maxSize: 1024 * 1024,
            compressionEnabled: false,
            environment: process.env.NODE_ENV,
            redisConnected: true,
            timestamp: new Date().toISOString(),
        };
    }
    async getPopularCacheKeys(limit = '10') {
        const mockPopularKeys = [
            { key: 'GET:/api/users', hits: 1250, lastAccess: new Date() },
            { key: 'GET:/api/posts', hits: 980, lastAccess: new Date() },
            { key: 'GET:/api/categories', hits: 750, lastAccess: new Date() },
        ];
        return {
            popularKeys: mockPopularKeys.slice(0, parseInt(limit)),
            totalTracked: mockPopularKeys.length,
            timestamp: new Date().toISOString(),
        };
    }
    formatBytes(bytes) {
        if (bytes === 0)
            return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
};
exports.CacheManagementController = CacheManagementController;
__decorate([
    (0, common_1.Get)('stats'),
    (0, swagger_1.ApiOperation)({ summary: '获取缓存统计信息' }),
    (0, response_decorator_1.ApiSuccessResponse)('获取缓存统计成功'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], CacheManagementController.prototype, "getCacheStats", null);
__decorate([
    (0, common_1.Get)('exists'),
    (0, swagger_1.ApiOperation)({ summary: '检查缓存是否存在' }),
    (0, swagger_1.ApiQuery)({ name: 'method', description: 'HTTP方法' }),
    (0, swagger_1.ApiQuery)({ name: 'path', description: '请求路径' }),
    (0, swagger_1.ApiQuery)({ name: 'userId', description: '用户ID', required: false }),
    (0, response_decorator_1.ApiSuccessResponse)('检查缓存存在性成功'),
    __param(0, (0, common_1.Query)('method')),
    __param(1, (0, common_1.Query)('path')),
    __param(2, (0, common_1.Query)('userId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String]),
    __metadata("design:returntype", Promise)
], CacheManagementController.prototype, "checkCacheExists", null);
__decorate([
    (0, common_1.Get)('content'),
    (0, swagger_1.ApiOperation)({ summary: '获取缓存内容' }),
    (0, swagger_1.ApiQuery)({ name: 'method', description: 'HTTP方法' }),
    (0, swagger_1.ApiQuery)({ name: 'path', description: '请求路径' }),
    (0, swagger_1.ApiQuery)({ name: 'userId', description: '用户ID', required: false }),
    (0, response_decorator_1.ApiSuccessResponse)('获取缓存内容成功'),
    __param(0, (0, common_1.Query)('method')),
    __param(1, (0, common_1.Query)('path')),
    __param(2, (0, common_1.Query)('userId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String]),
    __metadata("design:returntype", Promise)
], CacheManagementController.prototype, "getCacheContent", null);
__decorate([
    (0, common_1.Delete)('item'),
    (0, swagger_1.ApiOperation)({ summary: '删除特定缓存' }),
    (0, swagger_1.ApiQuery)({ name: 'method', description: 'HTTP方法' }),
    (0, swagger_1.ApiQuery)({ name: 'path', description: '请求路径' }),
    (0, swagger_1.ApiQuery)({ name: 'userId', description: '用户ID', required: false }),
    (0, response_decorator_1.ApiSuccessResponse)('删除缓存成功'),
    __param(0, (0, common_1.Query)('method')),
    __param(1, (0, common_1.Query)('path')),
    __param(2, (0, common_1.Query)('userId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String]),
    __metadata("design:returntype", Promise)
], CacheManagementController.prototype, "deleteCacheItem", null);
__decorate([
    (0, common_1.Delete)('pattern/:pattern'),
    (0, swagger_1.ApiOperation)({ summary: '按模式删除缓存' }),
    (0, swagger_1.ApiParam)({ name: 'pattern', description: '匹配模式，如 GET:*/users/*' }),
    (0, response_decorator_1.ApiSuccessResponse)('按模式删除缓存成功'),
    __param(0, (0, common_1.Param)('pattern')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CacheManagementController.prototype, "deleteCacheByPattern", null);
__decorate([
    (0, common_1.Delete)('all'),
    (0, swagger_1.ApiOperation)({ summary: '清空所有缓存' }),
    (0, response_decorator_1.ApiSuccessResponse)('清空所有缓存成功'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], CacheManagementController.prototype, "clearAllCache", null);
__decorate([
    (0, common_1.Post)('warmup'),
    (0, swagger_1.ApiOperation)({ summary: '预热缓存' }),
    (0, response_decorator_1.ApiSuccessResponse)('缓存预热成功'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], CacheManagementController.prototype, "warmupCache", null);
__decorate([
    (0, common_1.Get)('health'),
    (0, swagger_1.ApiOperation)({ summary: '获取缓存健康状态' }),
    (0, response_decorator_1.ApiSuccessResponse)('获取缓存健康状态成功'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], CacheManagementController.prototype, "getCacheHealth", null);
__decorate([
    (0, common_1.Get)('config'),
    (0, swagger_1.ApiOperation)({ summary: '获取缓存配置信息' }),
    (0, response_decorator_1.ApiSuccessResponse)('获取缓存配置成功'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], CacheManagementController.prototype, "getCacheConfig", null);
__decorate([
    (0, common_1.Get)('popular'),
    (0, swagger_1.ApiOperation)({ summary: '获取热门缓存键' }),
    (0, swagger_1.ApiQuery)({ name: 'limit', description: '返回数量限制', required: false }),
    (0, response_decorator_1.ApiSuccessResponse)('获取热门缓存键成功'),
    __param(0, (0, common_1.Query)('limit')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CacheManagementController.prototype, "getPopularCacheKeys", null);
exports.CacheManagementController = CacheManagementController = __decorate([
    (0, swagger_1.ApiTags)('缓存管理'),
    (0, common_1.Controller)('cache-management'),
    __metadata("design:paramtypes", [response_cache_service_1.ResponseCacheService,
        logger_service_1.LoggerService])
], CacheManagementController);
//# sourceMappingURL=cache-management.controller.js.map