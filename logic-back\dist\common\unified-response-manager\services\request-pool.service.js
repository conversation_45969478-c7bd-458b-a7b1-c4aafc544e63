"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RequestPoolService = void 0;
const common_1 = require("@nestjs/common");
const redis_service_1 = require("../../../util/database/redis/redis.service");
const logger_service_1 = require("../../logger/logger.service");
const performance_monitor_service_1 = require("./performance-monitor.service");
let RequestPoolService = class RequestPoolService {
    redisService;
    logger;
    performanceMonitor;
    config;
    processingTasks = new Map();
    completedTasks = new Map();
    maxHistorySize = 1000;
    isProcessing = false;
    processingInterval = null;
    constructor(redisService, logger, performanceMonitor, config) {
        this.redisService = redisService;
        this.logger = logger;
        this.performanceMonitor = performanceMonitor;
        this.config = config;
        this.startProcessing();
    }
    async onModuleDestroy() {
        await this.stop();
    }
    async enqueue(task) {
        if (!this.config.enableRequestPool) {
            return this.executeTask(task);
        }
        if (this.processingTasks.size >= this.config.maxConcurrency) {
            await this.addToQueue(task);
            return this.waitForCompletion(task.requestId, task.timeout);
        }
        else {
            return this.processTask(task);
        }
    }
    async getStatus() {
        const queueSize = await this.getQueueSize();
        const processingCount = this.processingTasks.size;
        const { averageWaitTime, averageProcessingTime, successRate } = this.calculateAverages();
        return {
            queueSize,
            processingCount,
            maxConcurrency: this.config.maxConcurrency,
            averageWaitTime,
            averageProcessingTime,
            successRate
        };
    }
    async cleanup() {
        const now = Date.now();
        const expiredThreshold = now - (this.config.requestTimeout * 2);
        let cleanedCompleted = 0;
        for (const [requestId, { task }] of this.completedTasks.entries()) {
            if (task.completedAt && task.completedAt < expiredThreshold) {
                this.completedTasks.delete(requestId);
                cleanedCompleted++;
            }
        }
        let cleanedProcessing = 0;
        for (const [requestId, task] of this.processingTasks.entries()) {
            if (task.createdAt < expiredThreshold) {
                this.processingTasks.delete(requestId);
                cleanedProcessing++;
            }
        }
        const cleanedQueue = await this.cleanupExpiredQueueTasks();
        if (this.completedTasks.size > this.maxHistorySize) {
            const sortedTasks = Array.from(this.completedTasks.entries())
                .sort(([, a], [, b]) => (b.task.completedAt || 0) - (a.task.completedAt || 0));
            this.completedTasks.clear();
            sortedTasks.slice(0, this.maxHistorySize).forEach(([id, taskData]) => {
                this.completedTasks.set(id, taskData);
            });
        }
        if (cleanedCompleted > 0 || cleanedProcessing > 0 || cleanedQueue > 0) {
            this.logger.debug(`请求池清理完成 - 已完成: ${cleanedCompleted}, 处理中: ${cleanedProcessing}, 队列: ${cleanedQueue}`, 'RequestPool');
        }
    }
    async stop() {
        this.isProcessing = false;
        if (this.processingInterval) {
            clearInterval(this.processingInterval);
            this.processingInterval = null;
        }
        const maxWaitTime = 30000;
        const startTime = Date.now();
        while (this.processingTasks.size > 0 && Date.now() - startTime < maxWaitTime) {
            await new Promise(resolve => setTimeout(resolve, 100));
        }
        this.logger.log('请求池已停止', 'RequestPool');
    }
    async executeTask(task) {
        const startTime = Date.now();
        task.startedAt = startTime;
        try {
            const result = await Promise.race([
                task.handler(),
                new Promise((_, reject) => setTimeout(() => reject(new Error('任务执行超时')), task.timeout))
            ]);
            task.completedAt = Date.now();
            return result;
        }
        catch (error) {
            task.completedAt = Date.now();
            throw error;
        }
    }
    async processTask(task) {
        this.processingTasks.set(task.requestId, task);
        try {
            const result = await this.executeTask(task);
            this.completedTasks.set(task.requestId, { task, result });
            return result;
        }
        catch (error) {
            this.completedTasks.set(task.requestId, { task, result: null, error });
            throw error;
        }
        finally {
            this.processingTasks.delete(task.requestId);
        }
    }
    async addToQueue(task) {
        const queueKey = `${this.config.redisKeyPrefix}:queue`;
        const taskData = {
            requestId: task.requestId,
            priority: task.priority,
            timeout: task.timeout,
            createdAt: task.createdAt,
            context: task.context
        };
        const score = task.priority * 1000000 + task.createdAt;
        await this.redisService.getClient().zadd(queueKey, score, JSON.stringify(taskData));
    }
    async getNextTask() {
        const queueKey = `${this.config.redisKeyPrefix}:queue`;
        const tasks = await this.redisService.getClient().zrange(queueKey, 0, 0);
        if (tasks.length === 0) {
            return null;
        }
        await this.redisService.getClient().zrem(queueKey, tasks[0]);
        return JSON.parse(tasks[0]);
    }
    async getQueueSize() {
        const queueKey = `${this.config.redisKeyPrefix}:queue`;
        return await this.redisService.getClient().zcard(queueKey);
    }
    async waitForCompletion(requestId, timeout) {
        const startTime = Date.now();
        return new Promise((resolve, reject) => {
            const checkCompletion = () => {
                const completed = this.completedTasks.get(requestId);
                if (completed) {
                    if (completed.error) {
                        reject(completed.error);
                    }
                    else {
                        resolve(completed.result);
                    }
                    return;
                }
                if (Date.now() - startTime > timeout) {
                    reject(new Error(`任务超时: ${requestId}`));
                    return;
                }
                setTimeout(checkCompletion, 100);
            };
            checkCompletion();
        });
    }
    startProcessing() {
        this.isProcessing = true;
        this.processingInterval = setInterval(async () => {
            if (!this.isProcessing)
                return;
            try {
                while (this.processingTasks.size < this.config.maxConcurrency) {
                    const taskData = await this.getNextTask();
                    if (!taskData)
                        break;
                    this.logger.warn(`从队列恢复任务 ${taskData.requestId}，但handler已丢失`, 'RequestPool');
                }
            }
            catch (error) {
                this.logger.error(`处理队列任务时出错: ${error.message}`, 'RequestPool');
            }
        }, 1000);
    }
    async cleanupExpiredQueueTasks() {
        const queueKey = `${this.config.redisKeyPrefix}:queue`;
        const now = Date.now();
        const expiredThreshold = now - (this.config.requestTimeout * 2);
        const tasks = await this.redisService.getClient().zrange(queueKey, 0, -1);
        let cleanedCount = 0;
        for (const taskStr of tasks) {
            try {
                const taskData = JSON.parse(taskStr);
                if (taskData.createdAt < expiredThreshold) {
                    await this.redisService.getClient().zrem(queueKey, taskStr);
                    cleanedCount++;
                }
            }
            catch (error) {
                await this.redisService.getClient().zrem(queueKey, taskStr);
                cleanedCount++;
            }
        }
        return cleanedCount;
    }
    calculateAverages() {
        const recentTasks = Array.from(this.completedTasks.values())
            .filter(({ task }) => task.completedAt && task.completedAt > Date.now() - 300000)
            .slice(-100);
        if (recentTasks.length === 0) {
            return { averageWaitTime: 0, averageProcessingTime: 0, successRate: 100 };
        }
        const waitTimes = recentTasks
            .filter(({ task }) => task.startedAt)
            .map(({ task }) => task.startedAt - task.createdAt);
        const processingTimes = recentTasks
            .filter(({ task }) => task.startedAt && task.completedAt)
            .map(({ task }) => task.completedAt - task.startedAt);
        const successCount = recentTasks.filter(({ error }) => !error).length;
        const averageWaitTime = waitTimes.length > 0
            ? waitTimes.reduce((sum, time) => sum + time, 0) / waitTimes.length
            : 0;
        const averageProcessingTime = processingTimes.length > 0
            ? processingTimes.reduce((sum, time) => sum + time, 0) / processingTimes.length
            : 0;
        const successRate = (successCount / recentTasks.length) * 100;
        return { averageWaitTime, averageProcessingTime, successRate };
    }
};
exports.RequestPoolService = RequestPoolService;
exports.RequestPoolService = RequestPoolService = __decorate([
    (0, common_1.Injectable)(),
    __param(3, (0, common_1.Inject)('UNIFIED_RESPONSE_CONFIG')),
    __metadata("design:paramtypes", [redis_service_1.RedisService,
        logger_service_1.LoggerService,
        performance_monitor_service_1.PerformanceMonitorService, Object])
], RequestPoolService);
//# sourceMappingURL=request-pool.service.js.map