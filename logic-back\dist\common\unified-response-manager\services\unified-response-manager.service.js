"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UnifiedResponseManagerService = void 0;
const common_1 = require("@nestjs/common");
const http_response_interface_1 = require("../../../web/http_response_result/http-response.interface");
const logger_service_1 = require("../../logger/logger.service");
let UnifiedResponseManagerService = class UnifiedResponseManagerService {
    logger;
    requestPool;
    performanceMonitor;
    typeDetector;
    config;
    constructor(logger, requestPool, performanceMonitor, typeDetector, config) {
        this.logger = logger;
        this.requestPool = requestPool;
        this.performanceMonitor = performanceMonitor;
        this.typeDetector = typeDetector;
        this.config = config;
    }
    async handleResponse(result, context, options = {}) {
        const fullContext = this.enrichContext(context);
        this.performanceMonitor.startMonitoring(fullContext.requestId);
        try {
            let processedResult;
            const usePool = options.usePool ?? this.config.enableRequestPool;
            if (usePool) {
                processedResult = await this.processWithPool(result, fullContext, options);
            }
            else {
                processedResult = await this.processDirectly(result, fullContext);
            }
            const unifiedResponse = this.createUnifiedResponse(processedResult, fullContext, true);
            this.logResponse(unifiedResponse, 'info');
            return this.toHttpResponse(unifiedResponse);
        }
        catch (error) {
            return this.handleError(error, context);
        }
    }
    async handleError(error, context) {
        const fullContext = this.enrichContext(context);
        const errorInfo = this.typeDetector.extractErrorInfo(error);
        const unifiedResponse = {
            context: fullContext,
            data: undefined,
            success: false,
            message: errorInfo.message,
            errors: errorInfo.errors,
            code: errorInfo.code,
            metrics: this.performanceMonitor.endMonitoring(fullContext.requestId),
            timestamp: new Date(),
            originalType: 'error'
        };
        this.logResponse(unifiedResponse, 'error');
        return this.toHttpResponse(unifiedResponse);
    }
    createContext(request, controller, action) {
        const requestId = this.generateRequestId();
        const context = {
            requestId,
            userId: request.user?.id || request.userId,
            clientIp: request.ip || request.connection?.remoteAddress,
            userAgent: request.headers?.['user-agent'],
            path: request.path || request.url,
            method: request.method,
            controller,
            action,
            startTime: Date.now(),
            metadata: {
                headers: this.sanitizeHeaders(request.headers),
                query: request.query,
                params: request.params
            }
        };
        return context;
    }
    async getPoolStatus() {
        return this.requestPool.getStatus();
    }
    logResponse(unifiedResponse, level = 'info') {
        const { context, success, message, errors, metrics, originalType } = unifiedResponse;
        const logData = {
            requestId: context.requestId,
            controller: context.controller,
            action: context.action,
            userId: context.userId,
            success,
            message,
            errors,
            originalType,
            executionTime: metrics.executionTime,
            memoryUsage: metrics.memoryUsage,
            dbQueryCount: metrics.dbQueryCount,
            cacheHits: metrics.cacheHits,
            cacheMisses: metrics.cacheMisses,
            queueWaitTime: metrics.queueWaitTime,
            path: context.path,
            method: context.method,
            clientIp: context.clientIp
        };
        const logMessage = `[${context.requestId}] ${context.controller}.${context.action} - ${message || (success ? '成功' : '失败')} (${originalType})`;
        switch (level) {
            case 'info':
                this.logger.log(logMessage, JSON.stringify(logData));
                break;
            case 'warn':
                this.logger.warn(logMessage, JSON.stringify(logData));
                break;
            case 'error':
                this.logger.error(logMessage, JSON.stringify(logData));
                break;
        }
        this.logger.logBusiness(context.controller || 'Unknown', context.action || 'Unknown', logData, success ? undefined : new Error(errors?.join(', ') || message));
    }
    async processWithPool(result, context, options) {
        const task = {
            requestId: context.requestId,
            handler: async () => this.processDirectly(result, context),
            priority: options.priority || 0,
            timeout: options.timeout || this.config.requestTimeout,
            createdAt: Date.now(),
            context
        };
        const queueStartTime = Date.now();
        const processedResult = await this.requestPool.enqueue(task);
        const queueWaitTime = Date.now() - queueStartTime;
        this.performanceMonitor.recordQueueWaitTime(context.requestId, queueWaitTime);
        return processedResult;
    }
    async processDirectly(result, _context) {
        const originalType = this.typeDetector.detectType(result);
        switch (originalType) {
            case 'http-response':
                return this.typeDetector.normalizeData(result);
            case 'ddd-result':
                return result;
            case 'error':
                throw result;
            default:
                return this.typeDetector.normalizeData(result);
        }
    }
    createUnifiedResponse(processedResult, context, success) {
        const metrics = this.performanceMonitor.endMonitoring(context.requestId);
        const originalType = this.typeDetector.detectType(processedResult);
        if (originalType === 'ddd-result') {
            return {
                context,
                data: processedResult.data,
                success: processedResult.success,
                message: processedResult.message || (processedResult.success ? '操作成功' : '操作失败'),
                errors: processedResult.errors,
                code: processedResult.success ? http_response_interface_1.SUCCESS_CODE : http_response_interface_1.ERROR_CODE,
                metrics: {
                    ...metrics,
                    executionTime: processedResult.executionTime || metrics.executionTime
                },
                timestamp: processedResult.timestamp || new Date(),
                originalType
            };
        }
        const responseSuccess = this.typeDetector.getSuccessStatus(processedResult);
        const responseMessage = this.typeDetector.getResponseMessage(processedResult);
        return {
            context,
            data: processedResult,
            success: responseSuccess,
            message: responseMessage,
            errors: responseSuccess ? undefined : ['处理失败'],
            code: responseSuccess ? http_response_interface_1.SUCCESS_CODE : http_response_interface_1.ERROR_CODE,
            metrics,
            timestamp: new Date(),
            originalType
        };
    }
    toHttpResponse(unifiedResponse) {
        return {
            code: unifiedResponse.code,
            msg: unifiedResponse.message || (unifiedResponse.success ? '操作成功' : '操作失败'),
            data: unifiedResponse.data
        };
    }
    generateRequestId() {
        const timestamp = Date.now();
        const random = Math.floor(Math.random() * 1000);
        return `req_${timestamp}_${random}`;
    }
    enrichContext(partialContext) {
        const requestId = partialContext.requestId || this.generateRequestId();
        return {
            requestId,
            userId: partialContext.userId,
            clientIp: partialContext.clientIp,
            userAgent: partialContext.userAgent,
            path: partialContext.path,
            method: partialContext.method,
            controller: partialContext.controller || 'Unknown',
            action: partialContext.action || 'Unknown',
            startTime: partialContext.startTime || Date.now(),
            metadata: partialContext.metadata || {}
        };
    }
    sanitizeHeaders(headers) {
        if (!headers)
            return {};
        const sanitized = { ...headers };
        delete sanitized.authorization;
        delete sanitized.cookie;
        delete sanitized['x-api-key'];
        return sanitized;
    }
};
exports.UnifiedResponseManagerService = UnifiedResponseManagerService;
exports.UnifiedResponseManagerService = UnifiedResponseManagerService = __decorate([
    (0, common_1.Injectable)(),
    __param(1, (0, common_1.Inject)('REQUEST_POOL')),
    __param(2, (0, common_1.Inject)('PERFORMANCE_MONITOR')),
    __param(3, (0, common_1.Inject)('RESPONSE_TYPE_DETECTOR')),
    __param(4, (0, common_1.Inject)('UNIFIED_RESPONSE_CONFIG')),
    __metadata("design:paramtypes", [logger_service_1.LoggerService, Object, Object, Object, Object])
], UnifiedResponseManagerService);
//# sourceMappingURL=unified-response-manager.service.js.map