import {
  ExceptionFilter,
  Catch,
  ArgumentsHost,
  HttpException,
  HttpStatus,
  Injectable,
  Logger,
} from '@nestjs/common';
import { Request, Response } from 'express';
import { ResponseManagerService } from '../services/response-manager.service';
import { ErrorResponse, StatusCodes } from '../interfaces/response.interface';

/**
 * 业务异常类
 */
export class BusinessException extends Error {
  constructor(
    message: string,
    public readonly errorCode?: string,
    public readonly statusCode: number = StatusCodes.BAD_REQUEST,
  ) {
    super(message);
    this.name = 'BusinessException';
  }
}

/**
 * 验证异常类
 */
export class ValidationException extends Error {
  constructor(
    message: string,
    public readonly errors: string[] = [],
    public readonly statusCode: number = StatusCodes.VALIDATION_ERROR,
  ) {
    super(message);
    this.name = 'ValidationException';
  }
}

/**
 * 响应异常过滤器
 * 统一处理所有异常并返回标准格式的错误响应
 */
@Catch()
@Injectable()
export class ResponseExceptionFilter implements ExceptionFilter {
  private readonly logger = new Logger(ResponseExceptionFilter.name);

  constructor(private readonly responseManager: ResponseManagerService) {}

  catch(exception: unknown, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();

    // 设置请求上下文（如果还没有设置）
    if (!this.responseManager.getContext()) {
      this.responseManager.setContext({
        requestId: this.generateRequestId(),
        startTime: Date.now(),
        path: request.path,
        method: request.method,
        ip: request.ip,
        userAgent: request.get('User-Agent'),
        userId: (request as any).user?.id,
      });
    }

    let errorResponse: ErrorResponse;
    let httpStatus: number;

    if (exception instanceof BusinessException) {
      // 业务异常
      errorResponse = this.responseManager.businessError(
        exception.message,
        exception.errorCode,
      );
      httpStatus = exception.statusCode;
    } else if (exception instanceof ValidationException) {
      // 验证异常
      errorResponse = this.responseManager.validationError(
        exception.errors,
        exception.message,
      );
      httpStatus = exception.statusCode;
    } else if (exception instanceof HttpException) {
      // HTTP异常
      httpStatus = exception.getStatus();
      const exceptionResponse = exception.getResponse();
      
      let message = exception.message;
      let errors: string[] = [];

      if (typeof exceptionResponse === 'object') {
        const responseObj = exceptionResponse as any;
        message = responseObj.message || exception.message;
        
        if (Array.isArray(responseObj.message)) {
          errors = responseObj.message;
          message = '请求参数验证失败';
        }
      }

      errorResponse = this.createHttpErrorResponse(httpStatus, message, errors);
    } else if (exception instanceof Error) {
      // 普通错误
      errorResponse = this.responseManager.systemError(
        exception.message,
        exception.stack,
      );
      httpStatus = StatusCodes.INTERNAL_SERVER_ERROR;
    } else {
      // 未知异常
      errorResponse = this.responseManager.systemError(
        '未知系统错误',
      );
      httpStatus = StatusCodes.INTERNAL_SERVER_ERROR;
    }

    // 记录错误日志
    this.logError(exception, request, errorResponse);

    // 返回错误响应
    response.status(httpStatus).json(errorResponse);
  }

  /**
   * 创建HTTP错误响应
   */
  private createHttpErrorResponse(
    status: number,
    message: string,
    errors: string[] = [],
  ): ErrorResponse {
    switch (status) {
      case HttpStatus.BAD_REQUEST:
        return errors.length > 0
          ? this.responseManager.validationError(errors, message)
          : this.responseManager.businessError(message);
      
      case HttpStatus.UNAUTHORIZED:
        return this.responseManager.authError(message);
      
      case HttpStatus.FORBIDDEN:
        return this.responseManager.forbiddenError(message);
      
      case HttpStatus.NOT_FOUND:
        return this.responseManager.notFoundError(message);
      
      default:
        return this.responseManager.systemError(message);
    }
  }

  /**
   * 记录错误日志
   */
  private logError(
    exception: unknown,
    request: Request,
    errorResponse: ErrorResponse,
  ) {
    const context = this.responseManager.getContext();
    
    const logData = {
      requestId: context?.requestId,
      method: request.method,
      url: request.url,
      ip: request.ip,
      userAgent: request.get('User-Agent'),
      userId: (request as any).user?.id,
      errorCode: errorResponse.code,
      errorMessage: errorResponse.message,
      errorType: errorResponse.errorType,
      timestamp: new Date().toISOString(),
    };

    if (exception instanceof Error) {
      this.logger.error(
        `${request.method} ${request.url} - ${exception.message}`,
        {
          ...logData,
          stack: exception.stack,
        },
      );
    } else {
      this.logger.error(
        `${request.method} ${request.url} - Unknown error`,
        logData,
      );
    }

    // 在开发环境下打印详细错误信息
    if (process.env.NODE_ENV === 'development') {
      console.error('Exception Details:', {
        exception,
        request: {
          method: request.method,
          url: request.url,
          headers: request.headers,
          body: request.body,
        },
        response: errorResponse,
      });
    }
  }

  /**
   * 生成请求ID
   */
  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

/**
 * HTTP异常过滤器
 * 专门处理HTTP异常
 */
@Catch(HttpException)
@Injectable()
export class HttpExceptionFilter implements ExceptionFilter {
  private readonly logger = new Logger(HttpExceptionFilter.name);

  constructor(private readonly responseManager: ResponseManagerService) {}

  catch(exception: HttpException, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();

    const status = exception.getStatus();
    const exceptionResponse = exception.getResponse();

    let message = exception.message;
    let errors: string[] = [];

    if (typeof exceptionResponse === 'object') {
      const responseObj = exceptionResponse as any;
      message = responseObj.message || exception.message;
      
      if (Array.isArray(responseObj.message)) {
        errors = responseObj.message;
        message = '请求参数验证失败';
      }
    }

    // 设置请求上下文（如果还没有设置）
    if (!this.responseManager.getContext()) {
      this.responseManager.setContext({
        requestId: this.generateRequestId(),
        startTime: Date.now(),
        path: request.path,
        method: request.method,
        ip: request.ip,
        userAgent: request.get('User-Agent'),
        userId: (request as any).user?.id,
      });
    }

    let errorResponse: ErrorResponse;

    switch (status) {
      case HttpStatus.BAD_REQUEST:
        errorResponse = errors.length > 0
          ? this.responseManager.validationError(errors, message)
          : this.responseManager.businessError(message);
        break;
      
      case HttpStatus.UNAUTHORIZED:
        errorResponse = this.responseManager.authError(message);
        break;
      
      case HttpStatus.FORBIDDEN:
        errorResponse = this.responseManager.forbiddenError(message);
        break;
      
      case HttpStatus.NOT_FOUND:
        errorResponse = this.responseManager.notFoundError(message);
        break;
      
      default:
        errorResponse = this.responseManager.systemError(message);
        break;
    }

    // 记录日志
    this.logger.warn(
      `HTTP Exception: ${request.method} ${request.url} - ${status} ${message}`,
      {
        requestId: this.responseManager.getContext()?.requestId,
        status,
        message,
        errors,
      },
    );

    response.status(status).json(errorResponse);
  }

  /**
   * 生成请求ID
   */
  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}
