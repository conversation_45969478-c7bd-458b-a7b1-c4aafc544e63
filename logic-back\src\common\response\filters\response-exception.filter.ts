import {
  ExceptionFilter,
  Catch,
  ArgumentsHost,
  HttpException,
  HttpStatus,
  Injectable,
  Logger,
} from '@nestjs/common';
import { Request, Response } from 'express';
import { LoggerService } from '../../logger/logger.service';
import { ResponseManagerService } from '../services/response-manager.service';
import { ErrorResponse, StatusCodes } from '../interfaces/response.interface';

/**
 * 业务异常类
 */
export class BusinessException extends Error {
  constructor(
    message: string,
    public readonly errorCode?: string,
    public readonly statusCode: number = StatusCodes.BAD_REQUEST,
  ) {
    super(message);
    this.name = 'BusinessException';
  }
}

/**
 * 验证异常类
 */
export class ValidationException extends Error {
  constructor(
    message: string,
    public readonly errors: string[] = [],
    public readonly statusCode: number = StatusCodes.VALIDATION_ERROR,
  ) {
    super(message);
    this.name = 'ValidationException';
  }
}

/**
 * 响应异常过滤器
 * 统一处理所有异常并返回标准格式的错误响应
 */
@Catch()
@Injectable()
export class ResponseExceptionFilter implements ExceptionFilter {
  private readonly logger = new Logger(ResponseExceptionFilter.name);

  constructor(
    private readonly responseManager: ResponseManagerService,
    private readonly loggerService: LoggerService,
  ) {}

  catch(exception: unknown, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();
    const startTime = Date.now();

    // 设置请求上下文（如果还没有设置）
    let requestId: string;
    if (!this.responseManager.getContext()) {
      requestId = this.generateRequestId();
      this.responseManager.setContext({
        requestId,
        startTime: Date.now(),
        path: request.path,
        method: request.method,
        ip: request.ip,
        userAgent: request.get('User-Agent'),
        userId: (request as any).user?.id,
      });
    } else {
      requestId = this.responseManager.getContext()?.requestId || this.generateRequestId();
    }

    // 记录异常过滤器开始处理
    this.loggerService.logResponseProcessing({
      requestId,
      stage: 'filter',
      action: 'start_exception_handling',
      details: {
        exceptionType: exception?.constructor?.name || 'Unknown',
        path: request.path,
        method: request.method,
      }
    });

    let errorResponse: ErrorResponse;
    let httpStatus: number;
    let exceptionType: string;

    try {
      if (exception instanceof BusinessException) {
        // 业务异常
        exceptionType = 'BusinessException';
        errorResponse = this.responseManager.businessError(
          exception.message,
          exception.errorCode,
        );
        httpStatus = exception.statusCode;

        this.loggerService.logResponseProcessing({
          requestId,
          stage: 'filter',
          action: 'handle_business_exception',
          details: {
            errorCode: exception.errorCode,
            statusCode: exception.statusCode,
            message: exception.message
          }
        });
      } else if (exception instanceof ValidationException) {
        // 验证异常
        exceptionType = 'ValidationException';
        errorResponse = this.responseManager.validationError(
          exception.errors,
          exception.message,
        );
        httpStatus = exception.statusCode;

        this.loggerService.logResponseProcessing({
          requestId,
          stage: 'filter',
          action: 'handle_validation_exception',
          details: {
            errorsCount: exception.errors?.length || 0,
            statusCode: exception.statusCode,
            message: exception.message
          }
        });
      } else if (exception instanceof HttpException) {
        // HTTP异常
        exceptionType = 'HttpException';
        httpStatus = exception.getStatus();
        const exceptionResponse = exception.getResponse();

        let message = exception.message;
        let errors: string[] = [];

        if (typeof exceptionResponse === 'object') {
          const responseObj = exceptionResponse as any;
          message = responseObj.message || exception.message;

          if (Array.isArray(responseObj.message)) {
            errors = responseObj.message;
            message = '请求参数验证失败';
          }
        }

        errorResponse = this.createHttpErrorResponse(httpStatus, message, errors);

        this.loggerService.logResponseProcessing({
          requestId,
          stage: 'filter',
          action: 'handle_http_exception',
          details: {
            httpStatus,
            message,
            errorsCount: errors.length,
            hasNestedResponse: typeof exceptionResponse === 'object'
          }
        });
      } else if (exception instanceof Error) {
        // 普通错误
        exceptionType = 'Error';
        errorResponse = this.responseManager.systemError(
          exception.message,
          exception.stack,
        );
        httpStatus = StatusCodes.INTERNAL_SERVER_ERROR;

        this.loggerService.logResponseProcessing({
          requestId,
          stage: 'filter',
          action: 'handle_system_error',
          details: {
            errorName: exception.name,
            message: exception.message,
            hasStack: !!exception.stack
          }
        });
      } else {
        // 未知异常
        exceptionType = 'Unknown';
        errorResponse = this.responseManager.systemError(
          '未知系统错误',
        );
        httpStatus = StatusCodes.INTERNAL_SERVER_ERROR;

        this.loggerService.logResponseProcessing({
          requestId,
          stage: 'filter',
          action: 'handle_unknown_exception',
          details: {
            exceptionType: typeof exception,
            exceptionValue: String(exception)
          }
        });
      }

      // 记录详细的错误日志
      this.logError(exception, request, errorResponse, exceptionType);

      // 记录异常过滤器完成处理
      this.loggerService.logResponseProcessing({
        requestId,
        stage: 'filter',
        action: 'complete_exception_handling',
        duration: Date.now() - startTime,
        details: {
          finalHttpStatus: httpStatus,
          errorResponseCode: errorResponse.code,
          exceptionType
        }
      });

      // 返回错误响应
      response.status(httpStatus).json(errorResponse);

    } catch (filterError) {
      // 异常过滤器本身出错的情况
      this.loggerService.logResponseProcessing({
        requestId,
        stage: 'filter',
        action: 'filter_processing_error',
        duration: Date.now() - startTime,
        error: filterError
      });

      // 返回最基本的错误响应
      response.status(500).json({
        success: false,
        code: 500,
        message: '系统内部错误',
        timestamp: new Date(),
        requestId
      });
    }
  }

  /**
   * 创建HTTP错误响应
   */
  private createHttpErrorResponse(
    status: number,
    message: string,
    errors: string[] = [],
  ): ErrorResponse {
    switch (status) {
      case HttpStatus.BAD_REQUEST:
        return errors.length > 0
          ? this.responseManager.validationError(errors, message)
          : this.responseManager.businessError(message);
      
      case HttpStatus.UNAUTHORIZED:
        return this.responseManager.authError(message);
      
      case HttpStatus.FORBIDDEN:
        return this.responseManager.forbiddenError(message);
      
      case HttpStatus.NOT_FOUND:
        return this.responseManager.notFoundError(message);
      
      default:
        return this.responseManager.systemError(message);
    }
  }

  /**
   * 记录错误日志
   */
  private logError(
    exception: unknown,
    request: Request,
    errorResponse: ErrorResponse,
    exceptionType: string,
  ) {
    const context = this.responseManager.getContext();

    // 使用新的日志服务记录API响应日志
    this.loggerService.logApiResponse({
      requestId: context?.requestId,
      method: request.method,
      path: request.path,
      statusCode: errorResponse.code,
      success: false,
      message: errorResponse.message,
      userId: (request as any).user?.id,
      ip: request.ip,
      userAgent: request.get('User-Agent'),
      requestBody: this.sanitizeRequestBody(request.body),
      errors: errorResponse.errors,
      errorType: errorResponse.errorType,
    });

    // 记录详细的异常信息
    const logData = {
      requestId: context?.requestId,
      method: request.method,
      url: request.url,
      ip: request.ip,
      userAgent: request.get('User-Agent'),
      userId: (request as any).user?.id,
      errorCode: errorResponse.code,
      errorMessage: errorResponse.message,
      errorType: errorResponse.errorType,
      exceptionType,
      timestamp: new Date().toISOString(),
    };

    if (exception instanceof Error) {
      // 使用新的日志服务记录业务日志
      this.loggerService.logBusiness(
        'ExceptionFilter',
        'handle_exception',
        {
          ...logData,
          exceptionName: exception.name,
          exceptionMessage: exception.message,
        },
        exception
      );

      // 保留原有的日志记录方式作为备份
      this.logger.error(
        `${request.method} ${request.url} - ${exception.message}`,
        {
          ...logData,
          stack: exception.stack,
        },
      );
    } else {
      // 使用新的日志服务记录业务日志
      this.loggerService.logBusiness(
        'ExceptionFilter',
        'handle_unknown_exception',
        {
          ...logData,
          exceptionValue: String(exception),
        }
      );

      this.logger.error(
        `${request.method} ${request.url} - Unknown error`,
        logData,
      );
    }

    // 记录安全相关的异常
    if (errorResponse.code === 401 || errorResponse.code === 403) {
      this.loggerService.logSecurity(
        'unauthorized_access_attempt',
        {
          requestId: context?.requestId,
          method: request.method,
          path: request.path,
          ip: request.ip,
          userAgent: request.get('User-Agent'),
          userId: (request as any).user?.id,
          errorCode: errorResponse.code,
          errorMessage: errorResponse.message,
        },
        errorResponse.code === 403 ? 'high' : 'medium'
      );
    }

    // 在开发环境下打印详细错误信息
    if (process.env.NODE_ENV === 'development') {
      console.error('Exception Details:', {
        exception,
        request: {
          method: request.method,
          url: request.url,
          headers: this.sanitizeHeaders(request.headers),
          body: this.sanitizeRequestBody(request.body),
        },
        response: errorResponse,
        context: context,
      });
    }
  }

  /**
   * 清理请求体中的敏感信息
   */
  private sanitizeRequestBody(body: any): any {
    if (!body || typeof body !== 'object') {
      return body;
    }

    const sensitiveFields = ['password', 'token', 'secret', 'key', 'authorization'];
    const sanitized = { ...body };

    for (const field of sensitiveFields) {
      if (field in sanitized) {
        sanitized[field] = '***REDACTED***';
      }
    }

    return sanitized;
  }

  /**
   * 清理请求头中的敏感信息
   */
  private sanitizeHeaders(headers: any): any {
    if (!headers || typeof headers !== 'object') {
      return headers;
    }

    const sensitiveHeaders = ['authorization', 'cookie', 'x-api-key', 'x-auth-token'];
    const sanitized = { ...headers };

    for (const header of sensitiveHeaders) {
      if (header in sanitized) {
        sanitized[header] = '***REDACTED***';
      }
    }

    return sanitized;
  }

  /**
   * 生成请求ID
   */
  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

/**
 * HTTP异常过滤器
 * 专门处理HTTP异常
 */
@Catch(HttpException)
@Injectable()
export class HttpExceptionFilter implements ExceptionFilter {
  private readonly logger = new Logger(HttpExceptionFilter.name);

  constructor(private readonly responseManager: ResponseManagerService) {}

  catch(exception: HttpException, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();

    const status = exception.getStatus();
    const exceptionResponse = exception.getResponse();

    let message = exception.message;
    let errors: string[] = [];

    if (typeof exceptionResponse === 'object') {
      const responseObj = exceptionResponse as any;
      message = responseObj.message || exception.message;
      
      if (Array.isArray(responseObj.message)) {
        errors = responseObj.message;
        message = '请求参数验证失败';
      }
    }

    // 设置请求上下文（如果还没有设置）
    if (!this.responseManager.getContext()) {
      this.responseManager.setContext({
        requestId: this.generateRequestId(),
        startTime: Date.now(),
        path: request.path,
        method: request.method,
        ip: request.ip,
        userAgent: request.get('User-Agent'),
        userId: (request as any).user?.id,
      });
    }

    let errorResponse: ErrorResponse;

    switch (status) {
      case HttpStatus.BAD_REQUEST:
        errorResponse = errors.length > 0
          ? this.responseManager.validationError(errors, message)
          : this.responseManager.businessError(message);
        break;
      
      case HttpStatus.UNAUTHORIZED:
        errorResponse = this.responseManager.authError(message);
        break;
      
      case HttpStatus.FORBIDDEN:
        errorResponse = this.responseManager.forbiddenError(message);
        break;
      
      case HttpStatus.NOT_FOUND:
        errorResponse = this.responseManager.notFoundError(message);
        break;
      
      default:
        errorResponse = this.responseManager.systemError(message);
        break;
    }

    // 记录日志
    this.logger.warn(
      `HTTP Exception: ${request.method} ${request.url} - ${status} ${message}`,
      {
        requestId: this.responseManager.getContext()?.requestId,
        status,
        message,
        errors,
      },
    );

    response.status(status).json(errorResponse);
  }

  /**
   * 生成请求ID
   */
  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}
