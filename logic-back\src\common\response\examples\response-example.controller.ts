import {
  Controller,
  Get,
  Post,
  Body,
  Query,
  Param,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import { ApiTags, ApiOperation } from '@nestjs/swagger';
import { ResponseManagerService } from '../services/response-manager.service';
import { ResponseCacheService } from '../services/response-cache.service';
import {
  ResponseConfig,
  ApiSuccessResponse,
  ApiPaginatedResponse,
  ApiSimpleResponse,
  ApiRestfulResponse,
  ApiResponseDocs,
} from '../decorators/response.decorator';
import {
  ShortCache,
  MediumCache,
  LongCache,
  UserCache,
  ParamCache,
  FullCache,
  StaticCache,
  NoCache,
} from '../decorators/cache.decorator';
import {
  BusinessException,
  ValidationException,
} from '../filters/response-exception.filter';
import { ResponseFormat } from '../interfaces/response.interface';

/**
 * 响应管理器使用示例控制器
 */
@ApiTags('响应管理器示例')
@Controller('response-examples')
export class ResponseExampleController {
  constructor(
    private readonly responseManager: ResponseManagerService,
    private readonly cacheService: ResponseCacheService,
  ) {}

  /**
   * 基础成功响应示例
   */
  @Get('basic-success')
  @ApiOperation({ summary: '基础成功响应示例' })
  @ApiSuccessResponse('获取数据成功')
  async basicSuccess() {
    return {
      id: 1,
      name: '测试数据',
      createTime: new Date(),
    };
  }

  /**
   * 手动创建成功响应示例
   */
  @Get('manual-success')
  @ApiOperation({ summary: '手动创建成功响应示例' })
  async manualSuccess() {
    const data = {
      id: 1,
      name: '手动创建的数据',
      createTime: new Date(),
    };

    return this.responseManager.success(data, '手动创建成功响应');
  }

  /**
   * 分页响应示例
   */
  @Get('paginated')
  @ApiOperation({ summary: '分页响应示例' })
  @ApiPaginatedResponse('获取分页数据成功')
  async paginatedData(
    @Query('page') page: number = 1,
    @Query('size') size: number = 10,
  ) {
    // 模拟分页数据
    const mockData = Array.from({ length: size }, (_, index) => ({
      id: (page - 1) * size + index + 1,
      name: `数据项 ${(page - 1) * size + index + 1}`,
      createTime: new Date(),
    }));

    return {
      data: mockData,
      pagination: {
        page,
        size,
        total: 100,
      },
    };
  }

  /**
   * 手动创建分页响应示例
   */
  @Get('manual-paginated')
  @ApiOperation({ summary: '手动创建分页响应示例' })
  async manualPaginated(
    @Query('page') page: number = 1,
    @Query('size') size: number = 10,
  ) {
    const mockData = Array.from({ length: size }, (_, index) => ({
      id: (page - 1) * size + index + 1,
      name: `手动分页数据 ${(page - 1) * size + index + 1}`,
      createTime: new Date(),
    }));

    return this.responseManager.paginated(
      mockData,
      { page, size, total: 100 },
      '手动创建分页响应成功',
    );
  }

  /**
   * 简化响应格式示例
   */
  @Get('simple')
  @ApiOperation({ summary: '简化响应格式示例' })
  @ApiSimpleResponse('简化格式响应')
  async simpleFormat() {
    return {
      message: '这是简化格式的响应',
      value: 42,
    };
  }

  /**
   * RESTful响应格式示例
   */
  @Get('restful')
  @ApiOperation({ summary: 'RESTful响应格式示例' })
  @ApiRestfulResponse()
  async restfulFormat() {
    return {
      id: 1,
      name: 'RESTful格式数据',
      createTime: new Date(),
    };
  }

  /**
   * 自定义响应格式示例
   */
  @Get('custom')
  @ApiOperation({ summary: '自定义响应格式示例' })
  @ResponseConfig({
    format: ResponseFormat.CUSTOM,
    successMessage: '自定义响应成功',
    includeExecutionTime: true,
    includeRequestId: true,
  })
  async customFormat() {
    return {
      customField: '自定义字段',
      data: {
        id: 1,
        name: '自定义格式数据',
      },
    };
  }

  /**
   * 业务异常示例
   */
  @Get('business-error')
  @ApiOperation({ summary: '业务异常示例' })
  @ApiResponseDocs()
  async businessError() {
    throw new BusinessException('这是一个业务异常示例', 'BUSINESS_ERROR_001');
  }

  /**
   * 验证异常示例
   */
  @Post('validation-error')
  @ApiOperation({ summary: '验证异常示例' })
  @ApiResponseDocs()
  async validationError(@Body() body: any) {
    const errors: string[] = [];

    if (!body.name) {
      errors.push('姓名不能为空');
    }

    if (!body.email) {
      errors.push('邮箱不能为空');
    } else if (!/\S+@\S+\.\S+/.test(body.email)) {
      errors.push('邮箱格式不正确');
    }

    if (errors.length > 0) {
      throw new ValidationException('数据验证失败', errors);
    }

    return this.responseManager.success(body, '数据验证通过');
  }

  /**
   * HTTP异常示例
   */
  @Get('http-error/:id')
  @ApiOperation({ summary: 'HTTP异常示例' })
  @ApiResponseDocs()
  async httpError(@Param('id') id: string) {
    if (id === 'not-found') {
      throw new HttpException('资源不存在', HttpStatus.NOT_FOUND);
    }
    
    if (id === 'forbidden') {
      throw new HttpException('权限不足', HttpStatus.FORBIDDEN);
    }
    
    if (id === 'unauthorized') {
      throw new HttpException('未授权访问', HttpStatus.UNAUTHORIZED);
    }

    return this.responseManager.success({ id }, '获取资源成功');
  }

  /**
   * 系统异常示例
   */
  @Get('system-error')
  @ApiOperation({ summary: '系统异常示例' })
  @ApiResponseDocs()
  async systemError() {
    throw new Error('这是一个系统异常示例');
  }

  /**
   * 空数据响应示例
   */
  @Get('null-data')
  @ApiOperation({ summary: '空数据响应示例' })
  @ApiSuccessResponse('操作成功，无返回数据')
  async nullData() {
    return null;
  }

  /**
   * 手动错误响应示例
   */
  @Get('manual-error')
  @ApiOperation({ summary: '手动错误响应示例' })
  async manualError() {
    return this.responseManager.businessError(
      '这是手动创建的业务错误',
      'MANUAL_ERROR_001',
    );
  }

  /**
   * 完整API文档示例
   */
  @Get('full-docs')
  @ApiOperation({ summary: '完整API文档示例' })
  @ApiResponseDocs({
    success: {
      message: '获取完整文档数据成功',
      example: {
        id: 1,
        title: '文档标题',
        content: '文档内容',
        createTime: new Date().toISOString(),
      },
    },
  })
  async fullDocs() {
    return {
      id: 1,
      title: '完整API文档示例',
      content: '这是一个包含完整API文档的示例接口',
      createTime: new Date(),
    };
  }

  /**
   * 日志记录示例
   */
  @Get('logging-example')
  @ApiOperation({ summary: '日志记录示例' })
  @ApiSuccessResponse('日志记录示例成功')
  async loggingExample() {
    // 模拟一些处理时间
    await new Promise(resolve => setTimeout(resolve, 100));

    // 记录缓存操作示例
    this.responseManager.logResponseCache('hit', 'user:123', 3600);

    // 返回数据
    return {
      id: 1,
      message: '这是一个日志记录示例',
      timestamp: new Date(),
      features: [
        '自动记录API响应日志',
        '记录请求处理过程',
        '性能监控',
        '错误追踪',
        '缓存操作记录',
        '敏感数据脱敏'
      ]
    };
  }

  /**
   * 性能测试示例（模拟慢查询）
   */
  @Get('performance-test')
  @ApiOperation({ summary: '性能测试示例' })
  @ApiSuccessResponse('性能测试完成')
  async performanceTest(@Query('delay') delay: number = 1500) {
    // 模拟慢查询
    await new Promise(resolve => setTimeout(resolve, delay));

    return {
      message: '性能测试完成',
      delay: `${delay}ms`,
      note: '这个接口会触发性能日志记录（超过阈值时）'
    };
  }

  /**
   * 敏感数据处理示例
   */
  @Post('sensitive-data')
  @ApiOperation({ summary: '敏感数据处理示例' })
  @ApiSuccessResponse('敏感数据处理成功')
  async sensitiveData(@Body() data: any) {
    // 模拟处理包含敏感信息的数据
    const processedData = {
      id: 1,
      username: data.username,
      email: data.email,
      // 这些字段在日志中会被脱敏
      password: data.password,
      token: 'jwt-token-example',
      credit_card: '1234-5678-9012-3456',
      phone: data.phone,
    };

    return {
      message: '敏感数据处理成功',
      note: '敏感字段在日志中已被自动脱敏',
      processedFields: Object.keys(processedData)
    };
  }

  // ==================== 缓存功能示例 ====================

  /**
   * 短期缓存示例（1分钟）
   */
  @Get('cache/short')
  @ApiOperation({ summary: '短期缓存示例' })
  @ShortCache(60)
  @ApiSuccessResponse('短期缓存示例成功')
  async shortCacheExample() {
    // 模拟一些处理时间
    await new Promise(resolve => setTimeout(resolve, 100));

    return {
      message: '这是短期缓存示例',
      timestamp: new Date(),
      cacheInfo: '缓存时间：60秒',
      data: {
        randomValue: Math.random(),
        processedAt: new Date().toISOString(),
      }
    };
  }

  /**
   * 中期缓存示例（5分钟）
   */
  @Get('cache/medium')
  @ApiOperation({ summary: '中期缓存示例' })
  @MediumCache(300)
  @ApiSuccessResponse('中期缓存示例成功')
  async mediumCacheExample() {
    await new Promise(resolve => setTimeout(resolve, 200));

    return {
      message: '这是中期缓存示例',
      timestamp: new Date(),
      cacheInfo: '缓存时间：5分钟',
      data: {
        expensiveCalculation: Math.pow(Math.random() * 1000, 2),
        processedAt: new Date().toISOString(),
      }
    };
  }

  /**
   * 长期缓存示例（1小时）
   */
  @Get('cache/long')
  @ApiOperation({ summary: '长期缓存示例' })
  @LongCache(3600)
  @ApiSuccessResponse('长期缓存示例成功')
  async longCacheExample() {
    await new Promise(resolve => setTimeout(resolve, 500));

    return {
      message: '这是长期缓存示例',
      timestamp: new Date(),
      cacheInfo: '缓存时间：1小时',
      data: {
        staticData: '这是相对静态的数据',
        processedAt: new Date().toISOString(),
      }
    };
  }

  /**
   * 用户相关缓存示例
   */
  @Get('cache/user')
  @ApiOperation({ summary: '用户相关缓存示例' })
  @UserCache(300)
  @ApiSuccessResponse('用户相关缓存示例成功')
  async userCacheExample() {
    return {
      message: '这是用户相关缓存示例',
      note: '缓存键包含用户ID，不同用户有独立缓存',
      timestamp: new Date(),
      userData: {
        preferences: '用户偏好设置',
        lastLogin: new Date().toISOString(),
      }
    };
  }

  /**
   * 参数相关缓存示例
   */
  @Get('cache/param')
  @ApiOperation({ summary: '参数相关缓存示例' })
  @ParamCache(300)
  @ApiSuccessResponse('参数相关缓存示例成功')
  async paramCacheExample(@Query('category') category: string = 'default') {
    await new Promise(resolve => setTimeout(resolve, 150));

    return {
      message: '这是参数相关缓存示例',
      note: '缓存键包含查询参数，不同参数有独立缓存',
      category,
      timestamp: new Date(),
      data: {
        categoryData: `${category}分类的数据`,
        processedAt: new Date().toISOString(),
      }
    };
  }

  /**
   * 完整缓存示例（用户+参数）
   */
  @Get('cache/full')
  @ApiOperation({ summary: '完整缓存示例' })
  @FullCache(300)
  @ApiSuccessResponse('完整缓存示例成功')
  async fullCacheExample(@Query('filter') filter: string = 'all') {
    await new Promise(resolve => setTimeout(resolve, 300));

    return {
      message: '这是完整缓存示例',
      note: '缓存键包含用户ID和查询参数',
      filter,
      timestamp: new Date(),
      data: {
        filteredData: `按${filter}过滤的数据`,
        processedAt: new Date().toISOString(),
      }
    };
  }

  /**
   * 静态数据缓存示例
   */
  @Get('cache/static')
  @ApiOperation({ summary: '静态数据缓存示例' })
  @StaticCache(86400) // 24小时
  @ApiSuccessResponse('静态数据缓存示例成功')
  async staticCacheExample() {
    return {
      message: '这是静态数据缓存示例',
      note: '适用于很少变化的配置数据',
      timestamp: new Date(),
      staticData: {
        appConfig: {
          version: '1.0.0',
          features: ['缓存', '日志', '响应管理'],
        },
        constants: {
          maxFileSize: '10MB',
          supportedFormats: ['jpg', 'png', 'pdf'],
        }
      }
    };
  }

  /**
   * 禁用缓存示例
   */
  @Get('cache/disabled')
  @ApiOperation({ summary: '禁用缓存示例' })
  @NoCache()
  @ApiSuccessResponse('禁用缓存示例成功')
  async disabledCacheExample() {
    return {
      message: '这是禁用缓存示例',
      note: '此接口明确禁用了缓存',
      timestamp: new Date(),
      realTimeData: {
        currentTime: new Date().toISOString(),
        randomValue: Math.random(),
      }
    };
  }

  /**
   * 手动缓存操作示例
   */
  @Get('cache/manual')
  @ApiOperation({ summary: '手动缓存操作示例' })
  @ApiSuccessResponse('手动缓存操作示例成功')
  async manualCacheExample(@Query('action') action: string = 'get') {
    switch (action) {
      case 'set':
        const data = {
          message: '手动设置的缓存数据',
          timestamp: new Date(),
          value: Math.random(),
        };

        // 创建标准响应格式用于缓存
        const response = this.responseManager.success(data, '手动缓存数据');
        await this.cacheService.set('GET', '/manual-cache', response, undefined, undefined, 300);

        return {
          action: 'set',
          message: '缓存数据已设置',
          data,
        };

      case 'get':
        const cachedData = await this.cacheService.get('GET', '/manual-cache');

        return {
          action: 'get',
          message: cachedData ? '获取到缓存数据' : '缓存数据不存在',
          cached: !!cachedData,
          data: cachedData,
        };

      case 'delete':
        const deleted = await this.cacheService.delete('GET', '/manual-cache');

        return {
          action: 'delete',
          message: deleted ? '缓存已删除' : '缓存不存在',
          deleted,
        };

      case 'stats':
        const stats = await this.cacheService.getStats();

        return {
          action: 'stats',
          message: '缓存统计信息',
          stats,
        };

      default:
        return {
          message: '支持的操作：set, get, delete, stats',
          currentAction: action,
        };
    }
  }
}
