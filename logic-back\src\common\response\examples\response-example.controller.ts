import {
  Controller,
  Get,
  Post,
  Body,
  Query,
  Param,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import { ApiTags, ApiOperation } from '@nestjs/swagger';
import { ResponseManagerService } from '../services/response-manager.service';
import {
  Response,
  SuccessResponse,
  PaginatedResponse,
  SimpleResponse,
  RestfulResponse,
  ApiResponseDocs,
} from '../decorators/response.decorator';
import {
  BusinessException,
  ValidationException,
} from '../filters/response-exception.filter';
import { ResponseFormat } from '../interfaces/response.interface';

/**
 * 响应管理器使用示例控制器
 */
@ApiTags('响应管理器示例')
@Controller('response-examples')
export class ResponseExampleController {
  constructor(private readonly responseManager: ResponseManagerService) {}

  /**
   * 基础成功响应示例
   */
  @Get('basic-success')
  @ApiOperation({ summary: '基础成功响应示例' })
  @SuccessResponse('获取数据成功')
  async basicSuccess() {
    return {
      id: 1,
      name: '测试数据',
      createTime: new Date(),
    };
  }

  /**
   * 手动创建成功响应示例
   */
  @Get('manual-success')
  @ApiOperation({ summary: '手动创建成功响应示例' })
  async manualSuccess() {
    const data = {
      id: 1,
      name: '手动创建的数据',
      createTime: new Date(),
    };

    return this.responseManager.success(data, '手动创建成功响应');
  }

  /**
   * 分页响应示例
   */
  @Get('paginated')
  @ApiOperation({ summary: '分页响应示例' })
  @PaginatedResponse('获取分页数据成功')
  async paginatedData(
    @Query('page') page: number = 1,
    @Query('size') size: number = 10,
  ) {
    // 模拟分页数据
    const mockData = Array.from({ length: size }, (_, index) => ({
      id: (page - 1) * size + index + 1,
      name: `数据项 ${(page - 1) * size + index + 1}`,
      createTime: new Date(),
    }));

    return {
      data: mockData,
      pagination: {
        page,
        size,
        total: 100,
      },
    };
  }

  /**
   * 手动创建分页响应示例
   */
  @Get('manual-paginated')
  @ApiOperation({ summary: '手动创建分页响应示例' })
  async manualPaginated(
    @Query('page') page: number = 1,
    @Query('size') size: number = 10,
  ) {
    const mockData = Array.from({ length: size }, (_, index) => ({
      id: (page - 1) * size + index + 1,
      name: `手动分页数据 ${(page - 1) * size + index + 1}`,
      createTime: new Date(),
    }));

    return this.responseManager.paginated(
      mockData,
      { page, size, total: 100 },
      '手动创建分页响应成功',
    );
  }

  /**
   * 简化响应格式示例
   */
  @Get('simple')
  @ApiOperation({ summary: '简化响应格式示例' })
  @SimpleResponse('简化格式响应')
  async simpleFormat() {
    return {
      message: '这是简化格式的响应',
      value: 42,
    };
  }

  /**
   * RESTful响应格式示例
   */
  @Get('restful')
  @ApiOperation({ summary: 'RESTful响应格式示例' })
  @RestfulResponse()
  async restfulFormat() {
    return {
      id: 1,
      name: 'RESTful格式数据',
      createTime: new Date(),
    };
  }

  /**
   * 自定义响应格式示例
   */
  @Get('custom')
  @ApiOperation({ summary: '自定义响应格式示例' })
  @Response({
    format: ResponseFormat.CUSTOM,
    successMessage: '自定义响应成功',
    includeExecutionTime: true,
    includeRequestId: true,
  })
  async customFormat() {
    return {
      customField: '自定义字段',
      data: {
        id: 1,
        name: '自定义格式数据',
      },
    };
  }

  /**
   * 业务异常示例
   */
  @Get('business-error')
  @ApiOperation({ summary: '业务异常示例' })
  @ApiResponseDocs()
  async businessError() {
    throw new BusinessException('这是一个业务异常示例', 'BUSINESS_ERROR_001');
  }

  /**
   * 验证异常示例
   */
  @Post('validation-error')
  @ApiOperation({ summary: '验证异常示例' })
  @ApiResponseDocs()
  async validationError(@Body() body: any) {
    const errors = [];
    
    if (!body.name) {
      errors.push('姓名不能为空');
    }
    
    if (!body.email) {
      errors.push('邮箱不能为空');
    } else if (!/\S+@\S+\.\S+/.test(body.email)) {
      errors.push('邮箱格式不正确');
    }

    if (errors.length > 0) {
      throw new ValidationException('数据验证失败', errors);
    }

    return this.responseManager.success(body, '数据验证通过');
  }

  /**
   * HTTP异常示例
   */
  @Get('http-error/:id')
  @ApiOperation({ summary: 'HTTP异常示例' })
  @ApiResponseDocs()
  async httpError(@Param('id') id: string) {
    if (id === 'not-found') {
      throw new HttpException('资源不存在', HttpStatus.NOT_FOUND);
    }
    
    if (id === 'forbidden') {
      throw new HttpException('权限不足', HttpStatus.FORBIDDEN);
    }
    
    if (id === 'unauthorized') {
      throw new HttpException('未授权访问', HttpStatus.UNAUTHORIZED);
    }

    return this.responseManager.success({ id }, '获取资源成功');
  }

  /**
   * 系统异常示例
   */
  @Get('system-error')
  @ApiOperation({ summary: '系统异常示例' })
  @ApiResponseDocs()
  async systemError() {
    throw new Error('这是一个系统异常示例');
  }

  /**
   * 空数据响应示例
   */
  @Get('null-data')
  @ApiOperation({ summary: '空数据响应示例' })
  @SuccessResponse('操作成功，无返回数据')
  async nullData() {
    return null;
  }

  /**
   * 手动错误响应示例
   */
  @Get('manual-error')
  @ApiOperation({ summary: '手动错误响应示例' })
  async manualError() {
    return this.responseManager.businessError(
      '这是手动创建的业务错误',
      'MANUAL_ERROR_001',
    );
  }

  /**
   * 完整API文档示例
   */
  @Get('full-docs')
  @ApiOperation({ summary: '完整API文档示例' })
  @ApiResponseDocs({
    success: {
      message: '获取完整文档数据成功',
      example: {
        id: 1,
        title: '文档标题',
        content: '文档内容',
        createTime: new Date().toISOString(),
      },
    },
  })
  async fullDocs() {
    return {
      id: 1,
      title: '完整API文档示例',
      content: '这是一个包含完整API文档的示例接口',
      createTime: new Date(),
    };
  }
}
