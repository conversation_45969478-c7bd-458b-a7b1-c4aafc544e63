import { CacheConfig } from '../services/response-cache.service';
export declare const CACHE_CONFIG_KEY = "cache_config";
export interface CacheDecoratorConfig extends CacheConfig {
    keyGenerator?: (method: string, path: string, params?: any, userId?: string | number) => string;
    includeUserId?: boolean;
    includeParams?: boolean;
    condition?: (request: any, response?: any) => boolean;
    tags?: string[];
}
export declare function Cache(config?: CacheDecoratorConfig): <TFunction extends Function, Y>(target: TFunction | object, propertyKey?: string | symbol, descriptor?: TypedPropertyDescriptor<Y>) => void;
export declare function ShortCache(ttl?: number): <TFunction extends Function, Y>(target: TFunction | object, propertyKey?: string | symbol, descriptor?: TypedPropertyDescriptor<Y>) => void;
export declare function MediumCache(ttl?: number): <TFunction extends Function, Y>(target: TFunction | object, propertyKey?: string | symbol, descriptor?: TypedPropertyDescriptor<Y>) => void;
export declare function LongCache(ttl?: number): <TFunction extends Function, Y>(target: TFunction | object, propertyKey?: string | symbol, descriptor?: TypedPropertyDescriptor<Y>) => void;
export declare function UserCache(ttl?: number): <TFunction extends Function, Y>(target: TFunction | object, propertyKey?: string | symbol, descriptor?: TypedPropertyDescriptor<Y>) => void;
export declare function ParamCache(ttl?: number): <TFunction extends Function, Y>(target: TFunction | object, propertyKey?: string | symbol, descriptor?: TypedPropertyDescriptor<Y>) => void;
export declare function FullCache(ttl?: number): <TFunction extends Function, Y>(target: TFunction | object, propertyKey?: string | symbol, descriptor?: TypedPropertyDescriptor<Y>) => void;
export declare function ConditionalCache(condition: (request: any, response?: any) => boolean, ttl?: number): <TFunction extends Function, Y>(target: TFunction | object, propertyKey?: string | symbol, descriptor?: TypedPropertyDescriptor<Y>) => void;
export declare function TaggedCache(tags: string[], ttl?: number): <TFunction extends Function, Y>(target: TFunction | object, propertyKey?: string | symbol, descriptor?: TypedPropertyDescriptor<Y>) => void;
export declare function CompressedCache(ttl?: number): <TFunction extends Function, Y>(target: TFunction | object, propertyKey?: string | symbol, descriptor?: TypedPropertyDescriptor<Y>) => void;
export declare function LargeDataCache(ttl?: number, maxSize?: number): <TFunction extends Function, Y>(target: TFunction | object, propertyKey?: string | symbol, descriptor?: TypedPropertyDescriptor<Y>) => void;
export declare function CustomKeyCache(keyGenerator: (method: string, path: string, params?: any, userId?: string | number) => string, ttl?: number): <TFunction extends Function, Y>(target: TFunction | object, propertyKey?: string | symbol, descriptor?: TypedPropertyDescriptor<Y>) => void;
export declare function NoCache(): <TFunction extends Function, Y>(target: TFunction | object, propertyKey?: string | symbol, descriptor?: TypedPropertyDescriptor<Y>) => void;
export declare function DevCache(ttl?: number): <TFunction extends Function, Y>(target: TFunction | object, propertyKey?: string | symbol, descriptor?: TypedPropertyDescriptor<Y>) => void;
export declare function ProdCache(ttl?: number): <TFunction extends Function, Y>(target: TFunction | object, propertyKey?: string | symbol, descriptor?: TypedPropertyDescriptor<Y>) => void;
export declare function WarmupCache(ttl?: number): <TFunction extends Function, Y>(target: TFunction | object, propertyKey?: string | symbol, descriptor?: TypedPropertyDescriptor<Y>) => void;
export declare function VersionedCache(version: string, ttl?: number): <TFunction extends Function, Y>(target: TFunction | object, propertyKey?: string | symbol, descriptor?: TypedPropertyDescriptor<Y>) => void;
export declare function PaginatedCache(ttl?: number): <TFunction extends Function, Y>(target: TFunction | object, propertyKey?: string | symbol, descriptor?: TypedPropertyDescriptor<Y>) => void;
export declare function SearchCache(ttl?: number): <TFunction extends Function, Y>(target: TFunction | object, propertyKey?: string | symbol, descriptor?: TypedPropertyDescriptor<Y>) => void;
export declare function StaticCache(ttl?: number): <TFunction extends Function, Y>(target: TFunction | object, propertyKey?: string | symbol, descriptor?: TypedPropertyDescriptor<Y>) => void;
export declare function RealtimeCache(ttl?: number): <TFunction extends Function, Y>(target: TFunction | object, propertyKey?: string | symbol, descriptor?: TypedPropertyDescriptor<Y>) => void;
export declare class CacheUtils {
    static timeBasedKey(method: string, path: string, timeWindow?: number): string;
    static roleBasedKey(method: string, path: string, userRole: string): string;
    static locationBasedKey(method: string, path: string, location: string): string;
    static languageBasedKey(method: string, path: string, language: string): string;
    static deviceBasedKey(method: string, path: string, deviceType: string): string;
    static shouldCacheResponse(response: any): boolean;
    static shouldUseCache(request: any): boolean;
}
