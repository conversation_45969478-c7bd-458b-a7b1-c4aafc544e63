import { IPerformanceMonitor, PerformanceMetrics, UnifiedResponseConfig } from '../interfaces/unified-response.interface';
import { LoggerService } from '../../logger/logger.service';
export declare class PerformanceMonitorService implements IPerformanceMonitor {
    private readonly logger;
    private readonly config;
    private readonly monitoringData;
    private readonly maxHistorySize;
    constructor(logger: LoggerService, config: UnifiedResponseConfig);
    startMonitoring(requestId: string): void;
    endMonitoring(requestId: string): PerformanceMetrics;
    recordDbQuery(requestId: string): void;
    recordCacheOperation(requestId: string, hit: boolean): void;
    recordQueueWaitTime(requestId: string, waitTime: number): void;
    getActiveMonitoringCount(): number;
    getSystemMetrics(): {
        memoryUsage: NodeJS.MemoryUsage;
        uptime: number;
        activeMonitoring: number;
    };
    private calculateMemoryUsage;
    private getDefaultMetrics;
    private logPerformanceMetrics;
    private startCleanupTimer;
    private cleanupStaleMonitoring;
}
