import { ConfigService } from '@nestjs/config';
import { DistributedLock } from '../../../payment/lock/distributed.lock';
export declare class TeachingLockService {
    private readonly distributedLock;
    private readonly configService;
    private readonly logger;
    constructor(distributedLock: DistributedLock, configService: ConfigService);
    withDistributedLock<T>(key: string, callback: () => Promise<T>, ttl?: number): Promise<T>;
    getLockConfig(): {
        timeout: any;
        maxRetries: any;
        retryDelay: any;
    };
}
