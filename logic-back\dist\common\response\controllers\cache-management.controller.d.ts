import { ResponseCacheService } from '../services/response-cache.service';
import { LoggerService } from '../../logger/logger.service';
export declare class CacheManagementController {
    private readonly cacheService;
    private readonly loggerService;
    constructor(cacheService: ResponseCacheService, loggerService: LoggerService);
    getCacheStats(): Promise<{
        timestamp: string;
        formattedSize: string;
        totalKeys: number;
        totalSize: number;
        avgTtl: number;
    }>;
    checkCacheExists(method: string, path: string, userId?: string): Promise<{
        exists: boolean;
        ttl: number;
        expiresAt: string | null;
    }>;
    getCacheContent(method: string, path: string, userId?: string): Promise<{
        exists: boolean;
        content: null;
        size?: undefined;
        formattedSize?: undefined;
    } | {
        exists: boolean;
        content: import("..").BaseResponse<any>;
        size: number;
        formattedSize: string;
    }>;
    deleteCacheItem(method: string, path: string, userId?: string): Promise<{
        deleted: boolean;
        message: string;
    }>;
    deleteCacheByPattern(pattern: string): Promise<{
        deletedCount: number;
        pattern: string;
        message: string;
    }>;
    clearAllCache(): Promise<{
        deletedCount: number;
        message: string;
        timestamp: string;
    }>;
    warmupCache(warmupConfig: {
        endpoints: Array<{
            method: string;
            path: string;
            params?: any;
            userId?: number;
        }>;
    }): Promise<{
        totalEndpoints: number;
        results: {
            method: string;
            path: string;
            status: string;
            message: string;
        }[];
        message: string;
    }>;
    getCacheHealth(): Promise<{
        healthy: boolean;
        stats: {
            totalKeys: number;
            totalSize: number;
            avgTtl: number;
        };
        timestamp: string;
        checks: {
            connection: boolean;
            keyCount: number;
            totalSize: number;
            avgTtl: number;
        };
        error?: undefined;
    } | {
        healthy: boolean;
        error: any;
        timestamp: string;
        stats?: undefined;
        checks?: undefined;
    }>;
    getCacheConfig(): Promise<{
        defaultTtl: number;
        maxSize: number;
        compressionEnabled: boolean;
        environment: string | undefined;
        redisConnected: boolean;
        timestamp: string;
    }>;
    getPopularCacheKeys(limit?: string): Promise<{
        popularKeys: {
            key: string;
            hits: number;
            lastAccess: Date;
        }[];
        totalTracked: number;
        timestamp: string;
    }>;
    private formatBytes;
}
