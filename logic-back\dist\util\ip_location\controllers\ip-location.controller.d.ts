import { Request } from 'express';
import { ResponseManagerService, ResponseCacheService } from '../../../common/response';
import { IpLocationFacadeService } from '../application/services/ip-location-facade.service';
import { IpQueryRequestDto } from '../application/dto/requests/ip-query.request.dto';
import { RiskCheckRequestDto } from '../application/dto/requests/risk-check.request.dto';
import { TrustLocationRequestDto } from '../application/dto/requests/trust-location.request.dto';
import { LocationInfoResponseDto } from '../application/dto/responses/location-info.response.dto';
import { RiskAssessmentResponseDto } from '../application/dto/responses/risk-assessment.response.dto';
import { LocationStatsResponseDto } from '../application/dto/responses/location-stats.response.dto';
export declare class IpLocationController {
    private readonly ipLocationFacadeService;
    private readonly responseManager;
    private readonly cacheService;
    constructor(ipLocationFacadeService: IpLocationFacadeService, responseManager: ResponseManagerService, cacheService: ResponseCacheService);
    queryIpLocationV2(query: IpQueryRequestDto): Promise<{
        location: LocationInfoResponseDto | null;
        meta: {
            executionTime: any;
            fromCache: any;
            ip: string;
            includeRisk: boolean;
        };
    }>;
    checkLoginRisk(request: RiskCheckRequestDto): Promise<{
        riskAssessment: RiskAssessmentResponseDto | null;
        meta: {
            userId: number;
            ipAddress: string;
            assessmentTime: string;
        };
    }>;
    getUserLocationStats(userId: number, days?: number, includeTrusted?: boolean): Promise<{
        statistics: LocationStatsResponseDto | null;
        meta: {
            userId: number;
            days: number;
            includeTrusted: boolean;
            generatedAt: string;
        };
    }>;
    setTrustedLocation(userId: number, request: TrustLocationRequestDto): Promise<{
        trustedLocation: {
            userId: number;
            province: string;
            city: string;
        } | null;
        meta: {
            userId: number;
            province: string;
            city: string;
            reason: string | undefined;
            setAt: string;
        };
    }>;
    getCurrentIpLocation(request: Request): Promise<{
        currentLocation: LocationInfoResponseDto | null;
        meta: {
            clientIp: string;
            requestTime: string;
            userAgent: string | undefined;
        };
    }>;
    healthCheck(): Promise<{
        service: string;
        status: string;
        timestamp: string;
        version: string;
        cache: {
            enabled: boolean;
            totalKeys: number;
            totalSize: number;
        };
        uptime: number;
        error?: undefined;
    } | {
        service: string;
        status: string;
        timestamp: string;
        version: string;
        error: string;
        cache?: undefined;
        uptime?: undefined;
    }>;
    clearCache(type?: string, target?: string): Promise<{
        type: string;
        target: string | undefined;
        deletedCount: number;
        message: string;
        clearedAt: string;
    }>;
    private extractClientIP;
    private cleanAndValidateIP;
    private isValidIP;
    private getIpSourceName;
    private clearUserLocationCache;
    private clearIpLocationCache;
}
