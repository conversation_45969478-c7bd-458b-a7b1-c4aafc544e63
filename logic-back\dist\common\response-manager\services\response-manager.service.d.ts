import { IResponseManager, RequestContext, EnhancedResponse, ResponseFormat, ResponseManagerConfig, RequestPoolStatus, IRequestPool, IPerformanceMonitor } from '../interfaces/response-manager.interface';
import { CommandResult } from '../../../util/ip_location/application/interfaces/command-handler.interface';
import { QueryResult } from '../../../util/ip_location/application/interfaces/query-handler.interface';
import { HttpResponse } from '../../../web/http_response_result/http-response.interface';
import { LoggerService } from '../../logger/logger.service';
export declare class ResponseManagerService implements IResponseManager {
    private readonly logger;
    private readonly requestPool;
    private readonly performanceMonitor;
    private readonly config;
    constructor(logger: LoggerService, requestPool: IRequestPool, performanceMonitor: IPerformanceMonitor, config: ResponseManagerConfig);
    handleCommandResult<T>(commandResult: CommandResult<T>, context: Partial<RequestContext>, format?: ResponseFormat): Promise<EnhancedResponse<T>>;
    handleQueryResult<T>(queryResult: QueryResult<T>, context: Partial<RequestContext>, format?: ResponseFormat): Promise<EnhancedResponse<T>>;
    createContext(request: any, module?: string, operation?: string): RequestContext;
    toHttpResponse<T>(enhancedResponse: EnhancedResponse<T>): HttpResponse<T>;
    getPoolStatus(): Promise<RequestPoolStatus>;
    logResponse<T>(enhancedResponse: EnhancedResponse<T>, level?: 'info' | 'warn' | 'error'): void;
    private generateRequestId;
    private enrichContext;
    private sanitizeHeaders;
}
