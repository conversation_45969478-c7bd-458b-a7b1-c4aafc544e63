"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DddResponseProcessorService = void 0;
const common_1 = require("@nestjs/common");
const http_response_interface_1 = require("../../../web/http_response_result/http-response.interface");
let DddResponseProcessorService = class DddResponseProcessorService {
    isCommandResult(result) {
        return (result &&
            typeof result === 'object' &&
            'success' in result &&
            'timestamp' in result &&
            typeof result.success === 'boolean' &&
            result.timestamp instanceof Date);
    }
    isQueryResult(result) {
        return (this.isCommandResult(result) &&
            ('fromCache' in result || 'cacheKey' in result));
    }
    processCommandResult(commandResult, context, metrics) {
        return {
            context,
            data: commandResult.data,
            success: commandResult.success,
            message: commandResult.message || (commandResult.success ? '命令执行成功' : '命令执行失败'),
            errors: commandResult.errors,
            code: commandResult.success ? http_response_interface_1.SUCCESS_CODE : http_response_interface_1.ERROR_CODE,
            metrics: {
                ...metrics,
                executionTime: commandResult.executionTime || metrics.executionTime
            },
            timestamp: commandResult.timestamp,
            originalType: 'command-result'
        };
    }
    processQueryResult(queryResult, context, metrics) {
        return {
            context,
            data: queryResult.data,
            success: queryResult.success,
            message: queryResult.message || (queryResult.success ? '查询执行成功' : '查询执行失败'),
            errors: queryResult.errors,
            code: queryResult.success ? http_response_interface_1.SUCCESS_CODE : http_response_interface_1.ERROR_CODE,
            metrics: {
                ...metrics,
                executionTime: queryResult.executionTime || metrics.executionTime,
                cacheHits: queryResult.fromCache ? 1 : 0,
                cacheMisses: queryResult.fromCache ? 0 : 1
            },
            timestamp: queryResult.timestamp,
            originalType: 'query-result'
        };
    }
    processDddResult(result, context, metrics) {
        if (this.isQueryResult(result)) {
            return this.processQueryResult(result, context, metrics);
        }
        else if (this.isCommandResult(result)) {
            return this.processCommandResult(result, context, metrics);
        }
        else {
            throw new Error('无效的DDD结果格式');
        }
    }
    createDddSuccessResponse(data, message, context, metrics, isQuery = false) {
        return {
            context,
            data,
            success: true,
            message,
            errors: undefined,
            code: http_response_interface_1.SUCCESS_CODE,
            metrics,
            timestamp: new Date(),
            originalType: isQuery ? 'query-result' : 'command-result'
        };
    }
    createDddErrorResponse(errors, message, context, metrics, isQuery = false) {
        return {
            context,
            data: undefined,
            success: false,
            message,
            errors,
            code: http_response_interface_1.ERROR_CODE,
            metrics,
            timestamp: new Date(),
            originalType: isQuery ? 'query-result' : 'command-result'
        };
    }
    toDddCompatibleFormat(unifiedResponse) {
        const { context, data, success, message, errors, metrics, timestamp, originalType } = unifiedResponse;
        const baseResult = {
            success,
            data,
            message,
            errors,
            timestamp,
            executionTime: metrics.executionTime
        };
        if (originalType === 'query-result') {
            return {
                ...baseResult,
                fromCache: (metrics.cacheHits || 0) > 0,
                cacheKey: context.metadata?.cacheKey
            };
        }
        return baseResult;
    }
    enhanceDddResult(originalResult, context, additionalMetrics) {
        const enhanced = { ...originalResult };
        if (additionalMetrics?.executionTime && !enhanced.executionTime) {
            enhanced.executionTime = additionalMetrics.executionTime;
        }
        if (this.isQueryResult(enhanced) && additionalMetrics) {
            if ((additionalMetrics.cacheHits || 0) > 0 && enhanced.fromCache === undefined) {
                enhanced.fromCache = true;
            }
            if (!enhanced.cacheKey && context.metadata?.cacheKey) {
                enhanced.cacheKey = context.metadata.cacheKey;
            }
        }
        return enhanced;
    }
    validateDddResult(result) {
        const errors = [];
        if (!result || typeof result !== 'object') {
            errors.push('结果必须是一个对象');
            return { isValid: false, errors };
        }
        if (typeof result.success !== 'boolean') {
            errors.push('success字段必须是布尔值');
        }
        if (!(result.timestamp instanceof Date)) {
            errors.push('timestamp字段必须是Date对象');
        }
        if (result.errors && !Array.isArray(result.errors)) {
            errors.push('errors字段必须是字符串数组');
        }
        if (result.executionTime && typeof result.executionTime !== 'number') {
            errors.push('executionTime字段必须是数字');
        }
        if ('fromCache' in result && typeof result.fromCache !== 'boolean') {
            errors.push('fromCache字段必须是布尔值');
        }
        if ('cacheKey' in result && typeof result.cacheKey !== 'string') {
            errors.push('cacheKey字段必须是字符串');
        }
        return { isValid: errors.length === 0, errors };
    }
};
exports.DddResponseProcessorService = DddResponseProcessorService;
exports.DddResponseProcessorService = DddResponseProcessorService = __decorate([
    (0, common_1.Injectable)()
], DddResponseProcessorService);
//# sourceMappingURL=ddd-response-processor.service.js.map