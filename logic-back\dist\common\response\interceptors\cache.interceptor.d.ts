import { NestInterceptor, ExecutionContext, CallHandler } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { Observable } from 'rxjs';
import { ResponseCacheService } from '../services/response-cache.service';
import { LoggerService } from '../../logger/logger.service';
export declare class CacheInterceptor implements NestInterceptor {
    private readonly cacheService;
    private readonly reflector;
    private readonly loggerService;
    constructor(cacheService: ResponseCacheService, reflector: Reflector, loggerService: LoggerService);
    intercept(context: ExecutionContext, next: CallHandler): Promise<Observable<any>>;
    private shouldUseCache;
    private shouldCacheResponse;
    private extractParams;
    private extractUserId;
    private generateCacheKey;
    private generateRequestId;
}
export declare class CacheClearInterceptor implements NestInterceptor {
    private readonly cacheService;
    private readonly reflector;
    private readonly loggerService;
    constructor(cacheService: ResponseCacheService, reflector: Reflector, loggerService: LoggerService);
    intercept(context: ExecutionContext, next: CallHandler): Observable<any>;
    private clearRelatedCache;
    private generateRequestId;
}
export declare class CacheWarmupInterceptor implements NestInterceptor {
    private readonly cacheService;
    private readonly loggerService;
    constructor(cacheService: ResponseCacheService, loggerService: LoggerService);
    intercept(context: ExecutionContext, next: CallHandler): Observable<any>;
    private warmupRelatedCache;
    private generateRequestId;
}
