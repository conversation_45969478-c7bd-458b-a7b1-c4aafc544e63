"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.IpLocationModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const user_common_location_entity_1 = require("./domain/entities/user-common-location.entity");
const ip_location_controller_1 = require("./controllers/ip-location.controller");
const ip_location_application_service_1 = require("./application/services/ip-location-application.service");
const ip_location_command_service_1 = require("./application/services/ip-location-command.service");
const ip_location_query_service_1 = require("./application/services/ip-location-query.service");
const ip_location_facade_service_1 = require("./application/services/ip-location-facade.service");
const ip_location_domain_service_1 = require("./domain/services/ip-location-domain.service");
const risk_assessment_domain_service_1 = require("./domain/services/risk-assessment-domain.service");
const location_comparison_service_1 = require("./domain/services/location-comparison.service");
const ip_location_command_repository_1 = require("./infrastructure/repositories/ip-location-command.repository");
const ip2region_service_1 = require("./infrastructure/external/ip2region.service");
const redis_cache_service_1 = require("./infrastructure/external/redis-cache.service");
const ip_location_util_1 = require("./utils/ip-location.util");
const risk_assessment_util_1 = require("./utils/risk-assessment.util");
const redis_module_1 = require("../database/redis/redis.module");
const logger_service_1 = require("../../common/logger/logger.service");
const response_module_1 = require("../../common/response/response.module");
let IpLocationModule = class IpLocationModule {
};
exports.IpLocationModule = IpLocationModule;
exports.IpLocationModule = IpLocationModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([user_common_location_entity_1.UserCommonLocation]),
            redis_module_1.RedisModule,
            response_module_1.ResponseModule,
        ],
        controllers: [
            ip_location_controller_1.IpLocationController,
        ],
        providers: [
            ip_location_application_service_1.IpLocationApplicationService,
            ip_location_command_service_1.IpLocationCommandService,
            ip_location_query_service_1.IpLocationQueryService,
            ip_location_facade_service_1.IpLocationFacadeService,
            ip_location_domain_service_1.IpLocationDomainService,
            risk_assessment_domain_service_1.RiskAssessmentDomainService,
            location_comparison_service_1.LocationComparisonService,
            ip_location_command_repository_1.IpLocationCommandRepository,
            ip2region_service_1.Ip2RegionService,
            redis_cache_service_1.RedisCacheService,
            ip_location_util_1.IpLocationUtil,
            risk_assessment_util_1.RiskAssessmentUtil,
            logger_service_1.LoggerService,
        ],
        exports: [
            ip_location_application_service_1.IpLocationApplicationService,
            ip_location_command_service_1.IpLocationCommandService,
            ip_location_query_service_1.IpLocationQueryService,
            ip_location_facade_service_1.IpLocationFacadeService,
            ip_location_domain_service_1.IpLocationDomainService,
            risk_assessment_domain_service_1.RiskAssessmentDomainService,
            ip2region_service_1.Ip2RegionService,
            redis_cache_service_1.RedisCacheService,
            ip_location_util_1.IpLocationUtil,
            risk_assessment_util_1.RiskAssessmentUtil,
        ],
    })
], IpLocationModule);
//# sourceMappingURL=ip-location.module.js.map