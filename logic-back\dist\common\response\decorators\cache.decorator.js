"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CacheUtils = exports.CACHE_CONFIG_KEY = void 0;
exports.Cache = Cache;
exports.ShortCache = ShortCache;
exports.MediumCache = MediumCache;
exports.LongCache = LongCache;
exports.UserCache = UserCache;
exports.ParamCache = ParamCache;
exports.FullCache = FullCache;
exports.ConditionalCache = ConditionalCache;
exports.TaggedCache = TaggedCache;
exports.CompressedCache = CompressedCache;
exports.LargeDataCache = LargeDataCache;
exports.CustomKeyCache = CustomKeyCache;
exports.NoCache = NoCache;
exports.DevCache = DevCache;
exports.ProdCache = ProdCache;
exports.WarmupCache = WarmupCache;
exports.VersionedCache = VersionedCache;
exports.PaginatedCache = PaginatedCache;
exports.SearchCache = SearchCache;
exports.StaticCache = StaticCache;
exports.RealtimeCache = RealtimeCache;
const common_1 = require("@nestjs/common");
exports.CACHE_CONFIG_KEY = 'cache_config';
function Cache(config) {
    return (0, common_1.applyDecorators)((0, common_1.SetMetadata)(exports.CACHE_CONFIG_KEY, config || {}));
}
function ShortCache(ttl = 60) {
    return Cache({
        defaultTtl: ttl,
        enabled: true,
    });
}
function MediumCache(ttl = 300) {
    return Cache({
        defaultTtl: ttl,
        enabled: true,
    });
}
function LongCache(ttl = 3600) {
    return Cache({
        defaultTtl: ttl,
        enabled: true,
    });
}
function UserCache(ttl = 300) {
    return Cache({
        defaultTtl: ttl,
        enabled: true,
        includeUserId: true,
    });
}
function ParamCache(ttl = 300) {
    return Cache({
        defaultTtl: ttl,
        enabled: true,
        includeParams: true,
    });
}
function FullCache(ttl = 300) {
    return Cache({
        defaultTtl: ttl,
        enabled: true,
        includeUserId: true,
        includeParams: true,
    });
}
function ConditionalCache(condition, ttl = 300) {
    return Cache({
        defaultTtl: ttl,
        enabled: true,
        condition,
    });
}
function TaggedCache(tags, ttl = 300) {
    return Cache({
        defaultTtl: ttl,
        enabled: true,
        tags,
    });
}
function CompressedCache(ttl = 300) {
    return Cache({
        defaultTtl: ttl,
        enabled: true,
        compress: true,
    });
}
function LargeDataCache(ttl = 300, maxSize = 5 * 1024 * 1024) {
    return Cache({
        defaultTtl: ttl,
        enabled: true,
        compress: true,
        maxSize,
    });
}
function CustomKeyCache(keyGenerator, ttl = 300) {
    return Cache({
        defaultTtl: ttl,
        enabled: true,
        keyGenerator,
    });
}
function NoCache() {
    return Cache({
        enabled: false,
    });
}
function DevCache(ttl = 60) {
    return Cache({
        defaultTtl: ttl,
        enabled: process.env.NODE_ENV === 'development',
    });
}
function ProdCache(ttl = 300) {
    return Cache({
        defaultTtl: ttl,
        enabled: process.env.NODE_ENV === 'production',
    });
}
function WarmupCache(ttl = 3600) {
    return Cache({
        defaultTtl: ttl,
        enabled: true,
        tags: ['warmup'],
    });
}
function VersionedCache(version, ttl = 300) {
    return Cache({
        defaultTtl: ttl,
        enabled: true,
        prefix: `response_cache_v${version}`,
    });
}
function PaginatedCache(ttl = 300) {
    return Cache({
        defaultTtl: ttl,
        enabled: true,
        includeParams: true,
        tags: ['paginated'],
    });
}
function SearchCache(ttl = 600) {
    return Cache({
        defaultTtl: ttl,
        enabled: true,
        includeParams: true,
        includeUserId: true,
        tags: ['search'],
    });
}
function StaticCache(ttl = 86400) {
    return Cache({
        defaultTtl: ttl,
        enabled: true,
        tags: ['static'],
    });
}
function RealtimeCache(ttl = 10) {
    return Cache({
        defaultTtl: ttl,
        enabled: true,
        tags: ['realtime'],
    });
}
class CacheUtils {
    static timeBasedKey(method, path, timeWindow = 60) {
        const timeSlot = Math.floor(Date.now() / (timeWindow * 1000));
        return `${method}:${path}:${timeSlot}`;
    }
    static roleBasedKey(method, path, userRole) {
        return `${method}:${path}:role:${userRole}`;
    }
    static locationBasedKey(method, path, location) {
        return `${method}:${path}:location:${location}`;
    }
    static languageBasedKey(method, path, language) {
        return `${method}:${path}:lang:${language}`;
    }
    static deviceBasedKey(method, path, deviceType) {
        return `${method}:${path}:device:${deviceType}`;
    }
    static shouldCacheResponse(response) {
        if (response.success === false || response.code >= 400) {
            return false;
        }
        if (!response.data) {
            return false;
        }
        const responseSize = JSON.stringify(response).length;
        if (responseSize > 1024 * 1024) {
            return false;
        }
        return true;
    }
    static shouldUseCache(request) {
        if (request.method !== 'GET') {
            return false;
        }
        if (request.headers.authorization && !request.cacheAuth) {
            return false;
        }
        return true;
    }
}
exports.CacheUtils = CacheUtils;
//# sourceMappingURL=cache.decorator.js.map