import { Injectable, LoggerService as NestLoggerService } from '@nestjs/common';
import { Logger } from 'winston';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';
import { Inject } from '@nestjs/common';

@Injectable()
export class LoggerService implements NestLoggerService {
  constructor(
    @Inject(WINSTON_MODULE_PROVIDER) private readonly logger: Logger
  ) {}

  /**
   * 记录普通日志
   */
  log(message: any, context?: string) {
    this.logger.info(message, { context });
  }

  /**
   * 记录错误日志
   */
  error(message: any, trace?: string, context?: string) {
    this.logger.error(message, { context, trace });
  }

  /**
   * 记录警告日志
   */
  warn(message: any, context?: string) {
    this.logger.warn(message, { context });
  }

  /**
   * 记录调试日志
   */
  debug(message: any, context?: string) {
    this.logger.debug(message, { context });
  }

  /**
   * 记录详细日志
   */
  verbose(message: any, context?: string) {
    this.logger.verbose(message, { context });
  }

  /**
   * 记录HTTP请求日志
   */
  logHttpRequest(req: any, res: any, responseTime: number) {
    const logData = {
      method: req.method,
      url: req.url,
      statusCode: res.statusCode,
      responseTime: `${responseTime}ms`,
      userAgent: req.get('User-Agent'),
      ip: req.ip || req.connection.remoteAddress,
      timestamp: new Date().toISOString()
    };

    this.logger.info('HTTP Request', {
      context: 'HTTP',
      ...logData
    });
  }

  /**
   * 记录数据库操作日志
   */
  logDatabase(operation: string, table: string, data?: any, error?: any) {
    const logData = {
      operation,
      table,
      data: data ? JSON.stringify(data) : undefined,
      error: error ? error.message : undefined,
      timestamp: new Date().toISOString()
    };

    if (error) {
      this.logger.error(`Database ${operation} failed on ${table}`, {
        context: 'Database',
        ...logData,
        trace: error.stack
      });
    } else {
      this.logger.info(`Database ${operation} on ${table}`, {
        context: 'Database',
        ...logData
      });
    }
  }

  /**
   * 记录支付相关日志
   */
  logPayment(action: string, data: any, error?: any) {
    const logData = {
      action,
      data: JSON.stringify(data),
      error: error ? error.message : undefined,
      timestamp: new Date().toISOString()
    };

    if (error) {
      this.logger.error(`Payment ${action} failed`, {
        context: 'Payment',
        ...logData,
        trace: error.stack
      });
    } else {
      this.logger.info(`Payment ${action}`, {
        context: 'Payment',
        ...logData
      });
    }
  }

  /**
   * 记录认证相关日志
   */
  logAuth(action: string, userId?: string, details?: any, error?: any) {
    const logData = {
      action,
      userId,
      details: details ? JSON.stringify(details) : undefined,
      error: error ? error.message : undefined,
      timestamp: new Date().toISOString()
    };

    if (error) {
      this.logger.error(`Auth ${action} failed`, {
        context: 'Auth',
        ...logData,
        trace: error.stack
      });
    } else {
      this.logger.info(`Auth ${action}`, {
        context: 'Auth',
        ...logData
      });
    }
  }

  /**
   * 记录业务逻辑日志
   */
  logBusiness(module: string, action: string, data?: any, error?: any) {
    const logData = {
      module,
      action,
      data: data ? JSON.stringify(data) : undefined,
      error: error ? error.message : undefined,
      timestamp: new Date().toISOString()
    };

    if (error) {
      this.logger.error(`Business ${module}.${action} failed`, {
        context: 'Business',
        ...logData,
        trace: error.stack
      });
    } else {
      this.logger.info(`Business ${module}.${action}`, {
        context: 'Business',
        ...logData
      });
    }
  }

  /**
   * 记录系统启动日志
   */
  logStartup(message: string, details?: any) {
    this.logger.info(message, {
      context: 'Startup',
      ...details,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * 记录性能日志
   */
  logPerformance(operation: string, duration: number, details?: any) {
    this.logger.info(`Performance: ${operation} took ${duration}ms`, {
      context: 'Performance',
      operation,
      duration,
      ...details,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * 记录安全相关日志
   */
  logSecurity(event: string, details: any, severity: 'low' | 'medium' | 'high' = 'medium') {
    const logData = {
      event,
      severity,
      details: JSON.stringify(details),
      timestamp: new Date().toISOString()
    };

    if (severity === 'high') {
      this.logger.error(`Security Alert: ${event}`, {
        context: 'Security',
        ...logData
      });
    } else {
      this.logger.warn(`Security Event: ${event}`, {
        context: 'Security',
        ...logData
      });
    }
  }

  /**
   * 记录API响应日志
   */
  logApiResponse(data: {
    requestId?: string;
    method: string;
    path: string;
    statusCode: number;
    success: boolean;
    message: string;
    executionTime?: number;
    userId?: string | number;
    ip?: string;
    userAgent?: string;
    requestBody?: any;
    responseData?: any;
    errors?: string[];
    errorType?: string;
    module?: string;
    operation?: string;
  }) {
    const logData = {
      requestId: data.requestId,
      method: data.method,
      path: data.path,
      statusCode: data.statusCode,
      success: data.success,
      message: data.message,
      executionTime: data.executionTime ? `${data.executionTime}ms` : undefined,
      userId: data.userId,
      ip: data.ip,
      userAgent: data.userAgent,
      module: data.module,
      operation: data.operation,
      timestamp: new Date().toISOString()
    };

    // 根据响应状态选择日志级别
    if (data.success) {
      this.logger.info(`API Response: ${data.method} ${data.path} - ${data.message}`, {
        context: 'ApiResponse',
        ...logData,
        requestBody: data.requestBody ? this.sanitizeData(data.requestBody) : undefined,
        responseData: data.responseData ? this.sanitizeData(data.responseData) : undefined
      });
    } else {
      this.logger.error(`API Error: ${data.method} ${data.path} - ${data.message}`, {
        context: 'ApiResponse',
        ...logData,
        errors: data.errors,
        errorType: data.errorType,
        requestBody: data.requestBody ? this.sanitizeData(data.requestBody) : undefined
      });
    }
  }

  /**
   * 记录响应处理过程日志
   */
  logResponseProcessing(data: {
    requestId?: string;
    stage: 'interceptor' | 'filter' | 'manager';
    action: string;
    details?: any;
    duration?: number;
    error?: any;
  }) {
    const logData = {
      requestId: data.requestId,
      stage: data.stage,
      action: data.action,
      duration: data.duration ? `${data.duration}ms` : undefined,
      details: data.details ? this.sanitizeData(data.details) : undefined,
      timestamp: new Date().toISOString()
    };

    if (data.error) {
      this.logger.error(`Response Processing Error: ${data.stage}.${data.action}`, {
        context: 'ResponseProcessing',
        ...logData,
        error: data.error.message,
        trace: data.error.stack
      });
    } else {
      this.logger.debug(`Response Processing: ${data.stage}.${data.action}`, {
        context: 'ResponseProcessing',
        ...logData
      });
    }
  }

  /**
   * 记录响应格式转换日志
   */
  logResponseTransform(data: {
    requestId?: string;
    fromFormat: string;
    toFormat: string;
    originalData?: any;
    transformedData?: any;
    duration?: number;
  }) {
    const logData = {
      requestId: data.requestId,
      fromFormat: data.fromFormat,
      toFormat: data.toFormat,
      duration: data.duration ? `${data.duration}ms` : undefined,
      timestamp: new Date().toISOString()
    };

    this.logger.debug(`Response Transform: ${data.fromFormat} -> ${data.toFormat}`, {
      context: 'ResponseTransform',
      ...logData,
      originalData: data.originalData ? this.sanitizeData(data.originalData) : undefined,
      transformedData: data.transformedData ? this.sanitizeData(data.transformedData) : undefined
    });
  }

  /**
   * 记录响应缓存日志
   */
  logResponseCache(data: {
    requestId?: string;
    action: 'hit' | 'miss' | 'set' | 'invalidate';
    cacheKey?: string;
    ttl?: number;
    size?: number;
  }) {
    const logData = {
      requestId: data.requestId,
      action: data.action,
      cacheKey: data.cacheKey,
      ttl: data.ttl,
      size: data.size,
      timestamp: new Date().toISOString()
    };

    this.logger.debug(`Response Cache: ${data.action}`, {
      context: 'ResponseCache',
      ...logData
    });
  }

  /**
   * 数据脱敏处理
   */
  private sanitizeData(data: any): any {
    if (!data || typeof data !== 'object') {
      return data;
    }

    const sensitiveFields = [
      'password', 'token', 'secret', 'key', 'authorization',
      'credit_card', 'ssn', 'phone', 'email', 'id_card'
    ];

    const sanitized = JSON.parse(JSON.stringify(data));

    const sanitizeObject = (obj: any): any => {
      if (Array.isArray(obj)) {
        return obj.map(item => sanitizeObject(item));
      }

      if (obj && typeof obj === 'object') {
        const result: any = {};
        for (const [key, value] of Object.entries(obj)) {
          const lowerKey = key.toLowerCase();
          if (sensitiveFields.some(field => lowerKey.includes(field))) {
            result[key] = '***REDACTED***';
          } else {
            result[key] = sanitizeObject(value);
          }
        }
        return result;
      }

      return obj;
    };

    return sanitizeObject(sanitized);
  }
}
