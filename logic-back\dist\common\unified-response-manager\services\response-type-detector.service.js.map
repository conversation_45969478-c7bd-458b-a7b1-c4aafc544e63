{"version": 3, "file": "response-type-detector.service.js", "sourceRoot": "", "sources": ["../../../../src/common/unified-response-manager/services/response-type-detector.service.ts"], "names": [], "mappings": ";;;;;;;;;AAAA,2CAA4C;AAQrC,IAAM,2BAA2B,GAAjC,MAAM,2BAA2B;IAKtC,UAAU,CAAC,MAAW;QACpB,IAAI,MAAM,KAAK,IAAI;YAAE,OAAO,MAAM,CAAC;QACnC,IAAI,MAAM,KAAK,SAAS;YAAE,OAAO,WAAW,CAAC;QAG7C,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;YAAE,OAAO,OAAO,CAAC;QAGzC,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC;YAAE,OAAO,eAAe,CAAC;QAGxD,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;YAAE,OAAO,YAAY,CAAC;QAGlD,MAAM,IAAI,GAAG,OAAO,MAAM,CAAC;QAC3B,IAAI,IAAI,KAAK,QAAQ,IAAI,IAAI,KAAK,QAAQ,IAAI,IAAI,KAAK,SAAS,EAAE,CAAC;YACjE,OAAO,IAAI,CAAC;QACd,CAAC;QAGD,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC;YAAE,OAAO,OAAO,CAAC;QAG1C,IAAI,IAAI,KAAK,QAAQ;YAAE,OAAO,QAAQ,CAAC;QAEvC,OAAO,QAAQ,CAAC;IAClB,CAAC;IAKD,cAAc,CAAC,MAAW;QACxB,OAAO,CACL,MAAM;YACN,OAAO,MAAM,KAAK,QAAQ;YAC1B,MAAM,IAAI,MAAM;YAChB,KAAK,IAAI,MAAM;YACf,MAAM,IAAI,MAAM;YAChB,OAAO,MAAM,CAAC,IAAI,KAAK,QAAQ;YAC/B,OAAO,MAAM,CAAC,GAAG,KAAK,QAAQ,CAC/B,CAAC;IACJ,CAAC;IAKD,OAAO,CAAC,MAAW;QACjB,OAAO,MAAM,YAAY,KAAK,CAAC;IACjC,CAAC;IAKD,WAAW,CAAC,MAAW;QACrB,OAAO,CACL,MAAM;YACN,OAAO,MAAM,KAAK,QAAQ;YAC1B,SAAS,IAAI,MAAM;YACnB,WAAW,IAAI,MAAM;YACrB,OAAO,MAAM,CAAC,OAAO,KAAK,SAAS;YACnC,CAAC,MAAM,CAAC,SAAS,YAAY,IAAI,IAAI,OAAO,MAAM,CAAC,SAAS,KAAK,QAAQ,CAAC,CAC3E,CAAC;IACJ,CAAC;IAKD,eAAe,CAAC,MAAW;QACzB,OAAO,CACL,MAAM;YACN,OAAO,MAAM,KAAK,QAAQ;YAC1B,CAAC,MAAM,IAAI,MAAM,IAAI,MAAM,IAAI,MAAM,CAAC;YACtC,CAAC,OAAO,IAAI,MAAM,IAAI,OAAO,IAAI,MAAM,CAAC;YACxC,CAAC,MAAM,IAAI,MAAM,IAAI,SAAS,IAAI,MAAM,IAAI,SAAS,IAAI,MAAM,CAAC;YAChE,CAAC,UAAU,IAAI,MAAM,IAAI,MAAM,IAAI,MAAM,CAAC,CAC3C,CAAC;IACJ,CAAC;IAKD,OAAO,CAAC,MAAW;QACjB,IAAI,MAAM,KAAK,IAAI,IAAI,MAAM,KAAK,SAAS;YAAE,OAAO,IAAI,CAAC;QACzD,IAAI,OAAO,MAAM,KAAK,QAAQ,IAAI,MAAM,CAAC,IAAI,EAAE,KAAK,EAAE;YAAE,OAAO,IAAI,CAAC;QACpE,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,IAAI,CAAC;QAC9D,IAAI,OAAO,MAAM,KAAK,QAAQ,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,IAAI,CAAC;QAChF,OAAO,KAAK,CAAC;IACf,CAAC;IAKD,kBAAkB,CAAC,MAAW;QAC5B,MAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;QAErC,QAAQ,IAAI,EAAE,CAAC;YACb,KAAK,MAAM,CAAC;YACZ,KAAK,WAAW;gBACd,OAAO,KAAK,CAAC;YACf,KAAK,QAAQ;gBACX,OAAO,YAAY,MAAM,CAAC,MAAM,GAAG,CAAC;YACtC,KAAK,QAAQ;gBACX,OAAO,OAAO,MAAM,GAAG,CAAC;YAC1B,KAAK,SAAS;gBACZ,OAAO,QAAQ,MAAM,GAAG,CAAC;YAC3B,KAAK,OAAO;gBACV,OAAO,WAAW,MAAM,CAAC,MAAM,GAAG,CAAC;YACrC,KAAK,QAAQ;gBACX,IAAI,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,EAAE,CAAC;oBACjC,OAAO,aAAa,MAAM,CAAC,KAAK,IAAI,MAAM,CAAC,KAAK,IAAI,IAAI,GAAG,CAAC;gBAC9D,CAAC;gBACD,OAAO,YAAY,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,MAAM,GAAG,CAAC;YACnD,KAAK,eAAe;gBAClB,OAAO,gBAAgB,MAAM,CAAC,IAAI,GAAG,CAAC;YACxC,KAAK,YAAY;gBACf,OAAO,cAAc,MAAM,CAAC,OAAO,GAAG,CAAC;YACzC,KAAK,OAAO;gBACV,OAAO,OAAO,MAAM,CAAC,OAAO,IAAI,MAAM,GAAG,CAAC;YAC5C;gBACE,OAAO,SAAS,IAAI,GAAG,CAAC;QAC5B,CAAC;IACH,CAAC;IAKD,gBAAgB,CAAC,KAAU;QACzB,IAAI,KAAK,YAAY,KAAK,EAAE,CAAC;YAC3B,OAAO;gBACL,OAAO,EAAE,KAAK,CAAC,OAAO,IAAI,MAAM;gBAChC,MAAM,EAAE,CAAC,KAAK,CAAC,OAAO,IAAI,MAAM,CAAC;gBACjC,IAAI,EAAE,GAAG;aACV,CAAC;QACJ,CAAC;QAED,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;YAC9B,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,CAAC,KAAK,CAAC;gBACf,IAAI,EAAE,GAAG;aACV,CAAC;QACJ,CAAC;QAED,IAAI,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;YAEvC,IAAI,QAAQ,IAAI,KAAK,IAAI,SAAS,IAAI,KAAK,EAAE,CAAC;gBAC5C,OAAO;oBACL,OAAO,EAAE,KAAK,CAAC,OAAO,IAAI,MAAM;oBAChC,MAAM,EAAE,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,IAAI,MAAM,CAAC;oBAC9E,IAAI,EAAE,KAAK,CAAC,MAAM,IAAI,GAAG;iBAC1B,CAAC;YACJ,CAAC;YAGD,IAAI,QAAQ,IAAI,KAAK,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE,CAAC;gBACrD,OAAO;oBACL,OAAO,EAAE,QAAQ;oBACjB,MAAM,EAAE,KAAK,CAAC,MAAM;oBACpB,IAAI,EAAE,GAAG;iBACV,CAAC;YACJ,CAAC;YAGD,IAAI,SAAS,IAAI,KAAK,IAAI,KAAK,CAAC,OAAO,KAAK,KAAK,EAAE,CAAC;gBAClD,OAAO;oBACL,OAAO,EAAE,KAAK,CAAC,OAAO,IAAI,MAAM;oBAChC,MAAM,EAAE,KAAK,CAAC,MAAM,IAAI,CAAC,KAAK,CAAC,OAAO,IAAI,MAAM,CAAC;oBACjD,IAAI,EAAE,GAAG;iBACV,CAAC;YACJ,CAAC;YAGD,OAAO;gBACL,OAAO,EAAE,KAAK,CAAC,OAAO,IAAI,MAAM;gBAChC,MAAM,EAAE,CAAC,KAAK,CAAC,OAAO,IAAI,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;gBAChD,IAAI,EAAE,GAAG;aACV,CAAC;QACJ,CAAC;QAED,OAAO;YACL,OAAO,EAAE,MAAM;YACf,MAAM,EAAE,CAAC,UAAU,CAAC;YACpB,IAAI,EAAE,GAAG;SACV,CAAC;IACJ,CAAC;IAKD,aAAa,CAAC,MAAW;QACvB,MAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;QAErC,QAAQ,IAAI,EAAE,CAAC;YACb,KAAK,MAAM,CAAC;YACZ,KAAK,WAAW;gBACd,OAAO,IAAI,CAAC;YAEd,KAAK,eAAe;gBAElB,OAAO,MAAM,CAAC,IAAI,CAAC;YAErB,KAAK,YAAY;gBAEf,OAAO,MAAM,CAAC,IAAI,CAAC;YAErB,KAAK,OAAO;gBAEV,OAAO,IAAI,CAAC;YAEd;gBAEE,OAAO,MAAM,CAAC;QAClB,CAAC;IACH,CAAC;IAKD,oBAAoB,CAAC,MAAW;QAC9B,OAAO,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;IACzF,CAAC;IAKD,gBAAgB,CAAC,MAAW;QAC1B,MAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;QAErC,QAAQ,IAAI,EAAE,CAAC;YACb,KAAK,OAAO;gBACV,OAAO,KAAK,CAAC;YACf,KAAK,YAAY;gBACf,OAAO,MAAM,CAAC,OAAO,KAAK,IAAI,CAAC;YACjC,KAAK,eAAe;gBAClB,OAAO,MAAM,CAAC,IAAI,IAAI,GAAG,IAAI,MAAM,CAAC,IAAI,GAAG,GAAG,CAAC;YACjD,KAAK,MAAM,CAAC;YACZ,KAAK,WAAW;gBACd,OAAO,IAAI,CAAC;YACd;gBACE,OAAO,IAAI,CAAC;QAChB,CAAC;IACH,CAAC;IAKD,kBAAkB,CAAC,MAAW;QAC5B,MAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;QAErC,QAAQ,IAAI,EAAE,CAAC;YACb,KAAK,YAAY;gBACf,OAAO,MAAM,CAAC,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;YAC9D,KAAK,eAAe;gBAClB,OAAO,MAAM,CAAC,GAAG,IAAI,MAAM,CAAC;YAC9B,KAAK,OAAO;gBACV,OAAO,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC;YAClC,KAAK,MAAM,CAAC;YACZ,KAAK,WAAW;gBACd,OAAO,MAAM,CAAC;YAChB;gBACE,OAAO,MAAM,CAAC;QAClB,CAAC;IACH,CAAC;CACF,CAAA;AA5QY,kEAA2B;sCAA3B,2BAA2B;IADvC,IAAA,mBAAU,GAAE;GACA,2BAA2B,CA4QvC"}