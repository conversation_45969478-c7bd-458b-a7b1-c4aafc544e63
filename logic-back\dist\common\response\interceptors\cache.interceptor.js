"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CacheWarmupInterceptor = exports.CacheClearInterceptor = exports.CacheInterceptor = void 0;
const common_1 = require("@nestjs/common");
const core_1 = require("@nestjs/core");
const rxjs_1 = require("rxjs");
const operators_1 = require("rxjs/operators");
const response_cache_service_1 = require("../services/response-cache.service");
const logger_service_1 = require("../../logger/logger.service");
const cache_decorator_1 = require("../decorators/cache.decorator");
let CacheInterceptor = class CacheInterceptor {
    cacheService;
    reflector;
    loggerService;
    constructor(cacheService, reflector, loggerService) {
        this.cacheService = cacheService;
        this.reflector = reflector;
        this.loggerService = loggerService;
    }
    async intercept(context, next) {
        const request = context.switchToHttp().getRequest();
        const handler = context.getHandler();
        const cacheConfig = this.reflector.get(cache_decorator_1.CACHE_CONFIG_KEY, handler);
        if (!cacheConfig || !cacheConfig.enabled) {
            return next.handle();
        }
        if (!this.shouldUseCache(request, cacheConfig)) {
            return next.handle();
        }
        const method = request.method;
        const path = request.path;
        const params = this.extractParams(request, cacheConfig);
        const userId = this.extractUserId(request, cacheConfig);
        const requestId = this.generateRequestId();
        try {
            const cachedResponse = await this.cacheService.get(method, path, params, userId, cacheConfig);
            if (cachedResponse) {
                this.loggerService.logResponseProcessing({
                    requestId,
                    stage: 'interceptor',
                    action: 'cache_hit',
                    details: {
                        method,
                        path,
                        userId,
                        cacheKey: this.generateCacheKey(method, path, params, userId),
                    }
                });
                return (0, rxjs_1.of)(cachedResponse);
            }
            return next.handle().pipe((0, operators_1.tap)(async (response) => {
                try {
                    if (this.shouldCacheResponse(response, cacheConfig)) {
                        await this.cacheService.set(method, path, response, params, userId, cacheConfig.defaultTtl, cacheConfig);
                        this.loggerService.logResponseProcessing({
                            requestId,
                            stage: 'interceptor',
                            action: 'cache_set',
                            details: {
                                method,
                                path,
                                userId,
                                ttl: cacheConfig.defaultTtl,
                                responseSize: JSON.stringify(response).length,
                            }
                        });
                    }
                }
                catch (error) {
                    this.loggerService.error('Failed to cache response', error.stack, 'CacheInterceptor');
                }
            }));
        }
        catch (error) {
            this.loggerService.error('Failed to read from cache', error.stack, 'CacheInterceptor');
            return next.handle();
        }
    }
    shouldUseCache(request, config) {
        if (config.condition && !config.condition(request)) {
            return false;
        }
        if (request.method !== 'GET' && !config.includeParams) {
            return false;
        }
        const cacheControl = request.get('Cache-Control');
        if (cacheControl && cacheControl.includes('no-cache')) {
            return false;
        }
        return true;
    }
    shouldCacheResponse(response, config) {
        if (config.condition && !config.condition(null, response)) {
            return false;
        }
        if (response.success === false || response.code >= 400) {
            return false;
        }
        if (!response.data && !config.includeParams) {
            return false;
        }
        const responseSize = JSON.stringify(response).length;
        if (config.maxSize && responseSize > config.maxSize) {
            return false;
        }
        return true;
    }
    extractParams(request, config) {
        if (!config.includeParams) {
            return null;
        }
        return {
            query: request.query,
            params: request.params,
        };
    }
    extractUserId(request, config) {
        if (!config.includeUserId) {
            return undefined;
        }
        const user = request.user;
        return user?.id || user?.userId;
    }
    generateCacheKey(method, path, params, userId) {
        const paramsStr = params ? JSON.stringify(params) : '';
        const userStr = userId ? `_user:${userId}` : '';
        return `${method}:${path}${userStr}${paramsStr}`;
    }
    generateRequestId() {
        return `cache_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
};
exports.CacheInterceptor = CacheInterceptor;
exports.CacheInterceptor = CacheInterceptor = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [response_cache_service_1.ResponseCacheService,
        core_1.Reflector,
        logger_service_1.LoggerService])
], CacheInterceptor);
let CacheClearInterceptor = class CacheClearInterceptor {
    cacheService;
    reflector;
    loggerService;
    constructor(cacheService, reflector, loggerService) {
        this.cacheService = cacheService;
        this.reflector = reflector;
        this.loggerService = loggerService;
    }
    intercept(context, next) {
        const request = context.switchToHttp().getRequest();
        const handler = context.getHandler();
        const cacheConfig = this.reflector.get(cache_decorator_1.CACHE_CONFIG_KEY, handler);
        return next.handle().pipe((0, operators_1.tap)(async (response) => {
            if (response.success !== false && response.code < 400) {
                await this.clearRelatedCache(request, cacheConfig);
            }
        }));
    }
    async clearRelatedCache(request, config) {
        if (!config || !config.tags) {
            return;
        }
        try {
            for (const tag of config.tags) {
                await this.cacheService.deleteByPattern(`*:${tag}:*`);
            }
            this.loggerService.logResponseProcessing({
                requestId: this.generateRequestId(),
                stage: 'interceptor',
                action: 'cache_clear',
                details: {
                    method: request.method,
                    path: request.path,
                    tags: config.tags,
                }
            });
        }
        catch (error) {
            this.loggerService.error('Failed to clear cache', error.stack, 'CacheClearInterceptor');
        }
    }
    generateRequestId() {
        return `cache_clear_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
};
exports.CacheClearInterceptor = CacheClearInterceptor;
exports.CacheClearInterceptor = CacheClearInterceptor = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [response_cache_service_1.ResponseCacheService,
        core_1.Reflector,
        logger_service_1.LoggerService])
], CacheClearInterceptor);
let CacheWarmupInterceptor = class CacheWarmupInterceptor {
    cacheService;
    loggerService;
    constructor(cacheService, loggerService) {
        this.cacheService = cacheService;
        this.loggerService = loggerService;
    }
    intercept(context, next) {
        const request = context.switchToHttp().getRequest();
        return next.handle().pipe((0, operators_1.tap)(async (response) => {
            this.warmupRelatedCache(request, response);
        }));
    }
    async warmupRelatedCache(request, response) {
        try {
            this.loggerService.logResponseProcessing({
                requestId: this.generateRequestId(),
                stage: 'interceptor',
                action: 'cache_warmup',
                details: {
                    method: request.method,
                    path: request.path,
                }
            });
        }
        catch (error) {
            this.loggerService.error('Failed to warmup cache', error.stack, 'CacheWarmupInterceptor');
        }
    }
    generateRequestId() {
        return `cache_warmup_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
};
exports.CacheWarmupInterceptor = CacheWarmupInterceptor;
exports.CacheWarmupInterceptor = CacheWarmupInterceptor = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [response_cache_service_1.ResponseCacheService,
        logger_service_1.LoggerService])
], CacheWarmupInterceptor);
//# sourceMappingURL=cache.interceptor.js.map