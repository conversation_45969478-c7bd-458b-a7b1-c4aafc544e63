{"version": 3, "file": "response-manager.service.js", "sourceRoot": "", "sources": ["../../../../src/common/response/services/response-manager.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,2CAAmD;AACnD,+BAAoC;AACpC,gEAA4D;AAC5D,+EAAyE;AACzE,yEAW0C;AAOnC,IAAM,sBAAsB,GAA5B,MAAM,sBAAsB;IAGJ;IAFrB,OAAO,GAA0B,IAAI,CAAC;IAE9C,YAA6B,aAA4B;QAA5B,kBAAa,GAAb,aAAa,CAAe;IAAG,CAAC;IAK7D,OAAO,CACL,IAAQ,EACR,UAAkB,oCAAe,CAAC,OAAO,EACzC,MAAuB;QAEvB,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,MAAM,QAAQ,GAAuB;YACnC,OAAO,EAAE,IAAI;YACb,IAAI,EAAE,gCAAW,CAAC,OAAO;YACzB,OAAO;YACP,IAAI;YACJ,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC;QAGF,IAAI,MAAM,EAAE,oBAAoB,KAAK,KAAK,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YAC3D,QAAQ,CAAC,aAAa,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC;QAC/D,CAAC;QAGD,IAAI,MAAM,EAAE,gBAAgB,KAAK,KAAK,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACvD,QAAQ,CAAC,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC;QAC9C,CAAC;QAGD,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,IAAI,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC,CAAC;QAEzD,OAAO,QAAQ,CAAC;IAClB,CAAC;IAKD,KAAK,CACH,UAAkB,oCAAe,CAAC,cAAc,EAChD,MAAiB,EACjB,MAAuB;QAEvB,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,MAAM,QAAQ,GAAqB;YACjC,OAAO,EAAE,KAAK;YACd,IAAI,EAAE,gCAAW,CAAC,qBAAqB;YACvC,OAAO;YACP,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC;QAEF,IAAI,MAAM,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAChC,QAAQ,CAAC,MAAM,GAAG,MAAM,CAAC;QAC3B,CAAC;QAGD,IAAI,MAAM,EAAE,gBAAgB,KAAK,KAAK,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACvD,QAAQ,CAAC,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC;QAC9C,CAAC;QAGD,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC,CAAC;QAE9D,OAAO,QAAQ,CAAC;IAClB,CAAC;IAKD,SAAS,CACP,IAAS,EACT,UAIC,EACD,UAAkB,oCAAe,CAAC,OAAO,EACzC,MAAuB;QAEvB,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC7B,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC;QACjE,MAAM,OAAO,GAAG,UAAU,CAAC,IAAI,GAAG,UAAU,CAAC;QAC7C,MAAM,OAAO,GAAG,UAAU,CAAC,IAAI,GAAG,CAAC,CAAC;QAEpC,MAAM,QAAQ,GAAyB;YACrC,OAAO,EAAE,IAAI;YACb,IAAI,EAAE,gCAAW,CAAC,OAAO;YACzB,OAAO;YACP,IAAI;YACJ,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,UAAU,EAAE;gBACV,GAAG,UAAU;gBACb,UAAU;gBACV,OAAO;gBACP,OAAO;aACR;SACF,CAAC;QAGF,IAAI,MAAM,EAAE,oBAAoB,KAAK,KAAK,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YAC3D,QAAQ,CAAC,aAAa,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC;QAC/D,CAAC;QAGD,IAAI,MAAM,EAAE,gBAAgB,KAAK,KAAK,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACvD,QAAQ,CAAC,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC;QAC9C,CAAC;QAGD,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,EAAE,SAAS,EAAE,IAAI,CAAC,MAAM,EAAE,UAAU,EAAE,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC,CAAC;QAE3F,OAAO,QAAQ,CAAC;IAClB,CAAC;IAKD,MAAM,CACJ,IAAY,EACZ,OAAe,EACf,IAAQ,EACR,MAAuB;QAEvB,MAAM,QAAQ,GAAoB;YAChC,IAAI;YACJ,OAAO;YACP,IAAI;YACJ,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC;QAGF,IAAI,MAAM,EAAE,gBAAgB,KAAK,KAAK,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACvD,QAAQ,CAAC,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC;QAC9C,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAKD,UAAU,CAAC,OAAgC;QACzC,IAAI,CAAC,OAAO,GAAG;YACb,SAAS,EAAE,OAAO,CAAC,SAAS,IAAI,IAAA,SAAM,GAAE;YACxC,SAAS,EAAE,OAAO,CAAC,SAAS,IAAI,IAAI,CAAC,GAAG,EAAE;YAC1C,IAAI,EAAE,OAAO,CAAC,IAAI,IAAI,EAAE;YACxB,MAAM,EAAE,OAAO,CAAC,MAAM,IAAI,EAAE;YAC5B,GAAG,OAAO;SACX,CAAC;IACJ,CAAC;IAKD,UAAU;QACR,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB,CAAC;IAKD,MAAM,CAAI,QAAyB,EAAE,SAAyB,mCAAc,CAAC,QAAQ;QACnF,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC7B,IAAI,mBAAwB,CAAC;QAE7B,QAAQ,MAAM,EAAE,CAAC;YACf,KAAK,mCAAc,CAAC,MAAM;gBACxB,mBAAmB,GAAG,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;gBAClD,MAAM;YACR,KAAK,mCAAc,CAAC,OAAO;gBACzB,mBAAmB,GAAG,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;gBACnD,MAAM;YACR,KAAK,mCAAc,CAAC,QAAQ,CAAC;YAC7B;gBACE,mBAAmB,GAAG,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;gBACpD,MAAM;QACV,CAAC;QAGD,IAAI,MAAM,KAAK,mCAAc,CAAC,QAAQ,EAAE,CAAC;YACvC,IAAI,CAAC,oBAAoB,CAAC,QAAQ,EAAE,mBAAmB,EAAE,MAAM,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC,CAAC;QAC3F,CAAC;QAED,OAAO,mBAAmB,CAAC;IAC7B,CAAC;IAKO,cAAc,CAAI,QAAyB;QACjD,OAAO,QAAQ,CAAC;IAClB,CAAC;IAKO,YAAY,CAAI,QAAyB;QAC/C,OAAO;YACL,IAAI,EAAE,QAAQ,CAAC,IAAI;YACnB,GAAG,EAAE,QAAQ,CAAC,OAAO;YACrB,IAAI,EAAE,QAAQ,CAAC,IAAI;SACpB,CAAC;IACJ,CAAC;IAKO,aAAa,CAAI,QAAyB;QAChD,IAAI,SAAS,IAAI,QAAQ,IAAI,QAAQ,CAAC,OAAO,EAAE,CAAC;YAC9C,OAAO,QAAQ,CAAC,IAAI,CAAC;QACvB,CAAC;aAAM,CAAC;YACN,OAAO;gBACL,KAAK,EAAE;oBACL,IAAI,EAAE,QAAQ,CAAC,IAAI;oBACnB,OAAO,EAAE,QAAQ,CAAC,OAAO;oBACzB,SAAS,EAAE,QAAQ,CAAC,SAAS;iBAC9B;aACF,CAAC;QACJ,CAAC;IACH,CAAC;IAKD,eAAe,CAAC,MAAgB,EAAE,UAAkB,oCAAe,CAAC,gBAAgB;QAClF,OAAO;YACL,OAAO,EAAE,KAAK;YACd,IAAI,EAAE,gCAAW,CAAC,gBAAgB;YAClC,OAAO;YACP,MAAM;YACN,SAAS,EAAE,YAAY;YACvB,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,SAAS,EAAE,IAAI,CAAC,OAAO,EAAE,SAAS;SACnC,CAAC;IACJ,CAAC;IAKD,aAAa,CAAC,OAAe,EAAE,SAAkB;QAC/C,OAAO;YACL,OAAO,EAAE,KAAK;YACd,IAAI,EAAE,gCAAW,CAAC,WAAW;YAC7B,OAAO;YACP,SAAS;YACT,SAAS,EAAE,UAAU;YACrB,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,SAAS,EAAE,IAAI,CAAC,OAAO,EAAE,SAAS;SACnC,CAAC;IACJ,CAAC;IAKD,SAAS,CAAC,UAAkB,oCAAe,CAAC,YAAY;QACtD,OAAO;YACL,OAAO,EAAE,KAAK;YACd,IAAI,EAAE,gCAAW,CAAC,YAAY;YAC9B,OAAO;YACP,SAAS,EAAE,MAAM;YACjB,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,SAAS,EAAE,IAAI,CAAC,OAAO,EAAE,SAAS;SACnC,CAAC;IACJ,CAAC;IAKD,cAAc,CAAC,UAAkB,oCAAe,CAAC,SAAS;QACxD,OAAO;YACL,OAAO,EAAE,KAAK;YACd,IAAI,EAAE,gCAAW,CAAC,SAAS;YAC3B,OAAO;YACP,SAAS,EAAE,MAAM;YACjB,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,SAAS,EAAE,IAAI,CAAC,OAAO,EAAE,SAAS;SACnC,CAAC;IACJ,CAAC;IAKD,aAAa,CAAC,UAAkB,oCAAe,CAAC,SAAS;QACvD,OAAO;YACL,OAAO,EAAE,KAAK;YACd,IAAI,EAAE,gCAAW,CAAC,SAAS;YAC3B,OAAO;YACP,SAAS,EAAE,UAAU;YACrB,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,SAAS,EAAE,IAAI,CAAC,OAAO,EAAE,SAAS;SACnC,CAAC;IACJ,CAAC;IAKD,WAAW,CAAC,UAAkB,oCAAe,CAAC,cAAc,EAAE,KAAc;QAC1E,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,MAAM,QAAQ,GAAkB;YAC9B,OAAO,EAAE,KAAK;YACd,IAAI,EAAE,gCAAW,CAAC,qBAAqB;YACvC,OAAO;YACP,SAAS,EAAE,QAAQ;YACnB,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,SAAS,EAAE,IAAI,CAAC,OAAO,EAAE,SAAS;SACnC,CAAC;QAGF,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,IAAI,KAAK,EAAE,CAAC;YACpD,QAAQ,CAAC,KAAK,GAAG,KAAK,CAAC;QACzB,CAAC;QAGD,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC,CAAC;QAE9D,OAAO,QAAQ,CAAC;IAClB,CAAC;IAKO,WAAW,CAAI,QAAyB,EAAE,YAAkB,EAAE,cAAuB;QAC3F,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YAClB,OAAO;QACT,CAAC;QAGD,IAAI,CAAC,8CAAoB,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE,CAAC;YAC/E,OAAO;QACT,CAAC;QAED,IAAI,CAAC;YAEH,MAAM,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;YAC3D,MAAM,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;YAGvD,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC;gBAChC,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS;gBACjC,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM;gBAC3B,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI;gBACvB,UAAU,EAAE,QAAQ,CAAC,IAAI;gBACzB,OAAO,EAAE,iBAAiB,CAAC,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC,IAAI,GAAG,GAAG;gBACnE,OAAO,EAAE,QAAQ,CAAC,OAAO;gBACzB,aAAa,EAAE,iBAAiB,CAAC,CAAC,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC,CAAC,SAAS;gBACrE,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM;gBAC3B,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,EAAE;gBACnB,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS;gBACjC,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM;gBAC3B,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS;gBACjC,YAAY,EAAE,8CAAoB,CAAC,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC;gBAC9D,MAAM,EAAE,eAAe,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS;gBACrD,SAAS,EAAE,eAAe,CAAC,CAAC,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS;aAC5D,CAAC,CAAC;YAGH,MAAM,MAAM,GAAG,8CAAoB,CAAC,SAAS,EAAE,CAAC;YAChD,IAAI,MAAM,CAAC,kBAAkB,EAAE,CAAC;gBAC9B,IAAI,CAAC,aAAa,CAAC,qBAAqB,CAAC;oBACvC,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS;oBACjC,KAAK,EAAE,SAAS;oBAChB,MAAM,EAAE,iBAAiB;oBACzB,QAAQ,EAAE,cAAc;oBACxB,OAAO,EAAE;wBACP,YAAY,EAAE,iBAAiB,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,QAAQ;wBACrF,OAAO,EAAE,CAAC,CAAC,QAAQ,CAAC,IAAI;wBACxB,aAAa,EAAE,QAAQ,CAAC,OAAO,EAAE,MAAM,IAAI,CAAC;wBAC5C,gBAAgB,EAAE,YAAY,CAAC,CAAC,CAAC,OAAO,YAAY,CAAC,CAAC,CAAC,MAAM;qBAC9D;iBACF,CAAC,CAAC;YACL,CAAC;YAGD,MAAM,kBAAkB,GAAG,iBAAiB,IAAI,OAAO,QAAQ,CAAC,aAAa,KAAK,QAAQ;gBACxF,CAAC,CAAC,QAAQ,CAAC,aAAa;gBACxB,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;YAE/E,IAAI,kBAAkB,IAAI,OAAO,kBAAkB,KAAK,QAAQ,IAAI,MAAM,CAAC,oBAAoB,IAAI,kBAAkB,IAAI,MAAM,CAAC,oBAAoB,EAAE,CAAC;gBACrJ,IAAI,CAAC,aAAa,CAAC,cAAc,CAC/B,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,EAC7C,kBAAkB,EAClB;oBACE,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS;oBACjC,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM;oBAC3B,YAAY,EAAE,QAAQ,CAAC,IAAI;oBAC3B,QAAQ,EAAE,iBAAiB,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC,IAAI,IAAI,GAAG;oBACtE,cAAc;oBACd,YAAY,EAAE,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,MAAM;iBAC9C,CACF,CAAC;YACJ,CAAC;QAEH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YAEf,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,wBAAwB,EAAE,KAAK,CAAC,KAAK,EAAE,iBAAiB,CAAC,CAAC;QACrF,CAAC;IACH,CAAC;IAKO,iBAAiB,CAAI,QAAyB;QACpD,OAAO,SAAS,IAAI,QAAQ,IAAI,eAAe,IAAI,QAAQ,CAAC;IAC9D,CAAC;IAKO,eAAe,CAAI,QAAyB;QAClD,OAAO,SAAS,IAAI,QAAQ,IAAI,QAAQ,IAAI,QAAQ,IAAI,WAAW,IAAI,QAAQ,CAAC;IAClF,CAAC;IAKO,oBAAoB,CAC1B,gBAAiC,EACjC,mBAAwB,EACxB,MAAsB,EACtB,cAAuB;QAEvB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YAClB,OAAO;QACT,CAAC;QAGD,MAAM,MAAM,GAAG,8CAAoB,CAAC,SAAS,EAAE,CAAC;QAChD,IAAI,CAAC,MAAM,CAAC,kBAAkB,EAAE,CAAC;YAC/B,OAAO;QACT,CAAC;QAED,IAAI,CAAC;YACH,IAAI,CAAC,aAAa,CAAC,oBAAoB,CAAC;gBACtC,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS;gBACjC,UAAU,EAAE,UAAU;gBACtB,QAAQ,EAAE,MAAM;gBAChB,YAAY,EAAE,8CAAoB,CAAC,YAAY,CAAC,gBAAgB,CAAC;gBACjE,eAAe,EAAE,8CAAoB,CAAC,YAAY,CAAC,mBAAmB,CAAC;gBACvE,QAAQ,EAAE,cAAc;aACzB,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC,KAAK,EAAE,iBAAiB,CAAC,CAAC;QAC/F,CAAC;IACH,CAAC;IAKD,gBAAgB,CAAC,MAA6C,EAAE,QAAiB,EAAE,GAAY;QAC7F,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YAClB,OAAO;QACT,CAAC;QAGD,MAAM,MAAM,GAAG,8CAAoB,CAAC,SAAS,EAAE,CAAC;QAChD,IAAI,CAAC,MAAM,CAAC,kBAAkB,EAAE,CAAC;YAC/B,OAAO;QACT,CAAC;QAED,IAAI,CAAC;YACH,IAAI,CAAC,aAAa,CAAC,gBAAgB,CAAC;gBAClC,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS;gBACjC,MAAM;gBACN,QAAQ;gBACR,GAAG;aACJ,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC,KAAK,EAAE,iBAAiB,CAAC,CAAC;QAC3F,CAAC;IACH,CAAC;CACF,CAAA;AA5dY,wDAAsB;iCAAtB,sBAAsB;IADlC,IAAA,mBAAU,EAAC,EAAE,KAAK,EAAE,cAAK,CAAC,OAAO,EAAE,CAAC;qCAIS,8BAAa;GAH9C,sBAAsB,CA4dlC"}