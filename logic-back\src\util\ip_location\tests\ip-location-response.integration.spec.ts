import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { IpLocationModule } from '../ip-location.module';
import { ResponseModule } from '../../../common/response/response.module';

describe('IpLocationController with Response Manager (Integration)', () => {
  let app: INestApplication;

  beforeEach(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [IpLocationModule, ResponseModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();
  });

  afterEach(async () => {
    await app.close();
  });

  describe('GET /ip-location/query', () => {
    it('should return standardized response format', async () => {
      const response = await request(app.getHttpServer())
        .get('/ip-location/query')
        .query({ ip: '**************' })
        .expect(200);

      // 验证标准响应格式
      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('code', 200);
      expect(response.body).toHaveProperty('message');
      expect(response.body).toHaveProperty('data');
      expect(response.body).toHaveProperty('timestamp');
      expect(response.body).toHaveProperty('requestId');

      // 验证数据结构
      expect(response.body.data).toHaveProperty('location');
      expect(response.body.data).toHaveProperty('meta');
      expect(response.body.data.meta).toHaveProperty('ip', '**************');
    });

    it('should handle invalid IP address', async () => {
      const response = await request(app.getHttpServer())
        .get('/ip-location/query')
        .query({ ip: 'invalid-ip' })
        .expect(400);

      // 验证错误响应格式
      expect(response.body).toHaveProperty('success', false);
      expect(response.body).toHaveProperty('code', 400);
      expect(response.body).toHaveProperty('message');
      expect(response.body).toHaveProperty('errorType');
    });

    it('should use cache on repeated requests', async () => {
      const ip = '*******';
      
      // 第一次请求
      const firstResponse = await request(app.getHttpServer())
        .get('/ip-location/query')
        .query({ ip })
        .expect(200);

      // 第二次请求（应该从缓存获取）
      const secondResponse = await request(app.getHttpServer())
        .get('/ip-location/query')
        .query({ ip })
        .expect(200);

      // 验证响应格式一致
      expect(firstResponse.body.data.location).toEqual(secondResponse.body.data.location);
      
      // 第二次请求可能从缓存获取，执行时间应该更短
      expect(secondResponse.body.data.meta).toHaveProperty('fromCache');
    });
  });

  describe('POST /ip-location/check-risk', () => {
    it('should return risk assessment with standard format', async () => {
      const riskRequest = {
        userId: 123,
        ipAddress: '**************',
        userAgent: 'Mozilla/5.0 (Test Browser)',
        sessionId: 'test-session-123'
      };

      const response = await request(app.getHttpServer())
        .post('/ip-location/check-risk')
        .send(riskRequest)
        .expect(200);

      // 验证标准响应格式
      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('code', 200);
      expect(response.body).toHaveProperty('data');

      // 验证风险评估数据
      expect(response.body.data).toHaveProperty('riskAssessment');
      expect(response.body.data).toHaveProperty('meta');
      expect(response.body.data.meta).toHaveProperty('userId', 123);
    });

    it('should validate required parameters', async () => {
      const invalidRequest = {
        // 缺少必需参数
        userAgent: 'Mozilla/5.0 (Test Browser)'
      };

      const response = await request(app.getHttpServer())
        .post('/ip-location/check-risk')
        .send(invalidRequest)
        .expect(400);

      // 验证验证错误响应
      expect(response.body).toHaveProperty('success', false);
      expect(response.body).toHaveProperty('errorType', 'VALIDATION');
      expect(response.body).toHaveProperty('errors');
      expect(Array.isArray(response.body.errors)).toBe(true);
    });
  });

  describe('GET /ip-location/user/:userId/stats', () => {
    it('should return user location statistics', async () => {
      const userId = 123;
      
      const response = await request(app.getHttpServer())
        .get(`/ip-location/user/${userId}/stats`)
        .query({ days: 30 })
        .expect(200);

      // 验证标准响应格式
      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('data');

      // 验证统计数据
      expect(response.body.data).toHaveProperty('statistics');
      expect(response.body.data).toHaveProperty('meta');
      expect(response.body.data.meta).toHaveProperty('userId', userId);
    });

    it('should validate userId parameter', async () => {
      const response = await request(app.getHttpServer())
        .get('/ip-location/user/invalid/stats')
        .expect(400);

      expect(response.body).toHaveProperty('success', false);
      expect(response.body).toHaveProperty('errorType', 'VALIDATION');
    });

    it('should validate days parameter range', async () => {
      const response = await request(app.getHttpServer())
        .get('/ip-location/user/123/stats')
        .query({ days: 500 }) // 超出范围
        .expect(400);

      expect(response.body).toHaveProperty('success', false);
      expect(response.body).toHaveProperty('errorType', 'VALIDATION');
    });
  });

  describe('POST /ip-location/user/:userId/trust', () => {
    it('should set trusted location', async () => {
      const userId = 123;
      const trustRequest = {
        province: '北京市',
        city: '北京市',
        reason: '用户常用登录地'
      };

      const response = await request(app.getHttpServer())
        .post(`/ip-location/user/${userId}/trust`)
        .send(trustRequest)
        .expect(200);

      // 验证标准响应格式
      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('data');

      // 验证可信位置数据
      expect(response.body.data).toHaveProperty('trustedLocation');
      expect(response.body.data).toHaveProperty('meta');
      expect(response.body.data.meta).toHaveProperty('province', '北京市');
    });

    it('should validate required fields', async () => {
      const userId = 123;
      const invalidRequest = {
        // 缺少必需字段
        reason: '测试'
      };

      const response = await request(app.getHttpServer())
        .post(`/ip-location/user/${userId}/trust`)
        .send(invalidRequest)
        .expect(400);

      expect(response.body).toHaveProperty('success', false);
      expect(response.body).toHaveProperty('errorType', 'VALIDATION');
      expect(response.body.errors).toContain('省份不能为空');
      expect(response.body.errors).toContain('城市不能为空');
    });
  });

  describe('GET /ip-location/current', () => {
    it('should return current IP location', async () => {
      const response = await request(app.getHttpServer())
        .get('/ip-location/current')
        .expect(200);

      // 验证标准响应格式
      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('data');

      // 验证当前位置数据
      expect(response.body.data).toHaveProperty('currentLocation');
      expect(response.body.data).toHaveProperty('meta');
      expect(response.body.data.meta).toHaveProperty('clientIp');
    });
  });

  describe('GET /ip-location/health', () => {
    it('should return health status', async () => {
      const response = await request(app.getHttpServer())
        .get('/ip-location/health')
        .expect(200);

      // 验证健康检查响应
      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('data');

      // 验证健康状态数据
      expect(response.body.data).toHaveProperty('service', 'ip-location');
      expect(response.body.data).toHaveProperty('status');
      expect(response.body.data).toHaveProperty('version');
      expect(response.body.data).toHaveProperty('cache');
    });
  });

  describe('POST /ip-location/cache/clear', () => {
    it('should clear all cache', async () => {
      const response = await request(app.getHttpServer())
        .post('/ip-location/cache/clear')
        .query({ type: 'all' })
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body.data).toHaveProperty('type', 'all');
      expect(response.body.data).toHaveProperty('message');
    });

    it('should clear user-specific cache', async () => {
      const response = await request(app.getHttpServer())
        .post('/ip-location/cache/clear')
        .query({ type: 'user', target: '123' })
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body.data).toHaveProperty('type', 'user');
      expect(response.body.data).toHaveProperty('target', '123');
    });

    it('should validate required target for user cache clear', async () => {
      const response = await request(app.getHttpServer())
        .post('/ip-location/cache/clear')
        .query({ type: 'user' }) // 缺少target
        .expect(400);

      expect(response.body).toHaveProperty('success', false);
      expect(response.body).toHaveProperty('errorType', 'VALIDATION');
    });
  });

  describe('Response Format Consistency', () => {
    it('should maintain consistent response format across all endpoints', async () => {
      const endpoints = [
        { method: 'get', path: '/ip-location/query?ip=*******' },
        { method: 'get', path: '/ip-location/current' },
        { method: 'get', path: '/ip-location/health' },
      ];

      for (const endpoint of endpoints) {
        const response = await request(app.getHttpServer())
          [endpoint.method](endpoint.path)
          .expect(200);

        // 验证所有成功响应都有标准字段
        expect(response.body).toHaveProperty('success', true);
        expect(response.body).toHaveProperty('code', 200);
        expect(response.body).toHaveProperty('message');
        expect(response.body).toHaveProperty('data');
        expect(response.body).toHaveProperty('timestamp');
        expect(response.body).toHaveProperty('requestId');
      }
    });
  });

  describe('Cache Behavior', () => {
    it('should respect cache TTL settings', async () => {
      const ip = '*******';
      
      // 第一次请求
      const firstResponse = await request(app.getHttpServer())
        .get('/ip-location/query')
        .query({ ip })
        .expect(200);

      // 立即第二次请求（应该从缓存获取）
      const secondResponse = await request(app.getHttpServer())
        .get('/ip-location/query')
        .query({ ip })
        .expect(200);

      // 验证缓存行为
      expect(firstResponse.body.data.location).toEqual(secondResponse.body.data.location);
    });
  });
});
