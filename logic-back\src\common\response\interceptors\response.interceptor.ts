import {
  Injectable,
  NestInterceptor,
  ExecutionContext,
  CallHandler,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { Request } from 'express';
import { ResponseManagerService } from '../services/response-manager.service';
import { RESPONSE_CONFIG_KEY, ResponseDecoratorConfig } from '../decorators/response.decorator';
import { BaseResponse, ResponseFormat } from '../interfaces/response.interface';

/**
 * 响应拦截器
 * 自动处理响应格式化和上下文设置
 */
@Injectable()
export class ResponseInterceptor implements NestInterceptor {
  constructor(
    private readonly responseManager: ResponseManagerService,
    private readonly reflector: Reflector,
  ) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest<Request>();
    const handler = context.getHandler();
    
    // 获取响应配置
    const config = this.reflector.get<ResponseDecoratorConfig>(
      RESPONSE_CONFIG_KEY,
      handler,
    ) || {};

    // 设置请求上下文
    this.responseManager.setContext({
      requestId: this.generateRequestId(),
      startTime: Date.now(),
      path: request.path,
      method: request.method,
      ip: request.ip,
      userAgent: request.get('User-Agent'),
      userId: (request as any).user?.id,
    });

    return next.handle().pipe(
      map((data) => {
        // 如果返回的已经是标准响应格式，直接返回
        if (this.isStandardResponse(data)) {
          return this.responseManager.format(data, config.format);
        }

        // 如果是null或undefined，返回成功响应
        if (data === null || data === undefined) {
          const response = this.responseManager.success(
            null,
            config.successMessage,
            {
              includeExecutionTime: config.includeExecutionTime,
              includeRequestId: config.includeRequestId,
            }
          );
          return this.responseManager.format(response, config.format);
        }

        // 检查是否是分页数据
        if (this.isPaginatedData(data)) {
          const response = this.responseManager.paginated(
            data.data || data.list || [],
            {
              page: data.pagination?.page || data.page || 1,
              size: data.pagination?.size || data.size || 10,
              total: data.pagination?.total || data.total || 0,
            },
            config.successMessage,
            {
              includeExecutionTime: config.includeExecutionTime,
              includeRequestId: config.includeRequestId,
            }
          );
          return this.responseManager.format(response, config.format);
        }

        // 普通成功响应
        const response = this.responseManager.success(
          data,
          config.successMessage,
          {
            includeExecutionTime: config.includeExecutionTime,
            includeRequestId: config.includeRequestId,
          }
        );
        return this.responseManager.format(response, config.format);
      }),
    );
  }

  /**
   * 检查是否是标准响应格式
   */
  private isStandardResponse(data: any): data is BaseResponse {
    return (
      data &&
      typeof data === 'object' &&
      'code' in data &&
      'message' in data &&
      'timestamp' in data
    );
  }

  /**
   * 检查是否是分页数据
   */
  private isPaginatedData(data: any): boolean {
    return (
      data &&
      typeof data === 'object' &&
      (
        ('data' in data && 'pagination' in data) ||
        ('list' in data && 'pagination' in data) ||
        ('data' in data && 'page' in data && 'total' in data) ||
        ('list' in data && 'page' in data && 'total' in data)
      )
    );
  }

  /**
   * 生成请求ID
   */
  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

/**
 * 全局响应拦截器
 * 用于全局应用响应格式化
 */
@Injectable()
export class GlobalResponseInterceptor implements NestInterceptor {
  constructor(private readonly responseManager: ResponseManagerService) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest<Request>();
    
    // 排除特定路径
    const excludePaths = [
      '/health',
      '/metrics',
      '/weixin/message',
      '/api-docs',
      '/swagger',
    ];

    if (excludePaths.some(path => request.path.includes(path))) {
      return next.handle();
    }

    // 设置请求上下文
    this.responseManager.setContext({
      requestId: this.generateRequestId(),
      startTime: Date.now(),
      path: request.path,
      method: request.method,
      ip: request.ip,
      userAgent: request.get('User-Agent'),
      userId: (request as any).user?.id,
    });

    return next.handle().pipe(
      map((data) => {
        // 如果已经是标准格式，直接返回
        if (this.isStandardResponse(data)) {
          return data;
        }

        // 转换为标准格式
        return this.responseManager.success(data);
      }),
    );
  }

  /**
   * 检查是否是标准响应格式
   */
  private isStandardResponse(data: any): boolean {
    return (
      data &&
      typeof data === 'object' &&
      'code' in data &&
      'message' in data &&
      'timestamp' in data
    );
  }

  /**
   * 生成请求ID
   */
  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}
