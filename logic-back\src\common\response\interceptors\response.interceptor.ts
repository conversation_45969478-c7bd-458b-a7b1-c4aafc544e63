import {
  Injectable,
  NestInterceptor,
  ExecutionContext,
  CallHandler,
  Optional,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { Request } from 'express';
import { LoggerService } from '../../logger/logger.service';
import { ResponseManagerService } from '../services/response-manager.service';
import { RESPONSE_CONFIG_KEY, ResponseDecoratorConfig } from '../decorators/response.decorator';
import { BaseResponse, ResponseFormat } from '../interfaces/response.interface';

/**
 * 响应拦截器
 * 自动处理响应格式化和上下文设置
 */
@Injectable()
export class ResponseInterceptor implements NestInterceptor {
  constructor(
    private readonly responseManager: ResponseManagerService,
    private readonly reflector: Reflector,
    private readonly loggerService: LoggerService,
  ) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest<Request>();
    const handler = context.getHandler();
    const controllerClass = context.getClass();
    const methodName = handler.name;

    // 获取响应配置
    const config = this.reflector.get<ResponseDecoratorConfig>(
      RESPONSE_CONFIG_KEY,
      handler,
    ) || {};

    // 设置请求上下文
    const requestId = this.generateRequestId();
    const startTime = Date.now();

    this.responseManager.setContext({
      requestId,
      startTime,
      path: request.path,
      method: request.method,
      ip: request.ip,
      userAgent: request.get('User-Agent'),
      userId: (request as any).user?.id,
      module: controllerClass.name,
      operation: methodName,
    });

    // 记录拦截器开始处理日志
    this.loggerService.logResponseProcessing({
      requestId,
      stage: 'interceptor',
      action: 'start_processing',
      details: {
        controller: controllerClass.name,
        method: methodName,
        path: request.path,
        httpMethod: request.method,
        hasConfig: Object.keys(config).length > 0,
        configFormat: config.format,
      }
    });

    return next.handle().pipe(
      map((data) => {
        const processingStartTime = Date.now();

        try {
          let finalResponse: any;

          // 如果返回的已经是标准响应格式，直接返回
          if (this.isStandardResponse(data)) {
            this.loggerService.logResponseProcessing({
              requestId,
              stage: 'interceptor',
              action: 'standard_response_detected',
              details: { responseType: 'standard' }
            });
            finalResponse = this.responseManager.format(data, config.format);
          }
          // 如果是null或undefined，返回成功响应
          else if (data === null || data === undefined) {
            this.loggerService.logResponseProcessing({
              requestId,
              stage: 'interceptor',
              action: 'null_data_response',
              details: { responseType: 'null' }
            });
            const response = this.responseManager.success(
              null,
              config.successMessage,
              {
                includeExecutionTime: config.includeExecutionTime,
                includeRequestId: config.includeRequestId,
              }
            );
            finalResponse = this.responseManager.format(response, config.format);
          }
          // 检查是否是分页数据
          else if (this.isPaginatedData(data)) {
            this.loggerService.logResponseProcessing({
              requestId,
              stage: 'interceptor',
              action: 'paginated_response',
              details: {
                responseType: 'paginated',
                dataCount: (data.data || data.list || []).length,
                page: data.pagination?.page || data.page || 1,
                total: data.pagination?.total || data.total || 0
              }
            });
            const response = this.responseManager.paginated(
              data.data || data.list || [],
              {
                page: data.pagination?.page || data.page || 1,
                size: data.pagination?.size || data.size || 10,
                total: data.pagination?.total || data.total || 0,
              },
              config.successMessage,
              {
                includeExecutionTime: config.includeExecutionTime,
                includeRequestId: config.includeRequestId,
              }
            );
            finalResponse = this.responseManager.format(response, config.format);
          }
          // 普通成功响应
          else {
            this.loggerService.logResponseProcessing({
              requestId,
              stage: 'interceptor',
              action: 'success_response',
              details: {
                responseType: 'success',
                hasData: !!data,
                dataType: typeof data
              }
            });
            const response = this.responseManager.success(
              data,
              config.successMessage,
              {
                includeExecutionTime: config.includeExecutionTime,
                includeRequestId: config.includeRequestId,
              }
            );
            finalResponse = this.responseManager.format(response, config.format);
          }

          // 记录拦截器完成处理日志
          this.loggerService.logResponseProcessing({
            requestId,
            stage: 'interceptor',
            action: 'complete_processing',
            duration: Date.now() - processingStartTime,
            details: {
              totalExecutionTime: Date.now() - startTime,
              finalFormat: config.format || 'standard'
            }
          });

          return finalResponse;

        } catch (error) {
          // 记录拦截器处理错误
          this.loggerService.logResponseProcessing({
            requestId,
            stage: 'interceptor',
            action: 'processing_error',
            duration: Date.now() - processingStartTime,
            error
          });
          throw error;
        }
      }),
    );
  }

  /**
   * 检查是否是标准响应格式
   */
  private isStandardResponse(data: any): data is BaseResponse {
    return (
      data &&
      typeof data === 'object' &&
      'code' in data &&
      'message' in data &&
      'timestamp' in data
    );
  }

  /**
   * 检查是否是分页数据
   */
  private isPaginatedData(data: any): boolean {
    return (
      data &&
      typeof data === 'object' &&
      (
        ('data' in data && 'pagination' in data) ||
        ('list' in data && 'pagination' in data) ||
        ('data' in data && 'page' in data && 'total' in data) ||
        ('list' in data && 'page' in data && 'total' in data)
      )
    );
  }

  /**
   * 生成请求ID
   */
  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

/**
 * 全局响应拦截器
 * 用于全局应用响应格式化
 */
@Injectable()
export class GlobalResponseInterceptor implements NestInterceptor {
  constructor(
    @Optional() private readonly responseManager: ResponseManagerService,
    @Optional() private readonly loggerService: LoggerService,
  ) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest<Request>();

    // 排除特定路径
    const excludePaths = [
      '/health',
      '/metrics',
      '/weixin/message',
      '/api-docs',
      '/swagger',
    ];

    if (excludePaths.some(path => request.path.includes(path))) {
      return next.handle();
    }

    // 检查依赖是否正确注入
    if (!this.responseManager) {
      console.error('ResponseManager is not injected in GlobalResponseInterceptor');
      return next.handle();
    }

    // 设置请求上下文
    const requestId = this.generateRequestId();
    const startTime = Date.now();

    try {
      this.responseManager.setContext({
        requestId,
        startTime,
        path: request.path,
        method: request.method,
        ip: request.ip,
        userAgent: request.get('User-Agent'),
        userId: (request as any).user?.id,
      });
    } catch (error) {
      console.error('Error setting context in GlobalResponseInterceptor:', error);
      return next.handle();
    }

    // 记录全局拦截器开始处理
    if (this.loggerService) {
      try {
        this.loggerService.logResponseProcessing({
          requestId,
          stage: 'interceptor',
          action: 'global_start_processing',
          details: {
            path: request.path,
            method: request.method,
            userAgent: request.get('User-Agent'),
            ip: request.ip,
          }
        });
      } catch (error) {
        console.error('Error logging in GlobalResponseInterceptor:', error);
      }
    }

    return next.handle().pipe(
      map((data) => {
        const processingStartTime = Date.now();

        try {
          let finalResponse: any;

          // 如果已经是标准格式，直接返回
          if (this.isStandardResponse(data)) {
            if (this.loggerService) {
              try {
                this.loggerService.logResponseProcessing({
                  requestId,
                  stage: 'interceptor',
                  action: 'global_standard_response',
                  details: { responseType: 'already_standard' }
                });
              } catch (error) {
                console.error('Error logging standard response:', error);
              }
            }
            finalResponse = data;
          } else {
            // 转换为标准格式
            if (this.loggerService) {
              try {
                this.loggerService.logResponseProcessing({
                  requestId,
                  stage: 'interceptor',
                  action: 'global_transform_response',
                  details: {
                    responseType: 'transform_to_standard',
                    originalType: typeof data
                  }
                });
              } catch (error) {
                console.error('Error logging transform response:', error);
              }
            }
            finalResponse = this.responseManager ? this.responseManager.success(data) : data;
          }

          // 记录全局拦截器完成处理
          if (this.loggerService) {
            try {
              this.loggerService.logResponseProcessing({
                requestId,
                stage: 'interceptor',
                action: 'global_complete_processing',
                duration: Date.now() - processingStartTime,
                details: {
                  totalExecutionTime: Date.now() - startTime
                }
              });
            } catch (error) {
              console.error('Error logging complete processing:', error);
            }
          }

          return finalResponse;

        } catch (error) {
          // 记录全局拦截器处理错误
          if (this.loggerService) {
            try {
              this.loggerService.logResponseProcessing({
                requestId,
                stage: 'interceptor',
                action: 'global_processing_error',
                duration: Date.now() - processingStartTime,
                error
              });
            } catch (logError) {
              console.error('Error logging processing error:', logError);
            }
          }
          throw error;
        }
      }),
    );
  }

  /**
   * 检查是否是标准响应格式
   */
  private isStandardResponse(data: any): boolean {
    return (
      data &&
      typeof data === 'object' &&
      'code' in data &&
      'message' in data &&
      'timestamp' in data
    );
  }

  /**
   * 生成请求ID
   */
  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}
