import { NestInterceptor, ExecutionContext, CallHandler } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { Observable } from 'rxjs';
import { IResponseManager } from '../interfaces/response-manager.interface';
export declare class DddResponseInterceptor implements NestInterceptor {
    private readonly reflector;
    private readonly responseManager;
    constructor(reflector: Reflector, responseManager: IResponseManager);
    intercept(context: ExecutionContext, next: CallHandler): Observable<any>;
    private processResult;
    private handlePlainResult;
    private formatResponse;
    private handleError;
    private isCommandResult;
    private isQueryResult;
    private isHttpResponse;
}
