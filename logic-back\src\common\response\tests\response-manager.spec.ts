import { Test, TestingModule } from '@nestjs/testing';
import { ResponseManagerService } from '../services/response-manager.service';
import { ResponseFormat, StatusCodes } from '../interfaces/response.interface';

describe('ResponseManagerService', () => {
  let service: ResponseManagerService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [ResponseManagerService],
    }).compile();

    service = module.get<ResponseManagerService>(ResponseManagerService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('success', () => {
    it('should create success response with default message', () => {
      const data = { id: 1, name: 'test' };
      const response = service.success(data);

      expect(response).toEqual({
        success: true,
        code: StatusCodes.SUCCESS,
        message: '操作成功',
        data,
        timestamp: expect.any(Date),
      });
    });

    it('should create success response with custom message', () => {
      const data = { id: 1, name: 'test' };
      const message = '自定义成功消息';
      const response = service.success(data, message);

      expect(response.message).toBe(message);
      expect(response.data).toBe(data);
    });

    it('should include execution time when context is set', () => {
      service.setContext({
        requestId: 'test-id',
        startTime: Date.now() - 100,
        path: '/test',
        method: 'GET',
      });

      const response = service.success({ test: true });

      expect(response.executionTime).toBeGreaterThan(0);
      expect(response.requestId).toBe('test-id');
    });
  });

  describe('error', () => {
    it('should create error response with default message', () => {
      const response = service.error();

      expect(response).toEqual({
        success: false,
        code: StatusCodes.INTERNAL_SERVER_ERROR,
        message: '系统内部错误',
        timestamp: expect.any(Date),
      });
    });

    it('should create error response with custom message and errors', () => {
      const message = '自定义错误消息';
      const errors = ['错误1', '错误2'];
      const response = service.error(message, errors);

      expect(response.message).toBe(message);
      expect(response.errors).toEqual(errors);
    });
  });

  describe('paginated', () => {
    it('should create paginated response', () => {
      const data = [{ id: 1 }, { id: 2 }];
      const pagination = { page: 1, size: 10, total: 100 };
      const response = service.paginated(data, pagination);

      expect(response.data).toBe(data);
      expect(response.pagination).toEqual({
        ...pagination,
        totalPages: 10,
        hasNext: true,
        hasPrev: false,
      });
    });

    it('should calculate pagination correctly', () => {
      const data = [{ id: 1 }];
      const pagination = { page: 5, size: 10, total: 45 };
      const response = service.paginated(data, pagination);

      expect(response.pagination.totalPages).toBe(5);
      expect(response.pagination.hasNext).toBe(false);
      expect(response.pagination.hasPrev).toBe(true);
    });
  });

  describe('custom', () => {
    it('should create custom response', () => {
      const code = 201;
      const message = '创建成功';
      const data = { id: 1 };
      const response = service.custom(code, message, data);

      expect(response).toEqual({
        code,
        message,
        data,
        timestamp: expect.any(Date),
      });
    });
  });

  describe('format', () => {
    it('should format response as standard', () => {
      const response = service.success({ test: true });
      const formatted = service.format(response, ResponseFormat.STANDARD);

      expect(formatted).toBe(response);
    });

    it('should format response as simple', () => {
      const response = service.success({ test: true }, '测试消息');
      const formatted = service.format(response, ResponseFormat.SIMPLE);

      expect(formatted).toEqual({
        code: response.code,
        msg: response.message,
        data: response.data,
      });
    });

    it('should format response as restful for success', () => {
      const data = { test: true };
      const response = service.success(data);
      const formatted = service.format(response, ResponseFormat.RESTFUL);

      expect(formatted).toBe(data);
    });

    it('should format response as restful for error', () => {
      const response = service.error('测试错误');
      const formatted = service.format(response, ResponseFormat.RESTFUL);

      expect(formatted).toEqual({
        error: {
          code: response.code,
          message: response.message,
          timestamp: response.timestamp,
        },
      });
    });
  });

  describe('context management', () => {
    it('should set and get context', () => {
      const context = {
        requestId: 'test-id',
        startTime: Date.now(),
        path: '/test',
        method: 'GET',
        userId: 123,
      };

      service.setContext(context);
      const retrievedContext = service.getContext();

      expect(retrievedContext).toMatchObject(context);
    });

    it('should generate requestId if not provided', () => {
      service.setContext({
        path: '/test',
        method: 'GET',
      });

      const context = service.getContext();
      expect(context?.requestId).toBeDefined();
      expect(context?.startTime).toBeDefined();
    });
  });

  describe('specialized error methods', () => {
    beforeEach(() => {
      service.setContext({
        requestId: 'test-id',
        startTime: Date.now(),
        path: '/test',
        method: 'GET',
      });
    });

    it('should create validation error', () => {
      const errors = ['字段1错误', '字段2错误'];
      const response = service.validationError(errors);

      expect(response.success).toBe(false);
      expect(response.code).toBe(StatusCodes.VALIDATION_ERROR);
      expect(response.errors).toEqual(errors);
      expect(response.errorType).toBe('VALIDATION');
    });

    it('should create business error', () => {
      const message = '业务逻辑错误';
      const errorCode = 'BIZ_001';
      const response = service.businessError(message, errorCode);

      expect(response.success).toBe(false);
      expect(response.code).toBe(StatusCodes.BAD_REQUEST);
      expect(response.message).toBe(message);
      expect(response.errorCode).toBe(errorCode);
      expect(response.errorType).toBe('BUSINESS');
    });

    it('should create auth error', () => {
      const response = service.authError();

      expect(response.success).toBe(false);
      expect(response.code).toBe(StatusCodes.UNAUTHORIZED);
      expect(response.errorType).toBe('AUTH');
    });

    it('should create forbidden error', () => {
      const response = service.forbiddenError();

      expect(response.success).toBe(false);
      expect(response.code).toBe(StatusCodes.FORBIDDEN);
      expect(response.errorType).toBe('AUTH');
    });

    it('should create not found error', () => {
      const response = service.notFoundError();

      expect(response.success).toBe(false);
      expect(response.code).toBe(StatusCodes.NOT_FOUND);
      expect(response.errorType).toBe('BUSINESS');
    });

    it('should create system error', () => {
      const message = '系统异常';
      const stack = 'Error stack trace';
      const response = service.systemError(message, stack);

      expect(response.success).toBe(false);
      expect(response.code).toBe(StatusCodes.INTERNAL_SERVER_ERROR);
      expect(response.message).toBe(message);
      expect(response.errorType).toBe('SYSTEM');
      
      // 在开发环境下应该包含堆栈信息
      if (process.env.NODE_ENV === 'development') {
        expect(response.stack).toBe(stack);
      }
    });
  });
});
