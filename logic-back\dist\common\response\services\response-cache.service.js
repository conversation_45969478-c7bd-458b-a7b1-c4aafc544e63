"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ResponseCacheService = void 0;
const common_1 = require("@nestjs/common");
const redis_service_1 = require("../../../util/database/redis/redis.service");
const logger_service_1 = require("../../logger/logger.service");
const response_logging_config_1 = require("../config/response-logging.config");
let ResponseCacheService = class ResponseCacheService {
    redisService;
    loggerService;
    defaultConfig = {
        prefix: 'response_cache',
        defaultTtl: 300,
        enabled: true,
        compress: false,
        maxSize: 1024 * 1024,
    };
    constructor(redisService, loggerService) {
        this.redisService = redisService;
        this.loggerService = loggerService;
    }
    generateCacheKey(method, path, params, userId, config) {
        const prefix = config?.prefix || this.defaultConfig.prefix;
        const paramsHash = params ? this.hashObject(params) : '';
        const userPart = userId ? `_user:${userId}` : '';
        return `${prefix}:${method}:${path}${userPart}${paramsHash ? `_${paramsHash}` : ''}`;
    }
    hashObject(obj) {
        const str = JSON.stringify(obj, Object.keys(obj).sort());
        let hash = 0;
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash;
        }
        return Math.abs(hash).toString(36);
    }
    compressData(data) {
        return data;
    }
    decompressData(data) {
        return data;
    }
    serializeResponse(response) {
        return JSON.stringify({
            ...response,
            cached: true,
            cachedAt: new Date().toISOString(),
        });
    }
    deserializeResponse(data) {
        try {
            return JSON.parse(data);
        }
        catch (error) {
            this.loggerService.error('Failed to deserialize cached response', error.stack, 'ResponseCacheService');
            return null;
        }
    }
    checkDataSize(data, config) {
        const maxSize = config?.maxSize || this.defaultConfig.maxSize;
        const dataSize = Buffer.byteLength(data, 'utf8');
        if (dataSize > maxSize) {
            this.loggerService.warn(`Response data too large for cache: ${dataSize} bytes > ${maxSize} bytes`, 'ResponseCacheService');
            return false;
        }
        return true;
    }
    async get(method, path, params, userId, config) {
        const finalConfig = { ...this.defaultConfig, ...config };
        if (!finalConfig.enabled) {
            return null;
        }
        try {
            const cacheKey = this.generateCacheKey(method, path, params, userId, finalConfig);
            const cachedData = await this.redisService.get(cacheKey);
            if (!cachedData) {
                if (response_logging_config_1.ResponseLoggingUtils.getConfig().logCacheOperations) {
                    this.loggerService.logResponseCache({
                        action: 'miss',
                        cacheKey,
                    });
                }
                return null;
            }
            const decompressedData = finalConfig.compress ? this.decompressData(cachedData) : cachedData;
            const response = this.deserializeResponse(decompressedData);
            if (response) {
                if (response_logging_config_1.ResponseLoggingUtils.getConfig().logCacheOperations) {
                    this.loggerService.logResponseCache({
                        action: 'hit',
                        cacheKey,
                        size: Buffer.byteLength(cachedData, 'utf8'),
                    });
                }
            }
            return response;
        }
        catch (error) {
            this.loggerService.error('Failed to get cached response', error.stack, 'ResponseCacheService');
            return null;
        }
    }
    async set(method, path, response, params, userId, ttl, config) {
        const finalConfig = { ...this.defaultConfig, ...config };
        if (!finalConfig.enabled) {
            return false;
        }
        try {
            const cacheKey = this.generateCacheKey(method, path, params, userId, finalConfig);
            const serializedData = this.serializeResponse(response);
            if (!this.checkDataSize(serializedData, finalConfig)) {
                return false;
            }
            const finalData = finalConfig.compress ? this.compressData(serializedData) : serializedData;
            const finalTtl = ttl || finalConfig.defaultTtl;
            await this.redisService.set(cacheKey, finalData, finalTtl);
            if (response_logging_config_1.ResponseLoggingUtils.getConfig().logCacheOperations) {
                this.loggerService.logResponseCache({
                    action: 'set',
                    cacheKey,
                    ttl: finalTtl,
                    size: Buffer.byteLength(finalData, 'utf8'),
                });
            }
            return true;
        }
        catch (error) {
            this.loggerService.error('Failed to set response cache', error.stack, 'ResponseCacheService');
            return false;
        }
    }
    async delete(method, path, params, userId, config) {
        const finalConfig = { ...this.defaultConfig, ...config };
        try {
            const cacheKey = this.generateCacheKey(method, path, params, userId, finalConfig);
            const result = await this.redisService.del(cacheKey);
            if (response_logging_config_1.ResponseLoggingUtils.getConfig().logCacheOperations) {
                this.loggerService.logResponseCache({
                    action: 'invalidate',
                    cacheKey,
                });
            }
            return result > 0;
        }
        catch (error) {
            this.loggerService.error('Failed to delete response cache', error.stack, 'ResponseCacheService');
            return false;
        }
    }
    async deleteByPattern(pattern, config) {
        const finalConfig = { ...this.defaultConfig, ...config };
        const prefix = finalConfig.prefix || this.defaultConfig.prefix;
        const fullPattern = `${prefix}:${pattern}`;
        try {
            const keys = await this.redisService.keys(fullPattern);
            if (keys.length === 0) {
                return 0;
            }
            const pipeline = this.redisService.getClient().pipeline();
            keys.forEach(key => pipeline.del(key));
            await pipeline.exec();
            if (response_logging_config_1.ResponseLoggingUtils.getConfig().logCacheOperations) {
                this.loggerService.logResponseCache({
                    action: 'invalidate',
                    cacheKey: fullPattern,
                });
            }
            return keys.length;
        }
        catch (error) {
            this.loggerService.error('Failed to delete cache by pattern', error.stack, 'ResponseCacheService');
            return 0;
        }
    }
    async exists(method, path, params, userId, config) {
        const finalConfig = { ...this.defaultConfig, ...config };
        try {
            const cacheKey = this.generateCacheKey(method, path, params, userId, finalConfig);
            const result = await this.redisService.exists(cacheKey);
            return result > 0;
        }
        catch (error) {
            this.loggerService.error('Failed to check cache existence', error.stack, 'ResponseCacheService');
            return false;
        }
    }
    async getTtl(method, path, params, userId, config) {
        const finalConfig = { ...this.defaultConfig, ...config };
        try {
            const cacheKey = this.generateCacheKey(method, path, params, userId, finalConfig);
            return await this.redisService.ttl(cacheKey);
        }
        catch (error) {
            this.loggerService.error('Failed to get cache TTL', error.stack, 'ResponseCacheService');
            return -1;
        }
    }
    async getStats(config) {
        const finalConfig = { ...this.defaultConfig, ...config };
        const prefix = finalConfig.prefix || this.defaultConfig.prefix;
        try {
            const keys = await this.redisService.keys(`${prefix}:*`);
            let totalSize = 0;
            let totalTtl = 0;
            let validTtlCount = 0;
            for (const key of keys) {
                const value = await this.redisService.get(key);
                if (value) {
                    totalSize += Buffer.byteLength(value, 'utf8');
                }
                const ttl = await this.redisService.ttl(key);
                if (ttl > 0) {
                    totalTtl += ttl;
                    validTtlCount++;
                }
            }
            return {
                totalKeys: keys.length,
                totalSize,
                avgTtl: validTtlCount > 0 ? Math.round(totalTtl / validTtlCount) : 0,
            };
        }
        catch (error) {
            this.loggerService.error('Failed to get cache stats', error.stack, 'ResponseCacheService');
            return {
                totalKeys: 0,
                totalSize: 0,
                avgTtl: 0,
            };
        }
    }
    async clear(config) {
        const finalConfig = { ...this.defaultConfig, ...config };
        const prefix = finalConfig.prefix || this.defaultConfig.prefix;
        return await this.deleteByPattern('*', finalConfig);
    }
};
exports.ResponseCacheService = ResponseCacheService;
exports.ResponseCacheService = ResponseCacheService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [redis_service_1.RedisService,
        logger_service_1.LoggerService])
], ResponseCacheService);
//# sourceMappingURL=response-cache.service.js.map