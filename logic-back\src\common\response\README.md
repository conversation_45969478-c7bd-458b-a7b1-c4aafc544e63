# 通用响应管理器

这是一个为 NestJS 应用程序设计的通用响应管理器，提供统一的响应格式化、错误处理和API文档生成功能。

## 功能特性

- 🎯 **统一响应格式** - 标准化所有API响应格式
- 🔧 **多种响应格式** - 支持标准、简化、RESTful等多种格式
- 📄 **分页响应** - 内置分页数据处理
- 🚨 **错误处理** - 统一的异常处理和错误响应
- 📝 **自动文档** - 自动生成Swagger API文档
- 🔍 **请求追踪** - 自动生成请求ID和执行时间
- 🎨 **装饰器支持** - 简化控制器代码

## 快速开始

### 1. 导入模块

在你的 `app.module.ts` 中导入 `ResponseModule`：

```typescript
import { Module } from '@nestjs/common';
import { ResponseModule } from './common/response';

@Module({
  imports: [
    ResponseModule, // 全局响应管理
    // 其他模块...
  ],
})
export class AppModule {}
```

### 2. 基础使用

#### 自动响应格式化

```typescript
import { Controller, Get } from '@nestjs/common';
import { SuccessResponse } from './common/response';

@Controller('users')
export class UserController {
  @Get()
  @SuccessResponse('获取用户列表成功')
  async getUsers() {
    // 直接返回数据，会自动包装成标准响应格式
    return [
      { id: 1, name: '张三' },
      { id: 2, name: '李四' },
    ];
  }
}
```

#### 手动创建响应

```typescript
import { Controller, Get } from '@nestjs/common';
import { ResponseManagerService } from './common/response';

@Controller('users')
export class UserController {
  constructor(private readonly responseManager: ResponseManagerService) {}

  @Get()
  async getUsers() {
    const users = [
      { id: 1, name: '张三' },
      { id: 2, name: '李四' },
    ];

    return this.responseManager.success(users, '获取用户列表成功');
  }
}
```

### 3. 分页响应

#### 自动分页处理

```typescript
@Get()
@PaginatedResponse('获取分页用户列表成功')
async getPaginatedUsers(@Query('page') page: number = 1, @Query('size') size: number = 10) {
  return {
    data: users, // 或者 list: users
    pagination: { page, size, total: 100 }
  };
}
```

#### 手动分页响应

```typescript
@Get()
async getPaginatedUsers(@Query('page') page: number = 1, @Query('size') size: number = 10) {
  const users = await this.userService.findPaginated(page, size);
  
  return this.responseManager.paginated(
    users.data,
    { page, size, total: users.total },
    '获取分页用户列表成功'
  );
}
```

### 4. 错误处理

#### 业务异常

```typescript
import { BusinessException } from './common/response';

@Get(':id')
async getUser(@Param('id') id: string) {
  const user = await this.userService.findById(id);
  
  if (!user) {
    throw new BusinessException('用户不存在', 'USER_NOT_FOUND');
  }
  
  return user;
}
```

#### 验证异常

```typescript
import { ValidationException } from './common/response';

@Post()
async createUser(@Body() userData: any) {
  const errors = [];
  
  if (!userData.name) {
    errors.push('姓名不能为空');
  }
  
  if (!userData.email) {
    errors.push('邮箱不能为空');
  }
  
  if (errors.length > 0) {
    throw new ValidationException('数据验证失败', errors);
  }
  
  return await this.userService.create(userData);
}
```

### 5. 响应格式

#### 标准格式（默认）

```json
{
  "success": true,
  "code": 200,
  "message": "操作成功",
  "data": { "id": 1, "name": "张三" },
  "timestamp": "2024-01-01T00:00:00.000Z",
  "requestId": "req_1704067200000_abc123",
  "executionTime": 15
}
```

#### 简化格式

```typescript
@Get()
@SimpleResponse('获取数据成功')
async getData() {
  return { id: 1, name: '张三' };
}
```

```json
{
  "code": 200,
  "msg": "获取数据成功",
  "data": { "id": 1, "name": "张三" }
}
```

#### RESTful格式

```typescript
@Get()
@RestfulResponse()
async getData() {
  return { id: 1, name: '张三' };
}
```

```json
{ "id": 1, "name": "张三" }
```

### 6. 自定义配置

```typescript
@Get()
@Response({
  format: ResponseFormat.STANDARD,
  includeExecutionTime: true,
  includeRequestId: true,
  successMessage: '自定义成功消息'
})
async getData() {
  return { id: 1, name: '张三' };
}
```

### 7. API文档

```typescript
@Get()
@ApiResponseDocs({
  success: {
    message: '获取用户成功',
    example: { id: 1, name: '张三', email: '<EMAIL>' }
  }
})
async getUser() {
  return { id: 1, name: '张三', email: '<EMAIL>' };
}
```

## 装饰器参考

### 响应装饰器

- `@SuccessResponse(message?, example?)` - 成功响应
- `@PaginatedResponse(message?, itemType?)` - 分页响应
- `@SimpleResponse(message?)` - 简化格式响应
- `@RestfulResponse(type?)` - RESTful格式响应
- `@Response(config)` - 自定义响应配置

### 文档装饰器

- `@ApiResponseDocs(config)` - 完整API文档
- `@CommonErrorResponses()` - 常见错误响应文档

## 异常类

- `BusinessException` - 业务异常
- `ValidationException` - 验证异常

## 响应格式枚举

- `ResponseFormat.STANDARD` - 标准格式
- `ResponseFormat.SIMPLE` - 简化格式
- `ResponseFormat.RESTFUL` - RESTful格式
- `ResponseFormat.CUSTOM` - 自定义格式

## 状态码常量

```typescript
import { StatusCodes } from './common/response';

// 使用预定义状态码
StatusCodes.SUCCESS // 200
StatusCodes.BAD_REQUEST // 400
StatusCodes.UNAUTHORIZED // 401
// ... 更多状态码
```

## 注意事项

1. 响应管理器使用 `REQUEST` 作用域，每个请求都有独立的实例
2. 请求上下文会自动设置，包含请求ID、执行时间等信息
3. 错误响应会自动记录日志
4. 开发环境下会包含更详细的错误信息

## 示例

查看 `response-example.controller.ts` 文件获取更多使用示例。
