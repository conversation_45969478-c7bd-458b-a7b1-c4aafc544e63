"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UnifiedResponseExampleController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const unified_response_interceptor_1 = require("../interceptors/unified-response.interceptor");
const http_response_result_service_1 = require("../../../web/http_response_result/http_response_result.service");
let UnifiedResponseExampleController = class UnifiedResponseExampleController {
    responseManager;
    httpResponseService;
    constructor(responseManager, httpResponseService) {
        this.responseManager = responseManager;
        this.httpResponseService = httpResponseService;
    }
    getString() {
        return 'Hello, World!';
    }
    getNumber() {
        return 42;
    }
    getBoolean() {
        return true;
    }
    getObject() {
        return {
            id: 1,
            name: '张三',
            email: '<EMAIL>',
            age: 25,
            createdAt: new Date()
        };
    }
    getArray() {
        return [
            { id: 1, name: '张三' },
            { id: 2, name: '李四' },
            { id: 3, name: '王五' }
        ];
    }
    getNull() {
        return null;
    }
    getUndefined() {
        return undefined;
    }
    getHttpResponse() {
        return this.httpResponseService.success({
            id: 1,
            name: '测试数据'
        }, '获取成功');
    }
    getPaginatedData(page = 1, size = 10) {
        const total = 100;
        const list = Array.from({ length: size }, (_, index) => ({
            id: (page - 1) * size + index + 1,
            name: `用户${(page - 1) * size + index + 1}`,
            email: `user${(page - 1) * size + index + 1}@example.com`
        }));
        return {
            list,
            total,
            page,
            pageSize: size,
            totalPages: Math.ceil(total / size)
        };
    }
    throwError() {
        throw new Error('这是一个测试错误');
    }
    async getAsyncData() {
        await new Promise(resolve => setTimeout(resolve, 100));
        return {
            message: '异步操作完成',
            timestamp: new Date(),
            data: { result: 'success' }
        };
    }
    async highPerformanceOperation(data) {
        await new Promise(resolve => setTimeout(resolve, 200));
        return {
            message: '高性能操作完成',
            input: data,
            processedAt: new Date()
        };
    }
    async realtimeOperation(data) {
        return {
            message: '实时操作完成',
            input: data,
            timestamp: new Date()
        };
    }
    async batchOperation(items) {
        await new Promise(resolve => setTimeout(resolve, 500));
        return {
            message: `批量处理完成，共处理 ${items.length} 项`,
            processedCount: items.length,
            results: items.map((item, index) => ({
                index,
                item,
                processed: true
            }))
        };
    }
    getDisabledResponse() {
        return {
            customFormat: true,
            message: '这是原始返回格式',
            timestamp: new Date()
        };
    }
    async customConfigOperation(data) {
        await new Promise(resolve => setTimeout(resolve, 1000));
        return {
            message: '自定义配置操作完成',
            config: {
                usePool: true,
                priority: 5,
                timeout: 60000
            },
            data
        };
    }
    async manualResponseHandling(data) {
        const context = this.responseManager.createContext({ user: { id: 'test_user' } }, 'Manual', 'ProcessData');
        try {
            const result = {
                message: '手动处理完成',
                data,
                processedAt: new Date()
            };
            return await this.responseManager.handleResponse(result, context, {
                usePool: true,
                priority: 1
            });
        }
        catch (error) {
            return await this.responseManager.handleError(error, context);
        }
    }
    async getPoolStatus() {
        return await this.responseManager.getPoolStatus();
    }
    async simulateDatabaseOperation() {
        await new Promise(resolve => setTimeout(resolve, 50));
        await new Promise(resolve => setTimeout(resolve, 30));
        await new Promise(resolve => setTimeout(resolve, 20));
        return {
            message: '数据库操作完成',
            queriesExecuted: 3,
            totalTime: '100ms',
            results: [
                { table: 'users', count: 150 },
                { table: 'orders', count: 300 },
                { table: 'products', count: 50 }
            ]
        };
    }
    getComplexObject() {
        return {
            user: {
                id: 1,
                profile: {
                    name: '张三',
                    contact: {
                        email: '<EMAIL>',
                        phone: '13800138000',
                        address: {
                            country: '中国',
                            province: '北京市',
                            city: '北京市',
                            district: '朝阳区',
                            street: '建国路1号'
                        }
                    }
                },
                preferences: {
                    theme: 'dark',
                    language: 'zh-CN',
                    notifications: {
                        email: true,
                        sms: false,
                        push: true
                    }
                },
                statistics: {
                    loginCount: 156,
                    lastLoginAt: new Date(),
                    createdAt: new Date('2024-01-01'),
                    updatedAt: new Date()
                }
            },
            metadata: {
                version: '1.0.0',
                apiVersion: 'v1',
                requestTime: new Date(),
                serverInfo: {
                    hostname: 'api-server-01',
                    region: 'cn-north-1',
                    environment: 'production'
                }
            }
        };
    }
};
exports.UnifiedResponseExampleController = UnifiedResponseExampleController;
__decorate([
    (0, common_1.Get)('string'),
    (0, swagger_1.ApiOperation)({ summary: '返回字符串示例' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", String)
], UnifiedResponseExampleController.prototype, "getString", null);
__decorate([
    (0, common_1.Get)('number'),
    (0, swagger_1.ApiOperation)({ summary: '返回数字示例' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Number)
], UnifiedResponseExampleController.prototype, "getNumber", null);
__decorate([
    (0, common_1.Get)('boolean'),
    (0, swagger_1.ApiOperation)({ summary: '返回布尔值示例' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Boolean)
], UnifiedResponseExampleController.prototype, "getBoolean", null);
__decorate([
    (0, common_1.Get)('object'),
    (0, swagger_1.ApiOperation)({ summary: '返回对象示例' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Object)
], UnifiedResponseExampleController.prototype, "getObject", null);
__decorate([
    (0, common_1.Get)('array'),
    (0, swagger_1.ApiOperation)({ summary: '返回数组示例' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Array)
], UnifiedResponseExampleController.prototype, "getArray", null);
__decorate([
    (0, common_1.Get)('null'),
    (0, swagger_1.ApiOperation)({ summary: '返回null示例' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], UnifiedResponseExampleController.prototype, "getNull", null);
__decorate([
    (0, common_1.Get)('undefined'),
    (0, swagger_1.ApiOperation)({ summary: '返回undefined示例' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], UnifiedResponseExampleController.prototype, "getUndefined", null);
__decorate([
    (0, common_1.Get)('http-response'),
    (0, swagger_1.ApiOperation)({ summary: '返回HttpResponse格式示例' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], UnifiedResponseExampleController.prototype, "getHttpResponse", null);
__decorate([
    (0, common_1.Get)('paginated'),
    (0, swagger_1.ApiOperation)({ summary: '返回分页数据示例' }),
    __param(0, (0, common_1.Query)('page')),
    __param(1, (0, common_1.Query)('size')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Number]),
    __metadata("design:returntype", void 0)
], UnifiedResponseExampleController.prototype, "getPaginatedData", null);
__decorate([
    (0, common_1.Get)('error'),
    (0, swagger_1.ApiOperation)({ summary: '错误处理示例' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], UnifiedResponseExampleController.prototype, "throwError", null);
__decorate([
    (0, common_1.Get)('async'),
    (0, swagger_1.ApiOperation)({ summary: '异步操作示例' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], UnifiedResponseExampleController.prototype, "getAsyncData", null);
__decorate([
    (0, common_1.Post)('high-performance'),
    (0, swagger_1.ApiOperation)({ summary: '高性能响应示例' }),
    (0, unified_response_interceptor_1.HighPerformanceResponse)(),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], UnifiedResponseExampleController.prototype, "highPerformanceOperation", null);
__decorate([
    (0, common_1.Post)('realtime'),
    (0, swagger_1.ApiOperation)({ summary: '实时响应示例' }),
    (0, unified_response_interceptor_1.RealtimeResponse)(),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], UnifiedResponseExampleController.prototype, "realtimeOperation", null);
__decorate([
    (0, common_1.Post)('batch'),
    (0, swagger_1.ApiOperation)({ summary: '批量响应示例' }),
    (0, unified_response_interceptor_1.BatchResponse)(),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array]),
    __metadata("design:returntype", Promise)
], UnifiedResponseExampleController.prototype, "batchOperation", null);
__decorate([
    (0, common_1.Get)('disabled'),
    (0, swagger_1.ApiOperation)({ summary: '禁用统一响应示例' }),
    (0, unified_response_interceptor_1.DisableUnifiedResponse)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], UnifiedResponseExampleController.prototype, "getDisabledResponse", null);
__decorate([
    (0, common_1.Post)('custom-config'),
    (0, swagger_1.ApiOperation)({ summary: '自定义配置示例' }),
    (0, unified_response_interceptor_1.UnifiedResponse)({
        usePool: true,
        priority: 5,
        timeout: 60000,
        controller: 'CustomExample',
        action: 'ProcessData'
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], UnifiedResponseExampleController.prototype, "customConfigOperation", null);
__decorate([
    (0, common_1.Post)('manual'),
    (0, swagger_1.ApiOperation)({ summary: '手动使用响应管理器示例' }),
    (0, unified_response_interceptor_1.DisableUnifiedResponse)(),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], UnifiedResponseExampleController.prototype, "manualResponseHandling", null);
__decorate([
    (0, common_1.Get)('pool-status'),
    (0, swagger_1.ApiOperation)({ summary: '获取请求池状态' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], UnifiedResponseExampleController.prototype, "getPoolStatus", null);
__decorate([
    (0, common_1.Get)('database-simulation'),
    (0, swagger_1.ApiOperation)({ summary: '模拟数据库操作示例' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], UnifiedResponseExampleController.prototype, "simulateDatabaseOperation", null);
__decorate([
    (0, common_1.Get)('complex-object'),
    (0, swagger_1.ApiOperation)({ summary: '复杂嵌套对象示例' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Object)
], UnifiedResponseExampleController.prototype, "getComplexObject", null);
exports.UnifiedResponseExampleController = UnifiedResponseExampleController = __decorate([
    (0, swagger_1.ApiTags)('统一响应管理器示例'),
    (0, common_1.Controller)('examples/unified-response'),
    __param(0, (0, common_1.Inject)('UNIFIED_RESPONSE_MANAGER')),
    __metadata("design:paramtypes", [Object, http_response_result_service_1.HttpResponseResultService])
], UnifiedResponseExampleController);
//# sourceMappingURL=usage-examples.controller.js.map