"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ResponseManagerService = void 0;
const common_1 = require("@nestjs/common");
const uuid_1 = require("uuid");
const response_interface_1 = require("../interfaces/response.interface");
let ResponseManagerService = class ResponseManagerService {
    context = null;
    success(data, message = response_interface_1.DefaultMessages.SUCCESS, config) {
        const response = {
            success: true,
            code: response_interface_1.StatusCodes.SUCCESS,
            message,
            data,
            timestamp: new Date(),
        };
        if (config?.includeExecutionTime !== false && this.context) {
            response.executionTime = Date.now() - this.context.startTime;
        }
        if (config?.includeRequestId !== false && this.context) {
            response.requestId = this.context.requestId;
        }
        return response;
    }
    error(message = response_interface_1.DefaultMessages.INTERNAL_ERROR, errors, config) {
        const response = {
            success: false,
            code: response_interface_1.StatusCodes.INTERNAL_SERVER_ERROR,
            message,
            timestamp: new Date(),
        };
        if (errors && errors.length > 0) {
            response.errors = errors;
        }
        if (config?.includeRequestId !== false && this.context) {
            response.requestId = this.context.requestId;
        }
        return response;
    }
    paginated(data, pagination, message = response_interface_1.DefaultMessages.SUCCESS, config) {
        const totalPages = Math.ceil(pagination.total / pagination.size);
        const hasNext = pagination.page < totalPages;
        const hasPrev = pagination.page > 1;
        const response = {
            success: true,
            code: response_interface_1.StatusCodes.SUCCESS,
            message,
            data,
            timestamp: new Date(),
            pagination: {
                ...pagination,
                totalPages,
                hasNext,
                hasPrev,
            },
        };
        if (config?.includeExecutionTime !== false && this.context) {
            response.executionTime = Date.now() - this.context.startTime;
        }
        if (config?.includeRequestId !== false && this.context) {
            response.requestId = this.context.requestId;
        }
        return response;
    }
    custom(code, message, data, config) {
        const response = {
            code,
            message,
            data,
            timestamp: new Date(),
        };
        if (config?.includeRequestId !== false && this.context) {
            response.requestId = this.context.requestId;
        }
        return response;
    }
    setContext(context) {
        this.context = {
            requestId: context.requestId || (0, uuid_1.v4)(),
            startTime: context.startTime || Date.now(),
            path: context.path || '',
            method: context.method || '',
            ...context,
        };
    }
    getContext() {
        return this.context;
    }
    format(response, format = response_interface_1.ResponseFormat.STANDARD) {
        switch (format) {
            case response_interface_1.ResponseFormat.SIMPLE:
                return this.formatSimple(response);
            case response_interface_1.ResponseFormat.RESTFUL:
                return this.formatRestful(response);
            case response_interface_1.ResponseFormat.STANDARD:
            default:
                return this.formatStandard(response);
        }
    }
    formatStandard(response) {
        return response;
    }
    formatSimple(response) {
        return {
            code: response.code,
            msg: response.message,
            data: response.data,
        };
    }
    formatRestful(response) {
        if ('success' in response && response.success) {
            return response.data;
        }
        else {
            return {
                error: {
                    code: response.code,
                    message: response.message,
                    timestamp: response.timestamp,
                },
            };
        }
    }
    validationError(errors, message = response_interface_1.DefaultMessages.VALIDATION_ERROR) {
        return {
            success: false,
            code: response_interface_1.StatusCodes.VALIDATION_ERROR,
            message,
            errors,
            errorType: 'VALIDATION',
            timestamp: new Date(),
            requestId: this.context?.requestId,
        };
    }
    businessError(message, errorCode) {
        return {
            success: false,
            code: response_interface_1.StatusCodes.BAD_REQUEST,
            message,
            errorCode,
            errorType: 'BUSINESS',
            timestamp: new Date(),
            requestId: this.context?.requestId,
        };
    }
    authError(message = response_interface_1.DefaultMessages.UNAUTHORIZED) {
        return {
            success: false,
            code: response_interface_1.StatusCodes.UNAUTHORIZED,
            message,
            errorType: 'AUTH',
            timestamp: new Date(),
            requestId: this.context?.requestId,
        };
    }
    forbiddenError(message = response_interface_1.DefaultMessages.FORBIDDEN) {
        return {
            success: false,
            code: response_interface_1.StatusCodes.FORBIDDEN,
            message,
            errorType: 'AUTH',
            timestamp: new Date(),
            requestId: this.context?.requestId,
        };
    }
    notFoundError(message = response_interface_1.DefaultMessages.NOT_FOUND) {
        return {
            success: false,
            code: response_interface_1.StatusCodes.NOT_FOUND,
            message,
            errorType: 'BUSINESS',
            timestamp: new Date(),
            requestId: this.context?.requestId,
        };
    }
    systemError(message = response_interface_1.DefaultMessages.INTERNAL_ERROR, stack) {
        const response = {
            success: false,
            code: response_interface_1.StatusCodes.INTERNAL_SERVER_ERROR,
            message,
            errorType: 'SYSTEM',
            timestamp: new Date(),
            requestId: this.context?.requestId,
        };
        if (process.env.NODE_ENV === 'development' && stack) {
            response.stack = stack;
        }
        return response;
    }
};
exports.ResponseManagerService = ResponseManagerService;
exports.ResponseManagerService = ResponseManagerService = __decorate([
    (0, common_1.Injectable)({ scope: common_1.Scope.REQUEST })
], ResponseManagerService);
//# sourceMappingURL=response-manager.service.js.map