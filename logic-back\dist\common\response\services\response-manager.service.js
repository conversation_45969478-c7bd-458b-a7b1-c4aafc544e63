"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ResponseManagerService = void 0;
const common_1 = require("@nestjs/common");
const uuid_1 = require("uuid");
const logger_service_1 = require("../../logger/logger.service");
const response_logging_config_1 = require("../config/response-logging.config");
const response_interface_1 = require("../interfaces/response.interface");
let ResponseManagerService = class ResponseManagerService {
    loggerService;
    context = null;
    constructor(loggerService) {
        this.loggerService = loggerService;
    }
    success(data, message = response_interface_1.DefaultMessages.SUCCESS, config) {
        const startTime = Date.now();
        const response = {
            success: true,
            code: response_interface_1.StatusCodes.SUCCESS,
            message,
            data,
            timestamp: new Date(),
        };
        if (config?.includeExecutionTime !== false && this.context) {
            response.executionTime = Date.now() - this.context.startTime;
        }
        if (config?.includeRequestId !== false && this.context) {
            response.requestId = this.context.requestId;
        }
        this.logResponse(response, data, Date.now() - startTime);
        return response;
    }
    error(message = response_interface_1.DefaultMessages.INTERNAL_ERROR, errors, config) {
        const startTime = Date.now();
        const response = {
            success: false,
            code: response_interface_1.StatusCodes.INTERNAL_SERVER_ERROR,
            message,
            timestamp: new Date(),
        };
        if (errors && errors.length > 0) {
            response.errors = errors;
        }
        if (config?.includeRequestId !== false && this.context) {
            response.requestId = this.context.requestId;
        }
        this.logResponse(response, undefined, Date.now() - startTime);
        return response;
    }
    paginated(data, pagination, message = response_interface_1.DefaultMessages.SUCCESS, config) {
        const startTime = Date.now();
        const totalPages = Math.ceil(pagination.total / pagination.size);
        const hasNext = pagination.page < totalPages;
        const hasPrev = pagination.page > 1;
        const response = {
            success: true,
            code: response_interface_1.StatusCodes.SUCCESS,
            message,
            data,
            timestamp: new Date(),
            pagination: {
                ...pagination,
                totalPages,
                hasNext,
                hasPrev,
            },
        };
        if (config?.includeExecutionTime !== false && this.context) {
            response.executionTime = Date.now() - this.context.startTime;
        }
        if (config?.includeRequestId !== false && this.context) {
            response.requestId = this.context.requestId;
        }
        this.logResponse(response, { dataCount: data.length, pagination }, Date.now() - startTime);
        return response;
    }
    custom(code, message, data, config) {
        const response = {
            code,
            message,
            data,
            timestamp: new Date(),
        };
        if (config?.includeRequestId !== false && this.context) {
            response.requestId = this.context.requestId;
        }
        return response;
    }
    setContext(context) {
        this.context = {
            requestId: context.requestId || (0, uuid_1.v4)(),
            startTime: context.startTime || Date.now(),
            path: context.path || '',
            method: context.method || '',
            ...context,
        };
    }
    getContext() {
        return this.context;
    }
    format(response, format = response_interface_1.ResponseFormat.STANDARD) {
        const startTime = Date.now();
        let transformedResponse;
        switch (format) {
            case response_interface_1.ResponseFormat.SIMPLE:
                transformedResponse = this.formatSimple(response);
                break;
            case response_interface_1.ResponseFormat.RESTFUL:
                transformedResponse = this.formatRestful(response);
                break;
            case response_interface_1.ResponseFormat.STANDARD:
            default:
                transformedResponse = this.formatStandard(response);
                break;
        }
        if (format !== response_interface_1.ResponseFormat.STANDARD) {
            this.logResponseTransform(response, transformedResponse, format, Date.now() - startTime);
        }
        return transformedResponse;
    }
    formatStandard(response) {
        return response;
    }
    formatSimple(response) {
        return {
            code: response.code,
            msg: response.message,
            data: response.data,
        };
    }
    formatRestful(response) {
        if ('success' in response && response.success) {
            return response.data;
        }
        else {
            return {
                error: {
                    code: response.code,
                    message: response.message,
                    timestamp: response.timestamp,
                },
            };
        }
    }
    validationError(errors, message = response_interface_1.DefaultMessages.VALIDATION_ERROR) {
        return {
            success: false,
            code: response_interface_1.StatusCodes.VALIDATION_ERROR,
            message,
            errors,
            errorType: 'VALIDATION',
            timestamp: new Date(),
            requestId: this.context?.requestId,
        };
    }
    businessError(message, errorCode) {
        return {
            success: false,
            code: response_interface_1.StatusCodes.BAD_REQUEST,
            message,
            errorCode,
            errorType: 'BUSINESS',
            timestamp: new Date(),
            requestId: this.context?.requestId,
        };
    }
    authError(message = response_interface_1.DefaultMessages.UNAUTHORIZED) {
        return {
            success: false,
            code: response_interface_1.StatusCodes.UNAUTHORIZED,
            message,
            errorType: 'AUTH',
            timestamp: new Date(),
            requestId: this.context?.requestId,
        };
    }
    forbiddenError(message = response_interface_1.DefaultMessages.FORBIDDEN) {
        return {
            success: false,
            code: response_interface_1.StatusCodes.FORBIDDEN,
            message,
            errorType: 'AUTH',
            timestamp: new Date(),
            requestId: this.context?.requestId,
        };
    }
    notFoundError(message = response_interface_1.DefaultMessages.NOT_FOUND) {
        return {
            success: false,
            code: response_interface_1.StatusCodes.NOT_FOUND,
            message,
            errorType: 'BUSINESS',
            timestamp: new Date(),
            requestId: this.context?.requestId,
        };
    }
    systemError(message = response_interface_1.DefaultMessages.INTERNAL_ERROR, stack) {
        const startTime = Date.now();
        const response = {
            success: false,
            code: response_interface_1.StatusCodes.INTERNAL_SERVER_ERROR,
            message,
            errorType: 'SYSTEM',
            timestamp: new Date(),
            requestId: this.context?.requestId,
        };
        if (process.env.NODE_ENV === 'development' && stack) {
            response.stack = stack;
        }
        this.logResponse(response, undefined, Date.now() - startTime);
        return response;
    }
    logResponse(response, originalData, processingTime) {
        if (!this.context) {
            return;
        }
        if (!response_logging_config_1.ResponseLoggingUtils.shouldLog(this.context.path, this.context.userAgent)) {
            return;
        }
        try {
            const isSuccessResponse = this.isSuccessResponse(response);
            const isErrorResponse = this.isErrorResponse(response);
            this.loggerService.logApiResponse({
                requestId: this.context.requestId,
                method: this.context.method,
                path: this.context.path,
                statusCode: response.code,
                success: isSuccessResponse ? response.success : response.code < 400,
                message: response.message,
                executionTime: isSuccessResponse ? response.executionTime : undefined,
                userId: this.context.userId,
                ip: this.context.ip,
                userAgent: this.context.userAgent,
                module: this.context.module,
                operation: this.context.operation,
                responseData: response_logging_config_1.ResponseLoggingUtils.sanitizeData(response.data),
                errors: isErrorResponse ? response.errors : undefined,
                errorType: isErrorResponse ? response.errorType : undefined,
            });
            const config = response_logging_config_1.ResponseLoggingUtils.getConfig();
            if (config.logProcessingSteps) {
                this.loggerService.logResponseProcessing({
                    requestId: this.context.requestId,
                    stage: 'manager',
                    action: 'create_response',
                    duration: processingTime,
                    details: {
                        responseType: isSuccessResponse ? (response.success ? 'success' : 'error') : 'custom',
                        hasData: !!response.data,
                        messageLength: response.message?.length || 0,
                        originalDataType: originalData ? typeof originalData : 'none',
                    }
                });
            }
            const totalExecutionTime = isSuccessResponse && typeof response.executionTime === 'number'
                ? response.executionTime
                : (this.context.startTime ? Date.now() - this.context.startTime : undefined);
            if (totalExecutionTime && typeof totalExecutionTime === 'number' && config.performanceThreshold && totalExecutionTime >= config.performanceThreshold) {
                this.loggerService.logPerformance(`${this.context.method} ${this.context.path}`, totalExecutionTime, {
                    requestId: this.context.requestId,
                    userId: this.context.userId,
                    responseCode: response.code,
                    hasError: isSuccessResponse ? !response.success : response.code >= 400,
                    processingTime,
                    responseSize: JSON.stringify(response).length
                });
            }
        }
        catch (error) {
            this.loggerService.error('Failed to log response', error.stack, 'ResponseManager');
        }
    }
    isSuccessResponse(response) {
        return 'success' in response && 'executionTime' in response;
    }
    isErrorResponse(response) {
        return 'success' in response && 'errors' in response && 'errorType' in response;
    }
    logResponseTransform(originalResponse, transformedResponse, format, processingTime) {
        if (!this.context) {
            return;
        }
        const config = response_logging_config_1.ResponseLoggingUtils.getConfig();
        if (!config.logTransformations) {
            return;
        }
        try {
            this.loggerService.logResponseTransform({
                requestId: this.context.requestId,
                fromFormat: 'standard',
                toFormat: format,
                originalData: response_logging_config_1.ResponseLoggingUtils.sanitizeData(originalResponse),
                transformedData: response_logging_config_1.ResponseLoggingUtils.sanitizeData(transformedResponse),
                duration: processingTime
            });
        }
        catch (error) {
            this.loggerService.error('Failed to log response transform', error.stack, 'ResponseManager');
        }
    }
    logResponseCache(action, cacheKey, ttl) {
        if (!this.context) {
            return;
        }
        const config = response_logging_config_1.ResponseLoggingUtils.getConfig();
        if (!config.logCacheOperations) {
            return;
        }
        try {
            this.loggerService.logResponseCache({
                requestId: this.context.requestId,
                action,
                cacheKey,
                ttl
            });
        }
        catch (error) {
            this.loggerService.error('Failed to log response cache', error.stack, 'ResponseManager');
        }
    }
};
exports.ResponseManagerService = ResponseManagerService;
exports.ResponseManagerService = ResponseManagerService = __decorate([
    (0, common_1.Injectable)({ scope: common_1.Scope.REQUEST }),
    __metadata("design:paramtypes", [logger_service_1.LoggerService])
], ResponseManagerService);
//# sourceMappingURL=response-manager.service.js.map