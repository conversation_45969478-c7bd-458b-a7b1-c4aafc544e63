"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ResponseTypeDetectorService = void 0;
const common_1 = require("@nestjs/common");
let ResponseTypeDetectorService = class ResponseTypeDetectorService {
    detectType(result) {
        if (result === null)
            return 'null';
        if (result === undefined)
            return 'undefined';
        if (this.isError(result))
            return 'error';
        if (this.isHttpResponse(result))
            return 'http-response';
        if (this.isDddResult(result))
            return 'ddd-result';
        const type = typeof result;
        if (type === 'string' || type === 'number' || type === 'boolean') {
            return type;
        }
        if (Array.isArray(result))
            return 'array';
        if (type === 'object')
            return 'object';
        return 'object';
    }
    isHttpResponse(result) {
        return (result &&
            typeof result === 'object' &&
            'code' in result &&
            'msg' in result &&
            'data' in result &&
            typeof result.code === 'number' &&
            typeof result.msg === 'string');
    }
    isError(result) {
        return result instanceof Error;
    }
    isDddResult(result) {
        return (result &&
            typeof result === 'object' &&
            'success' in result &&
            'timestamp' in result &&
            typeof result.success === 'boolean' &&
            (result.timestamp instanceof Date || typeof result.timestamp === 'string'));
    }
    isPaginatedData(result) {
        return (result &&
            typeof result === 'object' &&
            ('list' in result || 'data' in result) &&
            ('total' in result || 'count' in result) &&
            ('page' in result || 'pageNum' in result || 'current' in result) &&
            ('pageSize' in result || 'size' in result));
    }
    isEmpty(result) {
        if (result === null || result === undefined)
            return true;
        if (typeof result === 'string' && result.trim() === '')
            return true;
        if (Array.isArray(result) && result.length === 0)
            return true;
        if (typeof result === 'object' && Object.keys(result).length === 0)
            return true;
        return false;
    }
    getDataDescription(result) {
        const type = this.detectType(result);
        switch (type) {
            case 'null':
            case 'undefined':
                return '空数据';
            case 'string':
                return `字符串 (长度: ${result.length})`;
            case 'number':
                return `数字 (${result})`;
            case 'boolean':
                return `布尔值 (${result})`;
            case 'array':
                return `数组 (长度: ${result.length})`;
            case 'object':
                if (this.isPaginatedData(result)) {
                    return `分页数据 (总数: ${result.total || result.count || '未知'})`;
                }
                return `对象 (字段数: ${Object.keys(result).length})`;
            case 'http-response':
                return `HTTP响应 (状态码: ${result.code})`;
            case 'ddd-result':
                return `DDD结果 (成功: ${result.success})`;
            case 'error':
                return `错误 (${result.message || '未知错误'})`;
            default:
                return `未知类型 (${type})`;
        }
    }
    extractErrorInfo(error) {
        if (error instanceof Error) {
            return {
                message: error.message || '系统错误',
                errors: [error.message || '未知错误'],
                code: 500
            };
        }
        if (typeof error === 'string') {
            return {
                message: error,
                errors: [error],
                code: 500
            };
        }
        if (error && typeof error === 'object') {
            if ('status' in error && 'message' in error) {
                return {
                    message: error.message || '请求错误',
                    errors: Array.isArray(error.errors) ? error.errors : [error.message || '请求错误'],
                    code: error.status || 500
                };
            }
            if ('errors' in error && Array.isArray(error.errors)) {
                return {
                    message: '参数验证失败',
                    errors: error.errors,
                    code: 400
                };
            }
            if ('success' in error && error.success === false) {
                return {
                    message: error.message || '操作失败',
                    errors: error.errors || [error.message || '操作失败'],
                    code: 500
                };
            }
            return {
                message: error.message || '系统错误',
                errors: [error.message || JSON.stringify(error)],
                code: 500
            };
        }
        return {
            message: '未知错误',
            errors: ['系统发生未知错误'],
            code: 500
        };
    }
    normalizeData(result) {
        const type = this.detectType(result);
        switch (type) {
            case 'null':
            case 'undefined':
                return null;
            case 'http-response':
                return result.data;
            case 'ddd-result':
                return result.data;
            case 'error':
                return null;
            default:
                return result;
        }
    }
    needsSpecialHandling(result) {
        return this.isHttpResponse(result) || this.isDddResult(result) || this.isError(result);
    }
    getSuccessStatus(result) {
        const type = this.detectType(result);
        switch (type) {
            case 'error':
                return false;
            case 'ddd-result':
                return result.success === true;
            case 'http-response':
                return result.code >= 200 && result.code < 300;
            case 'null':
            case 'undefined':
                return true;
            default:
                return true;
        }
    }
    getResponseMessage(result) {
        const type = this.detectType(result);
        switch (type) {
            case 'ddd-result':
                return result.message || (result.success ? '操作成功' : '操作失败');
            case 'http-response':
                return result.msg || '操作成功';
            case 'error':
                return result.message || '系统错误';
            case 'null':
            case 'undefined':
                return '操作成功';
            default:
                return '操作成功';
        }
    }
};
exports.ResponseTypeDetectorService = ResponseTypeDetectorService;
exports.ResponseTypeDetectorService = ResponseTypeDetectorService = __decorate([
    (0, common_1.Injectable)()
], ResponseTypeDetectorService);
//# sourceMappingURL=response-type-detector.service.js.map