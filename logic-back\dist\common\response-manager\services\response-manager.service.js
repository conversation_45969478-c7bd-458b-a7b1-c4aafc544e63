"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ResponseManagerService = void 0;
const common_1 = require("@nestjs/common");
const http_response_interface_1 = require("../../../web/http_response_result/http-response.interface");
const logger_service_1 = require("../../logger/logger.service");
let ResponseManagerService = class ResponseManagerService {
    logger;
    requestPool;
    performanceMonitor;
    config;
    constructor(logger, requestPool, performanceMonitor, config) {
        this.logger = logger;
        this.requestPool = requestPool;
        this.performanceMonitor = performanceMonitor;
        this.config = config;
    }
    async handleCommandResult(commandResult, context, format = this.config.defaultFormat) {
        const fullContext = this.enrichContext(context, 'COMMAND');
        const metrics = this.performanceMonitor.endMonitoring(fullContext.requestId);
        const enhancedResponse = {
            context: fullContext,
            data: commandResult.data,
            success: commandResult.success,
            message: commandResult.message,
            errors: commandResult.errors,
            code: commandResult.success ? http_response_interface_1.SUCCESS_CODE : http_response_interface_1.ERROR_CODE,
            metrics,
            timestamp: commandResult.timestamp,
            fromCache: false
        };
        this.logResponse(enhancedResponse, commandResult.success ? 'info' : 'error');
        return enhancedResponse;
    }
    async handleQueryResult(queryResult, context, format = this.config.defaultFormat) {
        const fullContext = this.enrichContext(context, 'QUERY');
        const metrics = this.performanceMonitor.endMonitoring(fullContext.requestId);
        const enhancedResponse = {
            context: fullContext,
            data: queryResult.data,
            success: queryResult.success,
            message: queryResult.message,
            errors: queryResult.errors,
            code: queryResult.success ? http_response_interface_1.SUCCESS_CODE : http_response_interface_1.ERROR_CODE,
            metrics,
            timestamp: queryResult.timestamp,
            fromCache: queryResult.fromCache,
            cacheKey: queryResult.cacheKey
        };
        if (queryResult.fromCache !== undefined) {
            this.performanceMonitor.recordCacheOperation(fullContext.requestId, queryResult.fromCache);
        }
        this.logResponse(enhancedResponse, queryResult.success ? 'info' : 'error');
        return enhancedResponse;
    }
    createContext(request, module, operation) {
        const requestId = this.generateRequestId();
        this.performanceMonitor.startMonitoring(requestId);
        const context = {
            requestId,
            userId: request.user?.id || request.userId,
            sessionId: request.sessionId || request.headers?.['x-session-id'],
            clientIp: request.ip || request.connection?.remoteAddress,
            userAgent: request.headers?.['user-agent'],
            path: request.path || request.url,
            method: request.method,
            module,
            operation,
            startTime: Date.now(),
            metadata: {
                headers: this.sanitizeHeaders(request.headers),
                query: request.query,
                params: request.params
            }
        };
        return context;
    }
    toHttpResponse(enhancedResponse) {
        return {
            code: enhancedResponse.code,
            msg: enhancedResponse.message || (enhancedResponse.success ? '操作成功' : '操作失败'),
            data: enhancedResponse.data
        };
    }
    async getPoolStatus() {
        return this.requestPool.getStatus();
    }
    logResponse(enhancedResponse, level = 'info') {
        const { context, success, message, errors, metrics } = enhancedResponse;
        const logData = {
            requestId: context.requestId,
            module: context.module,
            operation: context.operation,
            userId: context.userId,
            success,
            message,
            errors,
            executionTime: metrics.executionTime,
            memoryUsage: metrics.memoryUsage,
            dbQueryCount: metrics.dbQueryCount,
            cacheHits: metrics.cacheHits,
            cacheMisses: metrics.cacheMisses,
            path: context.path,
            method: context.method,
            clientIp: context.clientIp
        };
        const logMessage = `[${context.requestId}] ${context.module}.${context.operation} - ${message || (success ? '成功' : '失败')}`;
        switch (level) {
            case 'info':
                this.logger.log(logMessage, JSON.stringify(logData));
                break;
            case 'warn':
                this.logger.warn(logMessage, JSON.stringify(logData));
                break;
            case 'error':
                this.logger.error(logMessage, JSON.stringify(logData));
                break;
        }
        this.logger.logBusiness(context.module || 'Unknown', context.operation || 'Unknown', logData, success ? undefined : new Error(errors?.join(', ') || message));
    }
    generateRequestId() {
        const timestamp = Date.now();
        const random = Math.floor(Math.random() * 1000);
        return `req_${timestamp}_${random}`;
    }
    enrichContext(partialContext, operationType) {
        const requestId = partialContext.requestId || this.generateRequestId();
        return {
            requestId,
            userId: partialContext.userId,
            sessionId: partialContext.sessionId,
            clientIp: partialContext.clientIp,
            userAgent: partialContext.userAgent,
            path: partialContext.path,
            method: partialContext.method,
            module: partialContext.module || 'DDD',
            operation: partialContext.operation || operationType,
            startTime: partialContext.startTime || Date.now(),
            metadata: partialContext.metadata || {}
        };
    }
    sanitizeHeaders(headers) {
        if (!headers)
            return {};
        const sanitized = { ...headers };
        delete sanitized.authorization;
        delete sanitized.cookie;
        delete sanitized['x-api-key'];
        return sanitized;
    }
};
exports.ResponseManagerService = ResponseManagerService;
exports.ResponseManagerService = ResponseManagerService = __decorate([
    (0, common_1.Injectable)(),
    __param(1, (0, common_1.Inject)('REQUEST_POOL')),
    __param(2, (0, common_1.Inject)('PERFORMANCE_MONITOR')),
    __param(3, (0, common_1.Inject)('RESPONSE_MANAGER_CONFIG')),
    __metadata("design:paramtypes", [logger_service_1.LoggerService, Object, Object, Object])
], ResponseManagerService);
//# sourceMappingURL=response-manager.service.js.map