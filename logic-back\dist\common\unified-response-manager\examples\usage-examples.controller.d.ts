import { IUnifiedResponseManager } from '../interfaces/unified-response.interface';
import { HttpResponseResultService } from '../../../web/http_response_result/http_response_result.service';
export declare class UnifiedResponseExampleController {
    private readonly responseManager;
    private readonly httpResponseService;
    constructor(responseManager: IUnifiedResponseManager, httpResponseService: HttpResponseResultService);
    getString(): string;
    getNumber(): number;
    getBoolean(): boolean;
    getObject(): any;
    getArray(): any[];
    getNull(): null;
    getUndefined(): undefined;
    getHttpResponse(): import("../../../web/http_response_result/http-response.interface").HttpResponse<{
        id: number;
        name: string;
    }>;
    getPaginatedData(page?: number, size?: number): {
        list: {
            id: number;
            name: string;
            email: string;
        }[];
        total: number;
        page: number;
        pageSize: number;
        totalPages: number;
    };
    throwError(): never;
    getAsyncData(): Promise<any>;
    highPerformanceOperation(data: any): Promise<any>;
    realtimeOperation(data: any): Promise<any>;
    batchOperation(items: any[]): Promise<any>;
    getDisabledResponse(): {
        customFormat: boolean;
        message: string;
        timestamp: Date;
    };
    customConfigOperation(data: any): Promise<any>;
    manualResponseHandling(data: any): Promise<any>;
    getPoolStatus(): Promise<import("../interfaces/unified-response.interface").RequestPoolStatus>;
    simulateDatabaseOperation(): Promise<any>;
    getComplexObject(): any;
}
