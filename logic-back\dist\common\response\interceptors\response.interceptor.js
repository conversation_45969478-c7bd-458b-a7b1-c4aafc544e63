"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GlobalResponseInterceptor = exports.ResponseInterceptor = void 0;
const common_1 = require("@nestjs/common");
const core_1 = require("@nestjs/core");
const operators_1 = require("rxjs/operators");
const logger_service_1 = require("../../logger/logger.service");
const response_manager_service_1 = require("../services/response-manager.service");
const response_decorator_1 = require("../decorators/response.decorator");
let ResponseInterceptor = class ResponseInterceptor {
    responseManager;
    reflector;
    loggerService;
    constructor(responseManager, reflector, loggerService) {
        this.responseManager = responseManager;
        this.reflector = reflector;
        this.loggerService = loggerService;
    }
    intercept(context, next) {
        const request = context.switchToHttp().getRequest();
        const handler = context.getHandler();
        const controllerClass = context.getClass();
        const methodName = handler.name;
        const config = this.reflector.get(response_decorator_1.RESPONSE_CONFIG_KEY, handler) || {};
        const requestId = this.generateRequestId();
        const startTime = Date.now();
        this.responseManager.setContext({
            requestId,
            startTime,
            path: request.path,
            method: request.method,
            ip: request.ip,
            userAgent: request.get('User-Agent'),
            userId: request.user?.id,
            module: controllerClass.name,
            operation: methodName,
        });
        this.loggerService.logResponseProcessing({
            requestId,
            stage: 'interceptor',
            action: 'start_processing',
            details: {
                controller: controllerClass.name,
                method: methodName,
                path: request.path,
                httpMethod: request.method,
                hasConfig: Object.keys(config).length > 0,
                configFormat: config.format,
            }
        });
        return next.handle().pipe((0, operators_1.map)((data) => {
            const processingStartTime = Date.now();
            try {
                let finalResponse;
                if (this.isStandardResponse(data)) {
                    this.loggerService.logResponseProcessing({
                        requestId,
                        stage: 'interceptor',
                        action: 'standard_response_detected',
                        details: { responseType: 'standard' }
                    });
                    finalResponse = this.responseManager.format(data, config.format);
                }
                else if (data === null || data === undefined) {
                    this.loggerService.logResponseProcessing({
                        requestId,
                        stage: 'interceptor',
                        action: 'null_data_response',
                        details: { responseType: 'null' }
                    });
                    const response = this.responseManager.success(null, config.successMessage, {
                        includeExecutionTime: config.includeExecutionTime,
                        includeRequestId: config.includeRequestId,
                    });
                    finalResponse = this.responseManager.format(response, config.format);
                }
                else if (this.isPaginatedData(data)) {
                    this.loggerService.logResponseProcessing({
                        requestId,
                        stage: 'interceptor',
                        action: 'paginated_response',
                        details: {
                            responseType: 'paginated',
                            dataCount: (data.data || data.list || []).length,
                            page: data.pagination?.page || data.page || 1,
                            total: data.pagination?.total || data.total || 0
                        }
                    });
                    const response = this.responseManager.paginated(data.data || data.list || [], {
                        page: data.pagination?.page || data.page || 1,
                        size: data.pagination?.size || data.size || 10,
                        total: data.pagination?.total || data.total || 0,
                    }, config.successMessage, {
                        includeExecutionTime: config.includeExecutionTime,
                        includeRequestId: config.includeRequestId,
                    });
                    finalResponse = this.responseManager.format(response, config.format);
                }
                else {
                    this.loggerService.logResponseProcessing({
                        requestId,
                        stage: 'interceptor',
                        action: 'success_response',
                        details: {
                            responseType: 'success',
                            hasData: !!data,
                            dataType: typeof data
                        }
                    });
                    const response = this.responseManager.success(data, config.successMessage, {
                        includeExecutionTime: config.includeExecutionTime,
                        includeRequestId: config.includeRequestId,
                    });
                    finalResponse = this.responseManager.format(response, config.format);
                }
                this.loggerService.logResponseProcessing({
                    requestId,
                    stage: 'interceptor',
                    action: 'complete_processing',
                    duration: Date.now() - processingStartTime,
                    details: {
                        totalExecutionTime: Date.now() - startTime,
                        finalFormat: config.format || 'standard'
                    }
                });
                return finalResponse;
            }
            catch (error) {
                this.loggerService.logResponseProcessing({
                    requestId,
                    stage: 'interceptor',
                    action: 'processing_error',
                    duration: Date.now() - processingStartTime,
                    error
                });
                throw error;
            }
        }));
    }
    isStandardResponse(data) {
        return (data &&
            typeof data === 'object' &&
            'code' in data &&
            'message' in data &&
            'timestamp' in data);
    }
    isPaginatedData(data) {
        return (data &&
            typeof data === 'object' &&
            (('data' in data && 'pagination' in data) ||
                ('list' in data && 'pagination' in data) ||
                ('data' in data && 'page' in data && 'total' in data) ||
                ('list' in data && 'page' in data && 'total' in data)));
    }
    generateRequestId() {
        return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
};
exports.ResponseInterceptor = ResponseInterceptor;
exports.ResponseInterceptor = ResponseInterceptor = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [response_manager_service_1.ResponseManagerService,
        core_1.Reflector,
        logger_service_1.LoggerService])
], ResponseInterceptor);
let GlobalResponseInterceptor = class GlobalResponseInterceptor {
    responseManager;
    loggerService;
    constructor(responseManager, loggerService) {
        this.responseManager = responseManager;
        this.loggerService = loggerService;
    }
    intercept(context, next) {
        const request = context.switchToHttp().getRequest();
        const excludePaths = [
            '/health',
            '/metrics',
            '/weixin/message',
            '/api-docs',
            '/swagger',
        ];
        if (excludePaths.some(path => request.path.includes(path))) {
            return next.handle();
        }
        if (!this.responseManager) {
            console.error('ResponseManager is not injected in GlobalResponseInterceptor');
            return next.handle();
        }
        const requestId = this.generateRequestId();
        const startTime = Date.now();
        try {
            this.responseManager.setContext({
                requestId,
                startTime,
                path: request.path,
                method: request.method,
                ip: request.ip,
                userAgent: request.get('User-Agent'),
                userId: request.user?.id,
            });
        }
        catch (error) {
            console.error('Error setting context in GlobalResponseInterceptor:', error);
            return next.handle();
        }
        if (this.loggerService) {
            try {
                this.loggerService.logResponseProcessing({
                    requestId,
                    stage: 'interceptor',
                    action: 'global_start_processing',
                    details: {
                        path: request.path,
                        method: request.method,
                        userAgent: request.get('User-Agent'),
                        ip: request.ip,
                    }
                });
            }
            catch (error) {
                console.error('Error logging in GlobalResponseInterceptor:', error);
            }
        }
        return next.handle().pipe((0, operators_1.map)((data) => {
            const processingStartTime = Date.now();
            try {
                let finalResponse;
                if (this.isStandardResponse(data)) {
                    if (this.loggerService) {
                        try {
                            this.loggerService.logResponseProcessing({
                                requestId,
                                stage: 'interceptor',
                                action: 'global_standard_response',
                                details: { responseType: 'already_standard' }
                            });
                        }
                        catch (error) {
                            console.error('Error logging standard response:', error);
                        }
                    }
                    finalResponse = data;
                }
                else {
                    if (this.loggerService) {
                        try {
                            this.loggerService.logResponseProcessing({
                                requestId,
                                stage: 'interceptor',
                                action: 'global_transform_response',
                                details: {
                                    responseType: 'transform_to_standard',
                                    originalType: typeof data
                                }
                            });
                        }
                        catch (error) {
                            console.error('Error logging transform response:', error);
                        }
                    }
                    finalResponse = this.responseManager ? this.responseManager.success(data) : data;
                }
                if (this.loggerService) {
                    try {
                        this.loggerService.logResponseProcessing({
                            requestId,
                            stage: 'interceptor',
                            action: 'global_complete_processing',
                            duration: Date.now() - processingStartTime,
                            details: {
                                totalExecutionTime: Date.now() - startTime
                            }
                        });
                    }
                    catch (error) {
                        console.error('Error logging complete processing:', error);
                    }
                }
                return finalResponse;
            }
            catch (error) {
                if (this.loggerService) {
                    try {
                        this.loggerService.logResponseProcessing({
                            requestId,
                            stage: 'interceptor',
                            action: 'global_processing_error',
                            duration: Date.now() - processingStartTime,
                            error
                        });
                    }
                    catch (logError) {
                        console.error('Error logging processing error:', logError);
                    }
                }
                throw error;
            }
        }));
    }
    isStandardResponse(data) {
        return (data &&
            typeof data === 'object' &&
            'code' in data &&
            'message' in data &&
            'timestamp' in data);
    }
    generateRequestId() {
        return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
};
exports.GlobalResponseInterceptor = GlobalResponseInterceptor;
exports.GlobalResponseInterceptor = GlobalResponseInterceptor = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [response_manager_service_1.ResponseManagerService,
        logger_service_1.LoggerService])
], GlobalResponseInterceptor);
//# sourceMappingURL=response.interceptor.js.map