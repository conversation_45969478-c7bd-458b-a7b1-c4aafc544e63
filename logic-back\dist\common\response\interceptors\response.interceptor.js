"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GlobalResponseInterceptor = exports.ResponseInterceptor = void 0;
const common_1 = require("@nestjs/common");
const core_1 = require("@nestjs/core");
const operators_1 = require("rxjs/operators");
const response_manager_service_1 = require("../services/response-manager.service");
const response_decorator_1 = require("../decorators/response.decorator");
let ResponseInterceptor = class ResponseInterceptor {
    responseManager;
    reflector;
    constructor(responseManager, reflector) {
        this.responseManager = responseManager;
        this.reflector = reflector;
    }
    intercept(context, next) {
        const request = context.switchToHttp().getRequest();
        const handler = context.getHandler();
        const config = this.reflector.get(response_decorator_1.RESPONSE_CONFIG_KEY, handler) || {};
        this.responseManager.setContext({
            requestId: this.generateRequestId(),
            startTime: Date.now(),
            path: request.path,
            method: request.method,
            ip: request.ip,
            userAgent: request.get('User-Agent'),
            userId: request.user?.id,
        });
        return next.handle().pipe((0, operators_1.map)((data) => {
            if (this.isStandardResponse(data)) {
                return this.responseManager.format(data, config.format);
            }
            if (data === null || data === undefined) {
                const response = this.responseManager.success(null, config.successMessage, {
                    includeExecutionTime: config.includeExecutionTime,
                    includeRequestId: config.includeRequestId,
                });
                return this.responseManager.format(response, config.format);
            }
            if (this.isPaginatedData(data)) {
                const response = this.responseManager.paginated(data.data || data.list || [], {
                    page: data.pagination?.page || data.page || 1,
                    size: data.pagination?.size || data.size || 10,
                    total: data.pagination?.total || data.total || 0,
                }, config.successMessage, {
                    includeExecutionTime: config.includeExecutionTime,
                    includeRequestId: config.includeRequestId,
                });
                return this.responseManager.format(response, config.format);
            }
            const response = this.responseManager.success(data, config.successMessage, {
                includeExecutionTime: config.includeExecutionTime,
                includeRequestId: config.includeRequestId,
            });
            return this.responseManager.format(response, config.format);
        }));
    }
    isStandardResponse(data) {
        return (data &&
            typeof data === 'object' &&
            'code' in data &&
            'message' in data &&
            'timestamp' in data);
    }
    isPaginatedData(data) {
        return (data &&
            typeof data === 'object' &&
            (('data' in data && 'pagination' in data) ||
                ('list' in data && 'pagination' in data) ||
                ('data' in data && 'page' in data && 'total' in data) ||
                ('list' in data && 'page' in data && 'total' in data)));
    }
    generateRequestId() {
        return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
};
exports.ResponseInterceptor = ResponseInterceptor;
exports.ResponseInterceptor = ResponseInterceptor = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [response_manager_service_1.ResponseManagerService,
        core_1.Reflector])
], ResponseInterceptor);
let GlobalResponseInterceptor = class GlobalResponseInterceptor {
    responseManager;
    constructor(responseManager) {
        this.responseManager = responseManager;
    }
    intercept(context, next) {
        const request = context.switchToHttp().getRequest();
        const excludePaths = [
            '/health',
            '/metrics',
            '/weixin/message',
            '/api-docs',
            '/swagger',
        ];
        if (excludePaths.some(path => request.path.includes(path))) {
            return next.handle();
        }
        this.responseManager.setContext({
            requestId: this.generateRequestId(),
            startTime: Date.now(),
            path: request.path,
            method: request.method,
            ip: request.ip,
            userAgent: request.get('User-Agent'),
            userId: request.user?.id,
        });
        return next.handle().pipe((0, operators_1.map)((data) => {
            if (this.isStandardResponse(data)) {
                return data;
            }
            return this.responseManager.success(data);
        }));
    }
    isStandardResponse(data) {
        return (data &&
            typeof data === 'object' &&
            'code' in data &&
            'message' in data &&
            'timestamp' in data);
    }
    generateRequestId() {
        return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
};
exports.GlobalResponseInterceptor = GlobalResponseInterceptor;
exports.GlobalResponseInterceptor = GlobalResponseInterceptor = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [response_manager_service_1.ResponseManagerService])
], GlobalResponseInterceptor);
//# sourceMappingURL=response.interceptor.js.map