import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Query,
  HttpCode,
  HttpStatus,
  UseGuards,
  Req,
  UsePipes,
  ValidationPipe
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiBearerAuth,
  ApiParam,
  ApiQuery
} from '@nestjs/swagger';
import { Request } from 'express';

// 导入响应管理器
import {
  ResponseManagerService,
  ResponseCacheService,
  ApiSuccessResponse,
  ApiResponseDocs,
  BusinessException,
  ValidationException,
  MediumCache,
  UserCache,
  NoCache,
} from '../../../common/response';

import { IpLocationFacadeService } from '../application/services/ip-location-facade.service';
import { IpQueryRequestDto } from '../application/dto/requests/ip-query.request.dto';
import { RiskCheckRequestDto } from '../application/dto/requests/risk-check.request.dto';
import { TrustLocationRequestDto } from '../application/dto/requests/trust-location.request.dto';
import { LocationInfoResponseDto } from '../application/dto/responses/location-info.response.dto';
import { RiskAssessmentResponseDto } from '../application/dto/responses/risk-assessment.response.dto';
import { LocationStatsResponseDto } from '../application/dto/responses/location-stats.response.dto';

/**
 * IP地理位置控制器
 * 基于DDD架构提供IP地理位置查询、风险评估、用户位置统计等API接口
 * 支持新的门面服务和原有应用服务
 */
@ApiTags('IP地理位置')
@ApiBearerAuth('access-token')
@Controller('api/v1/ip-location')
@UsePipes(new ValidationPipe({
  transform: true,
  transformOptions: { enableImplicitConversion: true },
  whitelist: true,
  forbidNonWhitelisted: true
}))
export class IpLocationController {
  constructor(
    // 统一使用门面服务作为唯一依赖
    private readonly ipLocationFacadeService: IpLocationFacadeService,
    // 响应管理器服务
    private readonly responseManager: ResponseManagerService,
    private readonly cacheService: ResponseCacheService,
  ) {}



  /**
   * 查询IP地理位置信息 (DDD架构版本)
   */
  @Get('query')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: '查询IP地理位置 (DDD架构)',
    description: '基于DDD架构的IP地理位置查询，性能更优，功能更强'
  })
  @ApiQuery({ name: 'ip', description: 'IP地址', example: '**************' })
  @ApiQuery({
    name: 'includeRisk',
    description: '是否包含风险评估',
    required: false,
    type: Boolean,
    example: false
  })
  @ApiSuccessResponse('IP地理位置查询成功')
  @MediumCache(300) // 5分钟缓存
  async queryIpLocationV2(@Query() query: IpQueryRequestDto) {
    try {
      const result = await this.ipLocationFacadeService.getLocationByIP(
        query.ip,
        query.includeRisk
      );

      if (!result.success) {
        // 使用业务异常替代普通错误
        throw new BusinessException((result as any).error || 'IP地理位置查询失败', 'IP_QUERY_FAILED');
      }

      // 直接返回数据，响应管理器会自动包装
      return {
        location: result.data,
        meta: {
          executionTime: (result as any).executionTime,
          fromCache: (result as any).fromCache,
          ip: query.ip,
          includeRisk: query.includeRisk || false,
        }
      };
    } catch (error) {
      if (error instanceof BusinessException) {
        throw error;
      }
      throw new BusinessException('IP地理位置查询服务异常', 'IP_SERVICE_ERROR');
    }
  }

  /**
   * 登录风险检查
   */
  @Post('check-risk')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: '登录风险检查',
    description: '基于IP地址和用户历史进行登录风险评估'
  })
  @ApiResponseDocs({
    success: {
      message: '风险评估成功',
      type: RiskAssessmentResponseDto
    }
  })
  @NoCache() // 风险检查不缓存，确保实时性
  async checkLoginRisk(@Body() request: RiskCheckRequestDto) {
    try {
      // 验证请求参数
      if (!request.userId || !request.ipAddress) {
        throw new ValidationException('参数验证失败', [
          !request.userId ? '用户ID不能为空' : '',
          !request.ipAddress ? 'IP地址不能为空' : ''
        ].filter(Boolean));
      }

      const result = await this.ipLocationFacadeService.assessLoginRisk(
        request.userId,
        request.ipAddress,
        request.userAgent,
        request.sessionId
      );

      if (!result.success) {
        throw new BusinessException((result as any).error || '登录风险评估失败', 'RISK_ASSESSMENT_FAILED');
      }

      // 返回风险评估结果
      return {
        riskAssessment: result.data,
        meta: {
          userId: request.userId,
          ipAddress: request.ipAddress,
          assessmentTime: new Date().toISOString(),
        }
      };
    } catch (error) {
      if (error instanceof BusinessException || error instanceof ValidationException) {
        throw error;
      }
      throw new BusinessException('风险评估服务异常', 'RISK_SERVICE_ERROR');
    }
  }

  /**
   * 获取用户位置统计
   */
  @Get('user/:userId/stats')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: '获取用户位置统计',
    description: '获取用户的常用登录地统计信息'
  })
  @ApiParam({ name: 'userId', description: '用户ID', type: Number })
  @ApiQuery({
    name: 'days',
    description: '统计天数',
    required: false,
    type: Number,
    example: 30
  })
  @ApiQuery({
    name: 'includeTrusted',
    description: '是否包含可信位置',
    required: false,
    type: Boolean,
    example: true
  })
  @ApiResponseDocs({
    success: {
      message: '获取用户位置统计成功',
      type: LocationStatsResponseDto
    }
  })
  @UserCache(600) // 10分钟用户相关缓存
  async getUserLocationStats(
    @Param('userId') userId: number,
    @Query('days') days: number = 30,
    @Query('includeTrusted') includeTrusted: boolean = true
  ) {
    try {
      // 验证参数
      if (!userId || userId <= 0) {
        throw new ValidationException('参数验证失败', ['用户ID必须是正整数']);
      }

      if (days && (days <= 0 || days > 365)) {
        throw new ValidationException('参数验证失败', ['统计天数必须在1-365之间']);
      }

      const result = await this.ipLocationFacadeService.getUserLocationStats(userId, days);

      if (!result.success) {
        throw new BusinessException((result as any).error || '获取用户位置统计失败', 'USER_STATS_FAILED');
      }

      // 返回统计数据
      return {
        statistics: result.data,
        meta: {
          userId,
          days,
          includeTrusted,
          generatedAt: new Date().toISOString(),
        }
      };
    } catch (error) {
      if (error instanceof BusinessException || error instanceof ValidationException) {
        throw error;
      }
      throw new BusinessException('用户位置统计服务异常', 'STATS_SERVICE_ERROR');
    }
  }

  /**
   * 设置可信登录地
   */
  @Post('user/:userId/trust')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: '设置可信登录地',
    description: '将指定位置设置为用户的可信登录地'
  })
  @ApiParam({ name: 'userId', description: '用户ID', type: Number })
  @ApiSuccessResponse('可信登录地设置成功')
  @NoCache() // 写操作不缓存
  async setTrustedLocation(
    @Param('userId') userId: number,
    @Body() request: TrustLocationRequestDto
  ) {
    try {
      // 验证参数
      const errors: string[] = [];
      if (!userId || userId <= 0) {
        errors.push('用户ID必须是正整数');
      }
      if (!request.province) {
        errors.push('省份不能为空');
      }
      if (!request.city) {
        errors.push('城市不能为空');
      }

      if (errors.length > 0) {
        throw new ValidationException('参数验证失败', errors);
      }

      const result = await this.ipLocationFacadeService.setTrustedLocation(
        userId,
        request.province,
        request.city,
        request.reason
      );

      if (!result.success) {
        throw new BusinessException((result as any).error || '设置可信位置失败', 'SET_TRUST_LOCATION_FAILED');
      }

      // 清理相关缓存
      await this.clearUserLocationCache(userId);

      return {
        trustedLocation: result.data,
        meta: {
          userId,
          province: request.province,
          city: request.city,
          reason: request.reason,
          setAt: new Date().toISOString(),
        }
      };
    } catch (error) {
      if (error instanceof BusinessException || error instanceof ValidationException) {
        throw error;
      }
      throw new BusinessException('设置可信位置服务异常', 'TRUST_LOCATION_SERVICE_ERROR');
    }
  }

  /**
   * 获取当前请求的IP地理位置（便捷接口）
   */
  @Get('current')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: '获取当前IP位置',
    description: '获取当前请求IP的地理位置信息'
  })
  @ApiResponseDocs({
    success: {
      message: '获取当前IP位置成功',
      type: LocationInfoResponseDto
    }
  })
  @MediumCache(180) // 3分钟缓存，因为用户IP可能变化
  async getCurrentIpLocation(@Req() request: Request) {
    try {
      const clientIp = this.extractClientIP(request);

      if (!clientIp) {
        throw new BusinessException('无法获取客户端IP地址', 'CLIENT_IP_NOT_FOUND');
      }

      const result = await this.ipLocationFacadeService.getLocationByIP(clientIp, false);

      if (!result.success) {
        throw new BusinessException((result as any).error || '获取当前IP位置失败', 'CURRENT_IP_QUERY_FAILED');
      }

      return {
        currentLocation: result.data,
        meta: {
          clientIp,
          requestTime: new Date().toISOString(),
          userAgent: request.get('User-Agent'),
        }
      };
    } catch (error) {
      if (error instanceof BusinessException) {
        throw error;
      }
      throw new BusinessException('获取当前IP位置服务异常', 'CURRENT_IP_SERVICE_ERROR');
    }
  }

  /**
   * 健康检查接口
   */
  @Get('health')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: '健康检查',
    description: 'IP地理位置服务健康检查'
  })
  @ApiSuccessResponse('服务健康检查成功')
  @NoCache() // 健康检查不缓存，确保实时性
  async healthCheck() {
    try {
      // 可以添加更详细的健康检查逻辑
      const cacheStats = await this.cacheService.getStats();

      return {
        service: 'ip-location',
        status: 'UP',
        timestamp: new Date().toISOString(),
        version: '2.0.0',
        cache: {
          enabled: true,
          totalKeys: cacheStats.totalKeys,
          totalSize: cacheStats.totalSize,
        },
        uptime: process.uptime(),
      };
    } catch (error) {
      // 即使健康检查出错，也返回基本信息
      return {
        service: 'ip-location',
        status: 'DEGRADED',
        timestamp: new Date().toISOString(),
        version: '2.0.0',
        error: 'Health check partially failed',
      };
    }
  }

  /**
   * 清理缓存接口
   */
  @Post('cache/clear')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: '清理缓存',
    description: '清理IP地理位置相关的缓存数据'
  })
  @ApiQuery({
    name: 'type',
    description: '缓存类型：all(全部), user(用户), ip(IP)',
    required: false,
    example: 'all'
  })
  @ApiQuery({
    name: 'target',
    description: '目标ID（用户ID或IP地址）',
    required: false
  })
  @ApiSuccessResponse('缓存清理成功')
  @NoCache()
  async clearCache(
    @Query('type') type: string = 'all',
    @Query('target') target?: string
  ) {
    try {
      let deletedCount = 0;
      let message = '';

      switch (type) {
        case 'user':
          if (!target) {
            throw new ValidationException('参数验证失败', ['清理用户缓存时必须提供用户ID']);
          }
          await this.clearUserLocationCache(parseInt(target));
          message = `已清理用户 ${target} 的位置缓存`;
          break;

        case 'ip':
          if (!target) {
            throw new ValidationException('参数验证失败', ['清理IP缓存时必须提供IP地址']);
          }
          await this.clearIpLocationCache(target);
          message = `已清理IP ${target} 的位置缓存`;
          break;

        case 'all':
        default:
          deletedCount = await this.cacheService.deleteByPattern('*ip*location*');
          message = `已清理所有IP地理位置缓存，共 ${deletedCount} 项`;
          break;
      }

      return {
        type,
        target,
        deletedCount,
        message,
        clearedAt: new Date().toISOString(),
      };
    } catch (error) {
      if (error instanceof ValidationException) {
        throw error;
      }
      throw new BusinessException('缓存清理失败', 'CACHE_CLEAR_FAILED');
    }
  }

  /**
   * 提取客户端IP地址
   * @param request HTTP请求对象
   * @returns 客户端IP地址
   */
  private extractClientIP(request: Request): string {
    // 检查各种可能的IP头部
    const forwarded = request.headers['x-forwarded-for'] as string;
    const realIp = request.headers['x-real-ip'] as string;
    const cfConnectingIp = request.headers['cf-connecting-ip'] as string; // Cloudflare
    const xClientIp = request.headers['x-client-ip'] as string;
    const xClusterClientIp = request.headers['x-cluster-client-ip'] as string;

    const clientIp = (request.socket as any)?.remoteAddress ||
                     (request as any)?.ip;

    // 添加详细的IP提取日志
    console.log('🖥️ [Backend] IP地址提取详情:', {
      请求路径: request.url,
      请求方法: request.method,
      请求头IP信息: {
        'x-forwarded-for': forwarded,
        'x-real-ip': realIp,
        'cf-connecting-ip': cfConnectingIp,
        'x-client-ip': xClientIp,
        'x-cluster-client-ip': xClusterClientIp,
      },
      连接IP信息: {
        'socket.remoteAddress': (request.socket as any)?.remoteAddress,
        'request.ip': (request as any)?.ip,
        '最终连接IP': clientIp
      },
      时间戳: new Date().toISOString()
    });

    // 按优先级检查各种IP头部
    const ipSources = [
      forwarded?.split(',')[0]?.trim(),  // x-forwarded-for (第一个IP)
      cfConnectingIp,                    // Cloudflare
      realIp,                           // x-real-ip
      xClientIp,                        // x-client-ip
      xClusterClientIp,                 // x-cluster-client-ip
      clientIp                          // 直连IP
    ];

    for (const ip of ipSources) {
      if (ip) {
        const cleanIp = this.cleanAndValidateIP(ip);
        if (cleanIp) {
          console.log('✅ [Backend] IP地址提取成功:', {
            原始IP: ip,
            清理后IP: cleanIp,
            来源: this.getIpSourceName(ip, {
              forwarded: forwarded?.split(',')[0]?.trim(),
              cfConnectingIp,
              realIp,
              xClientIp,
              xClusterClientIp,
              clientIp
            }),
            时间戳: new Date().toISOString()
          });
          return cleanIp;
        }
      }
    }

    // 默认返回本地IPv4地址（便于测试）
    console.log('⚠️ [Backend] 使用默认IP地址:', {
      原因: '所有IP源都无效',
      默认IP: '127.0.0.1',
      时间戳: new Date().toISOString()
    });
    return '127.0.0.1';
  }

  /**
   * 清理和验证IP地址
   * @param ip 原始IP地址
   * @returns 清理后的IP地址或null
   */
  private cleanAndValidateIP(ip: string): string | null {
    if (!ip) return null;

    let cleanIp = ip.trim();

    // 移除IPv6映射的IPv4前缀
    cleanIp = cleanIp.replace('::ffff:', '');

    // 处理IPv6本地回环地址
    if (cleanIp === '::1') {
      // 在测试环境中，将IPv6本地回环转换为IPv4
      return '127.0.0.1';
    }

    // 验证IP地址格式
    if (this.isValidIP(cleanIp)) {
      return cleanIp;
    }

    return null;
  }

  /**
   * 验证IP地址格式
   * @param ip IP地址
   * @returns 是否为有效IP
   */
  private isValidIP(ip: string): boolean {
    // IPv4格式验证
    const ipv4Regex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
    // IPv6格式验证（简化版）
    const ipv6Regex = /^([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$|^::1$|^::$/;

    return ipv4Regex.test(ip) || ipv6Regex.test(ip);
  }

  /**
   * 获取IP来源名称
   * @param ip 当前IP
   * @param sources IP来源对象
   * @returns IP来源描述
   */
  private getIpSourceName(ip: string, sources: {
    forwarded?: string;
    cfConnectingIp?: string;
    realIp?: string;
    xClientIp?: string;
    xClusterClientIp?: string;
    clientIp?: string;
  }): string {
    if (ip === sources.forwarded) return 'x-forwarded-for';
    if (ip === sources.cfConnectingIp) return 'cf-connecting-ip (Cloudflare)';
    if (ip === sources.realIp) return 'x-real-ip';
    if (ip === sources.xClientIp) return 'x-client-ip';
    if (ip === sources.xClusterClientIp) return 'x-cluster-client-ip';
    if (ip === sources.clientIp) return 'connection.remoteAddress';
    return '未知来源';
  }

  /**
   * 清理用户相关的位置缓存
   * @param userId 用户ID
   */
  private async clearUserLocationCache(userId: number): Promise<void> {
    try {
      // 清理用户统计缓存
      await this.cacheService.deleteByPattern(`*user:${userId}*stats*`);

      // 清理用户相关的位置缓存
      await this.cacheService.deleteByPattern(`*user:${userId}*location*`);

      // 记录缓存清理操作
      this.responseManager.logResponseCache('invalidate', `user_location_cache_${userId}`);
    } catch (error) {
      // 缓存清理失败不应该影响主要业务逻辑
      console.warn(`Failed to clear user location cache for user ${userId}:`, error);
    }
  }

  /**
   * 清理IP相关的缓存
   * @param ip IP地址
   */
  private async clearIpLocationCache(ip: string): Promise<void> {
    try {
      // 清理IP查询缓存
      await this.cacheService.deleteByPattern(`*${ip}*`);

      // 记录缓存清理操作
      this.responseManager.logResponseCache('invalidate', `ip_location_cache_${ip}`);
    } catch (error) {
      // 缓存清理失败不应该影响主要业务逻辑
      console.warn(`Failed to clear IP location cache for IP ${ip}:`, error);
    }
  }
}
