/**
 * 响应管理器模块导出
 */

// 接口和类型
export {
  BaseResponse,
  SuccessResponse,
  ErrorResponse,
  PaginatedResponse,
  ResponseFormat,
  ResponseConfig,
  RequestContext,
  IResponseManager,
  StatusCodes,
  DefaultMessages,
} from './interfaces/response.interface';

// 服务
export { ResponseManagerService } from './services/response-manager.service';

// 装饰器
export {
  RESPONSE_CONFIG_KEY,
  ResponseDecoratorConfig,
  ResponseConfig as ResponseConfigDecorator,
  ApiSuccessResponse,
  ApiPaginatedResponse,
  ApiSimpleResponse,
  ApiRestfulResponse,
  ApiErrorResponse,
  CommonErrorResponses,
  ApiResponseDocs,
} from './decorators/response.decorator';

// 拦截器
export {
  ResponseInterceptor,
  GlobalResponseInterceptor,
} from './interceptors/response.interceptor';

// 过滤器
export {
  BusinessException,
  ValidationException,
  ResponseExceptionFilter,
  HttpExceptionFilter,
} from './filters/response-exception.filter';

// 模块
export {
  ResponseModule,
  CustomResponseModule,
} from './response.module';

// 示例控制器
export { ResponseExampleController } from './examples/response-example.controller';
