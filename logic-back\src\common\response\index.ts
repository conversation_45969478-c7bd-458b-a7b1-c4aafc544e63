/**
 * 响应管理器模块导出
 */

// 接口和类型
export {
  BaseResponse,
  SuccessResponse,
  ErrorResponse,
  PaginatedResponse,
  ResponseFormat,
  ResponseConfig,
  RequestContext,
  IResponseManager,
  StatusCodes,
  DefaultMessages,
} from './interfaces/response.interface';

// 服务
export { ResponseManagerService } from './services/response-manager.service';
export { ResponseCacheService } from './services/response-cache.service';

// 装饰器
export {
  RESPONSE_CONFIG_KEY,
  ResponseDecoratorConfig,
  ResponseConfig as ResponseConfigDecorator,
  ApiSuccessResponse,
  ApiPaginatedResponse,
  ApiSimpleResponse,
  ApiRestfulResponse,
  ApiErrorResponse,
  CommonErrorResponses,
  ApiResponseDocs,
} from './decorators/response.decorator';

// 缓存装饰器
export {
  CACHE_CONFIG_KEY,
  CacheDecoratorConfig,
  Cache,
  ShortCache,
  MediumCache,
  LongCache,
  UserCache,
  ParamCache,
  FullCache,
  ConditionalCache,
  TaggedCache,
  CompressedCache,
  LargeDataCache,
  CustomKeyCache,
  NoCache,
  DevCache,
  ProdCache,
  WarmupCache,
  VersionedCache,
  PaginatedCache,
  SearchCache,
  StaticCache,
  RealtimeCache,
  CacheUtils,
} from './decorators/cache.decorator';

// 拦截器
export {
  ResponseInterceptor,
  GlobalResponseInterceptor,
} from './interceptors/response.interceptor';

// 缓存拦截器
export {
  CacheInterceptor,
  CacheClearInterceptor,
  CacheWarmupInterceptor,
} from './interceptors/cache.interceptor';

// 过滤器
export {
  BusinessException,
  ValidationException,
  ResponseExceptionFilter,
  HttpExceptionFilter,
} from './filters/response-exception.filter';

// 模块
export {
  ResponseModule,
  CustomResponseModule,
} from './response.module';

// 控制器
export { ResponseExampleController } from './examples/response-example.controller';
export { CacheManagementController } from './controllers/cache-management.controller';

// 配置
export {
  ResponseLoggingConfig,
  defaultResponseLoggingConfig,
  productionResponseLoggingConfig,
  developmentResponseLoggingConfig,
  getResponseLoggingConfig,
  ResponseLoggingUtils,
} from './config/response-logging.config';
