# IP地理位置模块 - 响应管理器集成指南

IP地理位置模块已成功集成响应管理器，提供统一的响应格式、智能缓存和详细日志记录功能。

## 🚀 集成功能

### 1. 统一响应格式
所有API现在返回标准化的响应格式：

```json
{
  "success": true,
  "code": 200,
  "message": "IP地理位置查询成功",
  "data": {
    "location": {
      "country": "中国",
      "province": "北京市",
      "city": "北京市",
      "isp": "电信"
    },
    "meta": {
      "executionTime": 45,
      "fromCache": false,
      "ip": "**************",
      "includeRisk": false
    }
  },
  "timestamp": "2024-01-01T00:00:00.000Z",
  "requestId": "req_1704067200000_abc123",
  "executionTime": 45
}
```

### 2. 智能缓存策略

#### IP查询缓存
```typescript
@Get('query')
@MediumCache(300) // 5分钟缓存
async queryIpLocationV2(@Query() query: IpQueryRequestDto) {
  // IP地理位置查询结果会被缓存5分钟
}
```

#### 用户统计缓存
```typescript
@Get('user/:userId/stats')
@UserCache(600) // 10分钟用户相关缓存
async getUserLocationStats(@Param('userId') userId: number) {
  // 用户位置统计会按用户ID缓存10分钟
}
```

#### 当前IP缓存
```typescript
@Get('current')
@MediumCache(180) // 3分钟缓存
async getCurrentIpLocation(@Req() request: Request) {
  // 当前IP位置缓存3分钟，考虑到用户IP可能变化
}
```

### 3. 错误处理增强

#### 业务异常
```typescript
// 原来的错误处理
throw new Error('查询失败');

// 现在的错误处理
throw new BusinessException('IP地理位置查询失败', 'IP_QUERY_FAILED');
```

#### 参数验证
```typescript
// 统一的参数验证
if (!userId || userId <= 0) {
  throw new ValidationException('参数验证失败', ['用户ID必须是正整数']);
}
```

### 4. 详细日志记录

系统会自动记录：
- API请求响应日志
- 缓存命中/未命中日志
- 性能监控日志
- 错误追踪日志

## 📊 API接口更新

### 1. IP地理位置查询
```http
GET /ip-location/query?ip=**************&includeRisk=false
```

**响应示例：**
```json
{
  "success": true,
  "code": 200,
  "message": "IP地理位置查询成功",
  "data": {
    "location": {
      "country": "中国",
      "province": "北京市", 
      "city": "北京市",
      "district": "海淀区",
      "isp": "电信",
      "latitude": 39.9042,
      "longitude": 116.4074
    },
    "meta": {
      "executionTime": 45,
      "fromCache": true,
      "ip": "**************",
      "includeRisk": false
    }
  },
  "timestamp": "2024-01-01T00:00:00.000Z",
  "requestId": "req_1704067200000_abc123",
  "executionTime": 45
}
```

### 2. 登录风险检查
```http
POST /ip-location/check-risk
Content-Type: application/json

{
  "userId": 123,
  "ipAddress": "**************",
  "userAgent": "Mozilla/5.0...",
  "sessionId": "session_123"
}
```

**响应示例：**
```json
{
  "success": true,
  "code": 200,
  "message": "风险评估成功",
  "data": {
    "riskAssessment": {
      "riskLevel": "LOW",
      "riskScore": 0.2,
      "factors": ["新IP地址", "正常时间段"],
      "recommendation": "允许登录"
    },
    "meta": {
      "userId": 123,
      "ipAddress": "**************",
      "assessmentTime": "2024-01-01T00:00:00.000Z"
    }
  },
  "timestamp": "2024-01-01T00:00:00.000Z",
  "requestId": "req_1704067200000_def456"
}
```

### 3. 用户位置统计
```http
GET /ip-location/user/123/stats?days=30&includeTrusted=true
```

**响应示例：**
```json
{
  "success": true,
  "code": 200,
  "message": "获取用户位置统计成功",
  "data": {
    "statistics": {
      "totalLocations": 5,
      "commonLocations": [
        {
          "province": "北京市",
          "city": "北京市",
          "count": 25,
          "percentage": 83.3,
          "isTrusted": true
        }
      ],
      "riskLocations": [],
      "lastLoginLocation": {
        "province": "北京市",
        "city": "北京市",
        "loginTime": "2024-01-01T00:00:00.000Z"
      }
    },
    "meta": {
      "userId": 123,
      "days": 30,
      "includeTrusted": true,
      "generatedAt": "2024-01-01T00:00:00.000Z"
    }
  },
  "timestamp": "2024-01-01T00:00:00.000Z",
  "requestId": "req_1704067200000_ghi789"
}
```

### 4. 设置可信登录地
```http
POST /ip-location/user/123/trust
Content-Type: application/json

{
  "province": "北京市",
  "city": "北京市",
  "reason": "用户常用登录地"
}
```

### 5. 获取当前IP位置
```http
GET /ip-location/current
```

### 6. 健康检查
```http
GET /ip-location/health
```

**响应示例：**
```json
{
  "success": true,
  "code": 200,
  "message": "服务健康检查成功",
  "data": {
    "service": "ip-location",
    "status": "UP",
    "timestamp": "2024-01-01T00:00:00.000Z",
    "version": "2.0.0",
    "cache": {
      "enabled": true,
      "totalKeys": 150,
      "totalSize": 1048576
    },
    "uptime": 3600
  },
  "timestamp": "2024-01-01T00:00:00.000Z",
  "requestId": "req_1704067200000_jkl012"
}
```

### 7. 缓存管理
```http
POST /ip-location/cache/clear?type=user&target=123
```

## 🔧 缓存管理

### 缓存策略
- **IP查询缓存**: 5分钟，减少重复查询
- **用户统计缓存**: 10分钟，用户相关数据
- **当前IP缓存**: 3分钟，考虑IP变化
- **风险检查**: 不缓存，确保实时性
- **健康检查**: 不缓存，确保实时性

### 缓存清理
```typescript
// 自动清理：设置可信位置后自动清理相关缓存
await this.clearUserLocationCache(userId);

// 手动清理：通过API清理缓存
POST /ip-location/cache/clear?type=all
POST /ip-location/cache/clear?type=user&target=123
POST /ip-location/cache/clear?type=ip&target=**************
```

## 📝 日志记录

### 自动日志
系统会自动记录：
```log
[INFO] API Response: GET /ip-location/query - IP地理位置查询成功
[INFO] Response Cache: hit - cacheKey: GET:/ip-location/query?ip=**************
[WARN] Performance: GET /ip-location/query - 1250ms (超过阈值)
[ERROR] API Error: POST /ip-location/check-risk - 参数验证失败
```

### 性能监控
- 执行时间超过1秒会记录性能警告
- 缓存命中率统计
- 错误率监控

## 🚨 错误处理

### 标准错误响应
```json
{
  "success": false,
  "code": 400,
  "message": "参数验证失败",
  "errors": ["用户ID必须是正整数", "IP地址不能为空"],
  "errorType": "VALIDATION",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "requestId": "req_1704067200000_error123"
}
```

### 错误类型
- `VALIDATION`: 参数验证错误
- `BUSINESS`: 业务逻辑错误
- `AUTH`: 认证授权错误
- `SYSTEM`: 系统内部错误

## 💡 最佳实践

### 1. 缓存使用
- 频繁查询的IP使用缓存
- 用户统计数据使用用户相关缓存
- 实时性要求高的接口不使用缓存

### 2. 错误处理
- 使用具体的业务异常类型
- 提供详细的错误信息
- 记录完整的错误上下文

### 3. 性能优化
- 监控API响应时间
- 合理设置缓存TTL
- 定期清理无效缓存

### 4. 日志分析
- 关注缓存命中率
- 监控错误趋势
- 分析性能瓶颈

## 🔗 相关文档

- [响应管理器基础文档](../../common/response/README.md)
- [缓存功能指南](../../common/response/CACHE_GUIDE.md)
- [IP地理位置服务文档](./README.md)
