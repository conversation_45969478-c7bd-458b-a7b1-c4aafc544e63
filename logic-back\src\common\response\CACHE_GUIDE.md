# Redis缓存功能使用指南

响应管理器现在集成了强大的Redis缓存功能，提供智能的响应缓存管理。

## 🚀 功能特性

- **自动缓存管理** - 装饰器驱动的自动缓存读写
- **多种缓存策略** - 短期、长期、用户相关、参数相关等
- **智能键生成** - 基于请求方法、路径、参数和用户的智能缓存键
- **缓存监控** - 完整的缓存统计和健康检查
- **手动操作** - 支持手动缓存操作和管理
- **数据压缩** - 可选的数据压缩功能
- **批量管理** - 支持按模式批量删除缓存

## 📦 快速开始

### 1. 基础缓存装饰器

```typescript
import { ShortCache, MediumCache, LongCache } from './common/response';

@Controller('api')
export class ApiController {
  // 短期缓存 - 1分钟
  @Get('news')
  @ShortCache(60)
  async getNews() {
    return await this.newsService.getLatestNews();
  }

  // 中期缓存 - 5分钟
  @Get('config')
  @MediumCache(300)
  async getConfig() {
    return await this.configService.getAppConfig();
  }

  // 长期缓存 - 1小时
  @Get('categories')
  @LongCache(3600)
  async getCategories() {
    return await this.categoryService.getAllCategories();
  }
}
```

### 2. 用户和参数相关缓存

```typescript
import { UserCache, ParamCache, FullCache } from './common/response';

@Controller('api')
export class UserController {
  // 用户相关缓存 - 缓存键包含用户ID
  @Get('profile')
  @UserCache(300)
  async getUserProfile() {
    return await this.userService.getCurrentUserProfile();
  }

  // 参数相关缓存 - 缓存键包含查询参数
  @Get('posts')
  @ParamCache(600)
  async getPosts(@Query('category') category: string) {
    return await this.postService.getPostsByCategory(category);
  }

  // 完整缓存 - 包含用户ID和参数
  @Get('recommendations')
  @FullCache(900)
  async getRecommendations(@Query('type') type: string) {
    return await this.recommendationService.getPersonalizedRecommendations(type);
  }
}
```

### 3. 特殊用途缓存

```typescript
import { StaticCache, NoCache, ConditionalCache } from './common/response';

@Controller('api')
export class SpecialController {
  // 静态数据缓存 - 24小时
  @Get('constants')
  @StaticCache(86400)
  async getConstants() {
    return await this.configService.getSystemConstants();
  }

  // 禁用缓存
  @Get('realtime')
  @NoCache()
  async getRealtimeData() {
    return await this.dataService.getCurrentTimestamp();
  }

  // 条件缓存 - 只有满足条件时才缓存
  @Get('conditional')
  @ConditionalCache((req) => req.query.cache === 'true', 300)
  async getConditionalData(@Query('cache') enableCache: string) {
    return await this.dataService.getExpensiveData();
  }
}
```

## 🔧 手动缓存操作

### 基础操作

```typescript
import { ResponseCacheService, ResponseManagerService } from './common/response';

@Controller('api')
export class ManualCacheController {
  constructor(
    private readonly cacheService: ResponseCacheService,
    private readonly responseManager: ResponseManagerService,
  ) {}

  @Get('manual-example')
  async manualCacheExample() {
    const cacheKey = { method: 'GET', path: '/api/data' };
    
    // 1. 检查缓存是否存在
    const exists = await this.cacheService.exists(cacheKey.method, cacheKey.path);
    
    if (exists) {
      // 2. 获取缓存数据
      const cached = await this.cacheService.get(cacheKey.method, cacheKey.path);
      if (cached) {
        return cached;
      }
    }

    // 3. 获取新数据
    const freshData = await this.dataService.getExpensiveData();
    
    // 4. 创建响应并缓存
    const response = this.responseManager.success(freshData, '获取数据成功');
    await this.cacheService.set(
      cacheKey.method,
      cacheKey.path,
      response,
      undefined, // params
      undefined, // userId
      300        // TTL (5分钟)
    );
    
    return response;
  }
}
```

### 批量操作

```typescript
@Controller('cache-admin')
export class CacheAdminController {
  constructor(private readonly cacheService: ResponseCacheService) {}

  // 获取缓存统计
  @Get('stats')
  async getCacheStats() {
    return await this.cacheService.getStats();
  }

  // 删除用户相关的所有缓存
  @Delete('user/:userId')
  async clearUserCache(@Param('userId') userId: string) {
    const deletedCount = await this.cacheService.deleteByPattern(`*_user:${userId}*`);
    return { deletedCount, message: `清理了用户 ${userId} 的 ${deletedCount} 个缓存` };
  }

  // 清空所有缓存
  @Delete('all')
  async clearAllCache() {
    const deletedCount = await this.cacheService.clear();
    return { deletedCount, message: `清空了 ${deletedCount} 个缓存项` };
  }
}
```

## 📊 缓存管理API

系统提供了完整的缓存管理API，访问 `/cache-management` 路径：

- `GET /cache-management/stats` - 获取缓存统计信息
- `GET /cache-management/exists` - 检查特定缓存是否存在
- `GET /cache-management/content` - 获取缓存内容
- `DELETE /cache-management/item` - 删除特定缓存
- `DELETE /cache-management/pattern/:pattern` - 按模式删除缓存
- `DELETE /cache-management/all` - 清空所有缓存
- `POST /cache-management/warmup` - 预热缓存
- `GET /cache-management/health` - 获取缓存健康状态

## 🎛️ 高级配置

### 自定义缓存配置

```typescript
import { Cache } from './common/response';

@Controller('api')
export class AdvancedController {
  @Get('custom')
  @Cache({
    defaultTtl: 1800,        // 30分钟
    enabled: true,
    compress: true,          // 启用压缩
    maxSize: 2 * 1024 * 1024, // 最大2MB
    includeUserId: true,
    includeParams: true,
    tags: ['user-data', 'important'], // 缓存标签
  })
  async getCustomCachedData() {
    return await this.dataService.getComplexData();
  }
}
```

### 自定义键生成器

```typescript
import { CustomKeyCache } from './common/response';

@Controller('api')
export class CustomKeyController {
  @Get('custom-key')
  @CustomKeyCache(
    (method, path, params, userId) => {
      // 自定义缓存键生成逻辑
      const timestamp = Math.floor(Date.now() / (5 * 60 * 1000)); // 5分钟时间窗口
      return `${method}:${path}:${userId}:${timestamp}`;
    },
    300
  )
  async getTimeBasedCache() {
    return await this.dataService.getTimeBasedData();
  }
}
```

## 🔍 监控和调试

### 缓存日志

系统会自动记录缓存操作日志：

```typescript
// 缓存命中日志
[INFO] Response Cache: hit - cacheKey: GET:/api/users, size: 1024

// 缓存设置日志
[INFO] Response Cache: set - cacheKey: GET:/api/users, ttl: 300, size: 1024

// 缓存未命中日志
[DEBUG] Response Cache: miss - cacheKey: GET:/api/users
```

### 性能监控

```typescript
// 获取详细的缓存统计
const stats = await this.cacheService.getStats();
console.log('缓存统计:', {
  totalKeys: stats.totalKeys,      // 总缓存键数
  totalSize: stats.totalSize,      // 总缓存大小（字节）
  avgTtl: stats.avgTtl,           // 平均TTL（秒）
});
```

## 💡 最佳实践

### 1. 缓存策略选择

- **静态数据** - 使用 `@StaticCache(86400)` (24小时)
- **用户配置** - 使用 `@UserCache(1800)` (30分钟)
- **搜索结果** - 使用 `@SearchCache(600)` (10分钟)
- **实时数据** - 使用 `@RealtimeCache(10)` (10秒) 或 `@NoCache()`

### 2. 缓存键设计

```typescript
// 好的做法：包含必要的上下文
@FullCache(300) // 包含用户ID和参数
async getUserPosts(@Query('category') category: string) {
  // 缓存键：GET:/api/posts_user:123_category:tech
}

// 避免：过于宽泛的缓存
@Cache({ includeParams: false })
async getAllPosts() {
  // 所有用户共享同一个缓存，可能不合适
}
```

### 3. 缓存失效策略

```typescript
@Controller('api')
export class PostController {
  // 读操作：使用缓存
  @Get('posts')
  @MediumCache(300)
  async getPosts() {
    return await this.postService.getAllPosts();
  }

  // 写操作：清理相关缓存
  @Post('posts')
  async createPost(@Body() postData: any) {
    const result = await this.postService.createPost(postData);
    
    // 手动清理相关缓存
    await this.cacheService.deleteByPattern('GET:/api/posts*');
    
    return result;
  }
}
```

### 4. 错误处理

```typescript
@Get('safe-cache')
@MediumCache(300)
async getSafeData() {
  try {
    return await this.dataService.getRiskyData();
  } catch (error) {
    // 错误响应不会被缓存
    throw new BusinessException('数据获取失败');
  }
}
```

## 🚨 注意事项

1. **内存使用** - 大量缓存会占用Redis内存，注意监控
2. **数据一致性** - 缓存可能导致数据不一致，合理设置TTL
3. **缓存穿透** - 对于不存在的数据，考虑缓存空结果
4. **热点数据** - 对于热点数据，考虑使用更长的TTL
5. **缓存雪崩** - 避免大量缓存同时过期，使用随机TTL

## 🔗 相关文档

- [响应管理器基础使用](./README.md)
- [日志配置指南](./config/response-logging.config.ts)
- [示例代码](./examples/response-example.controller.ts)
- [缓存管理API](./controllers/cache-management.controller.ts)
