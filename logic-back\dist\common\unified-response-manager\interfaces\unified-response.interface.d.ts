import { HttpResponse } from '../../../web/http_response_result/http-response.interface';
export interface RequestContext {
    requestId: string;
    userId?: string;
    clientIp?: string;
    userAgent?: string;
    path?: string;
    method?: string;
    controller?: string;
    action?: string;
    startTime: number;
    metadata?: Record<string, any>;
}
export interface PerformanceMetrics {
    executionTime: number;
    memoryUsage?: number;
    dbQueryCount?: number;
    cacheHits?: number;
    cacheMisses?: number;
    queueWaitTime?: number;
}
export interface RequestPoolStatus {
    queueSize: number;
    processingCount: number;
    maxConcurrency: number;
    averageWaitTime: number;
    averageProcessingTime: number;
    successRate: number;
}
export interface UnifiedResponse<T = any> {
    context: RequestContext;
    data?: T;
    success: boolean;
    message?: string;
    errors?: string[];
    code: number;
    metrics: PerformanceMetrics;
    timestamp: Date;
    originalType: string;
}
export interface UnifiedResponseConfig {
    enableRequestPool: boolean;
    maxConcurrency: number;
    requestTimeout: number;
    enableMetrics: boolean;
    enableDetailedLogging: boolean;
    redisKeyPrefix: string;
    cleanupInterval: number;
}
export interface RequestTask {
    requestId: string;
    handler: () => Promise<any>;
    priority: number;
    timeout: number;
    createdAt: number;
    startedAt?: number;
    completedAt?: number;
    context: RequestContext;
}
export interface IUnifiedResponseManager {
    handleResponse<T>(result: any, context: Partial<RequestContext>, options?: {
        usePool?: boolean;
        priority?: number;
        timeout?: number;
    }): Promise<HttpResponse<T>>;
    handleError(error: any, context: Partial<RequestContext>): Promise<HttpResponse<never>>;
    createContext(request: any, controller?: string, action?: string): RequestContext;
    getPoolStatus(): Promise<RequestPoolStatus>;
    logResponse<T>(unifiedResponse: UnifiedResponse<T>, level?: 'info' | 'warn' | 'error'): void;
}
export interface IRequestPool {
    enqueue<T>(task: RequestTask): Promise<T>;
    getStatus(): Promise<RequestPoolStatus>;
    cleanup(): Promise<void>;
    stop(): Promise<void>;
}
export interface IPerformanceMonitor {
    startMonitoring(requestId: string): void;
    endMonitoring(requestId: string): PerformanceMetrics;
    recordDbQuery(requestId: string): void;
    recordCacheOperation(requestId: string, hit: boolean): void;
    recordQueueWaitTime(requestId: string, waitTime: number): void;
}
export interface IResponseTypeDetector {
    detectType(result: any): string;
    isHttpResponse(result: any): boolean;
    isError(result: any): boolean;
    extractErrorInfo(error: any): {
        message: string;
        errors: string[];
        code: number;
    };
    normalizeData(result: any): any;
    getSuccessStatus(result: any): boolean;
    getResponseMessage(result: any): string;
}
