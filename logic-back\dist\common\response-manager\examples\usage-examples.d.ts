import { IResponseManager } from '../interfaces/response-manager.interface';
import { CommandResult } from '../../../util/ip_location/application/interfaces/command-handler.interface';
import { QueryResult } from '../../../util/ip_location/application/interfaces/query-handler.interface';
interface CreateUserCommand {
    name: string;
    email: string;
    age: number;
}
interface UpdateUserCommand {
    id: string;
    name?: string;
    email?: string;
    age?: number;
}
interface User {
    id: string;
    name: string;
    email: string;
    age: number;
    createdAt: Date;
    updatedAt: Date;
}
export declare class DddResponseExampleController {
    private readonly responseManager;
    constructor(responseManager: IResponseManager);
    createUser(command: CreateUserCommand): Promise<CommandResult<User>>;
    getUser(id: string): Promise<QueryResult<User>>;
    getUserProfile(id: string): Promise<CommandResult<any>>;
    batchCreateUsers(commands: CreateUserCommand[]): Promise<CommandResult<User[]>>;
    getUserList(): Promise<QueryResult<User[]>>;
    updateUserStatus(id: string, body: {
        status: string;
    }): Promise<CommandResult<{
        id: string;
        status: string;
    }>>;
    exportUsers(): Promise<CommandResult<{
        taskId: string;
        estimatedTime: number;
    }>>;
    manualResponseHandling(id: string, updateCommand: UpdateUserCommand): Promise<any>;
    errorExample(): Promise<CommandResult<never>>;
    getPoolStatus(): Promise<import("../interfaces/response-manager.interface").RequestPoolStatus>;
}
export {};
