{"version": 3, "file": "response.interceptor.js", "sourceRoot": "", "sources": ["../../../../src/common/response/interceptors/response.interceptor.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,2CAKwB;AACxB,uCAAyC;AAEzC,8CAAqC;AAErC,gEAA4D;AAC5D,mFAA8E;AAC9E,yEAAgG;AAQzF,IAAM,mBAAmB,GAAzB,MAAM,mBAAmB;IAEX;IACA;IACA;IAHnB,YACmB,eAAuC,EACvC,SAAoB,EACpB,aAA4B;QAF5B,oBAAe,GAAf,eAAe,CAAwB;QACvC,cAAS,GAAT,SAAS,CAAW;QACpB,kBAAa,GAAb,aAAa,CAAe;IAC5C,CAAC;IAEJ,SAAS,CAAC,OAAyB,EAAE,IAAiB;QACpD,MAAM,OAAO,GAAG,OAAO,CAAC,YAAY,EAAE,CAAC,UAAU,EAAW,CAAC;QAC7D,MAAM,OAAO,GAAG,OAAO,CAAC,UAAU,EAAE,CAAC;QACrC,MAAM,eAAe,GAAG,OAAO,CAAC,QAAQ,EAAE,CAAC;QAC3C,MAAM,UAAU,GAAG,OAAO,CAAC,IAAI,CAAC;QAGhC,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAC/B,wCAAmB,EACnB,OAAO,CACR,IAAI,EAAE,CAAC;QAGR,MAAM,SAAS,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAC3C,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC;YAC9B,SAAS;YACT,SAAS;YACT,IAAI,EAAE,OAAO,CAAC,IAAI;YAClB,MAAM,EAAE,OAAO,CAAC,MAAM;YACtB,EAAE,EAAE,OAAO,CAAC,EAAE;YACd,SAAS,EAAE,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC;YACpC,MAAM,EAAG,OAAe,CAAC,IAAI,EAAE,EAAE;YACjC,MAAM,EAAE,eAAe,CAAC,IAAI;YAC5B,SAAS,EAAE,UAAU;SACtB,CAAC,CAAC;QAGH,IAAI,CAAC,aAAa,CAAC,qBAAqB,CAAC;YACvC,SAAS;YACT,KAAK,EAAE,aAAa;YACpB,MAAM,EAAE,kBAAkB;YAC1B,OAAO,EAAE;gBACP,UAAU,EAAE,eAAe,CAAC,IAAI;gBAChC,MAAM,EAAE,UAAU;gBAClB,IAAI,EAAE,OAAO,CAAC,IAAI;gBAClB,UAAU,EAAE,OAAO,CAAC,MAAM;gBAC1B,SAAS,EAAE,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,MAAM,GAAG,CAAC;gBACzC,YAAY,EAAE,MAAM,CAAC,MAAM;aAC5B;SACF,CAAC,CAAC;QAEH,OAAO,IAAI,CAAC,MAAM,EAAE,CAAC,IAAI,CACvB,IAAA,eAAG,EAAC,CAAC,IAAI,EAAE,EAAE;YACX,MAAM,mBAAmB,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAEvC,IAAI,CAAC;gBACH,IAAI,aAAkB,CAAC;gBAGvB,IAAI,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,EAAE,CAAC;oBAClC,IAAI,CAAC,aAAa,CAAC,qBAAqB,CAAC;wBACvC,SAAS;wBACT,KAAK,EAAE,aAAa;wBACpB,MAAM,EAAE,4BAA4B;wBACpC,OAAO,EAAE,EAAE,YAAY,EAAE,UAAU,EAAE;qBACtC,CAAC,CAAC;oBACH,aAAa,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;gBACnE,CAAC;qBAEI,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,SAAS,EAAE,CAAC;oBAC7C,IAAI,CAAC,aAAa,CAAC,qBAAqB,CAAC;wBACvC,SAAS;wBACT,KAAK,EAAE,aAAa;wBACpB,MAAM,EAAE,oBAAoB;wBAC5B,OAAO,EAAE,EAAE,YAAY,EAAE,MAAM,EAAE;qBAClC,CAAC,CAAC;oBACH,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,OAAO,CAC3C,IAAI,EACJ,MAAM,CAAC,cAAc,EACrB;wBACE,oBAAoB,EAAE,MAAM,CAAC,oBAAoB;wBACjD,gBAAgB,EAAE,MAAM,CAAC,gBAAgB;qBAC1C,CACF,CAAC;oBACF,aAAa,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,QAAQ,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;gBACvE,CAAC;qBAEI,IAAI,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,EAAE,CAAC;oBACpC,IAAI,CAAC,aAAa,CAAC,qBAAqB,CAAC;wBACvC,SAAS;wBACT,KAAK,EAAE,aAAa;wBACpB,MAAM,EAAE,oBAAoB;wBAC5B,OAAO,EAAE;4BACP,YAAY,EAAE,WAAW;4BACzB,SAAS,EAAE,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,IAAI,EAAE,CAAC,CAAC,MAAM;4BAChD,IAAI,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC;4BAC7C,KAAK,EAAE,IAAI,CAAC,UAAU,EAAE,KAAK,IAAI,IAAI,CAAC,KAAK,IAAI,CAAC;yBACjD;qBACF,CAAC,CAAC;oBACH,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,SAAS,CAC7C,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,IAAI,EAAE,EAC5B;wBACE,IAAI,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC;wBAC7C,IAAI,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,IAAI,IAAI,CAAC,IAAI,IAAI,EAAE;wBAC9C,KAAK,EAAE,IAAI,CAAC,UAAU,EAAE,KAAK,IAAI,IAAI,CAAC,KAAK,IAAI,CAAC;qBACjD,EACD,MAAM,CAAC,cAAc,EACrB;wBACE,oBAAoB,EAAE,MAAM,CAAC,oBAAoB;wBACjD,gBAAgB,EAAE,MAAM,CAAC,gBAAgB;qBAC1C,CACF,CAAC;oBACF,aAAa,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,QAAQ,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;gBACvE,CAAC;qBAEI,CAAC;oBACJ,IAAI,CAAC,aAAa,CAAC,qBAAqB,CAAC;wBACvC,SAAS;wBACT,KAAK,EAAE,aAAa;wBACpB,MAAM,EAAE,kBAAkB;wBAC1B,OAAO,EAAE;4BACP,YAAY,EAAE,SAAS;4BACvB,OAAO,EAAE,CAAC,CAAC,IAAI;4BACf,QAAQ,EAAE,OAAO,IAAI;yBACtB;qBACF,CAAC,CAAC;oBACH,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,OAAO,CAC3C,IAAI,EACJ,MAAM,CAAC,cAAc,EACrB;wBACE,oBAAoB,EAAE,MAAM,CAAC,oBAAoB;wBACjD,gBAAgB,EAAE,MAAM,CAAC,gBAAgB;qBAC1C,CACF,CAAC;oBACF,aAAa,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,QAAQ,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;gBACvE,CAAC;gBAGD,IAAI,CAAC,aAAa,CAAC,qBAAqB,CAAC;oBACvC,SAAS;oBACT,KAAK,EAAE,aAAa;oBACpB,MAAM,EAAE,qBAAqB;oBAC7B,QAAQ,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,mBAAmB;oBAC1C,OAAO,EAAE;wBACP,kBAAkB,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;wBAC1C,WAAW,EAAE,MAAM,CAAC,MAAM,IAAI,UAAU;qBACzC;iBACF,CAAC,CAAC;gBAEH,OAAO,aAAa,CAAC;YAEvB,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBAEf,IAAI,CAAC,aAAa,CAAC,qBAAqB,CAAC;oBACvC,SAAS;oBACT,KAAK,EAAE,aAAa;oBACpB,MAAM,EAAE,kBAAkB;oBAC1B,QAAQ,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,mBAAmB;oBAC1C,KAAK;iBACN,CAAC,CAAC;gBACH,MAAM,KAAK,CAAC;YACd,CAAC;QACH,CAAC,CAAC,CACH,CAAC;IACJ,CAAC;IAKO,kBAAkB,CAAC,IAAS;QAClC,OAAO,CACL,IAAI;YACJ,OAAO,IAAI,KAAK,QAAQ;YACxB,MAAM,IAAI,IAAI;YACd,SAAS,IAAI,IAAI;YACjB,WAAW,IAAI,IAAI,CACpB,CAAC;IACJ,CAAC;IAKO,eAAe,CAAC,IAAS;QAC/B,OAAO,CACL,IAAI;YACJ,OAAO,IAAI,KAAK,QAAQ;YACxB,CACE,CAAC,MAAM,IAAI,IAAI,IAAI,YAAY,IAAI,IAAI,CAAC;gBACxC,CAAC,MAAM,IAAI,IAAI,IAAI,YAAY,IAAI,IAAI,CAAC;gBACxC,CAAC,MAAM,IAAI,IAAI,IAAI,MAAM,IAAI,IAAI,IAAI,OAAO,IAAI,IAAI,CAAC;gBACrD,CAAC,MAAM,IAAI,IAAI,IAAI,MAAM,IAAI,IAAI,IAAI,OAAO,IAAI,IAAI,CAAC,CACtD,CACF,CAAC;IACJ,CAAC;IAKO,iBAAiB;QACvB,OAAO,OAAO,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;IACxE,CAAC;CACF,CAAA;AAxMY,kDAAmB;8BAAnB,mBAAmB;IAD/B,IAAA,mBAAU,GAAE;qCAGyB,iDAAsB;QAC5B,gBAAS;QACL,8BAAa;GAJpC,mBAAmB,CAwM/B;AAOM,IAAM,yBAAyB,GAA/B,MAAM,yBAAyB;IAEjB;IACA;IAFnB,YACmB,eAAuC,EACvC,aAA4B;QAD5B,oBAAe,GAAf,eAAe,CAAwB;QACvC,kBAAa,GAAb,aAAa,CAAe;IAC5C,CAAC;IAEJ,SAAS,CAAC,OAAyB,EAAE,IAAiB;QACpD,MAAM,OAAO,GAAG,OAAO,CAAC,YAAY,EAAE,CAAC,UAAU,EAAW,CAAC;QAG7D,MAAM,YAAY,GAAG;YACnB,SAAS;YACT,UAAU;YACV,iBAAiB;YACjB,WAAW;YACX,UAAU;SACX,CAAC;QAEF,IAAI,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC;YAC3D,OAAO,IAAI,CAAC,MAAM,EAAE,CAAC;QACvB,CAAC;QAGD,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC;YAC1B,OAAO,CAAC,KAAK,CAAC,8DAA8D,CAAC,CAAC;YAC9E,OAAO,IAAI,CAAC,MAAM,EAAE,CAAC;QACvB,CAAC;QAGD,MAAM,SAAS,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAC3C,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,IAAI,CAAC;YACH,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC;gBAC9B,SAAS;gBACT,SAAS;gBACT,IAAI,EAAE,OAAO,CAAC,IAAI;gBAClB,MAAM,EAAE,OAAO,CAAC,MAAM;gBACtB,EAAE,EAAE,OAAO,CAAC,EAAE;gBACd,SAAS,EAAE,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC;gBACpC,MAAM,EAAG,OAAe,CAAC,IAAI,EAAE,EAAE;aAClC,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,qDAAqD,EAAE,KAAK,CAAC,CAAC;YAC5E,OAAO,IAAI,CAAC,MAAM,EAAE,CAAC;QACvB,CAAC;QAGD,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YACvB,IAAI,CAAC;gBACH,IAAI,CAAC,aAAa,CAAC,qBAAqB,CAAC;oBACvC,SAAS;oBACT,KAAK,EAAE,aAAa;oBACpB,MAAM,EAAE,yBAAyB;oBACjC,OAAO,EAAE;wBACP,IAAI,EAAE,OAAO,CAAC,IAAI;wBAClB,MAAM,EAAE,OAAO,CAAC,MAAM;wBACtB,SAAS,EAAE,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC;wBACpC,EAAE,EAAE,OAAO,CAAC,EAAE;qBACf;iBACF,CAAC,CAAC;YACL,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,6CAA6C,EAAE,KAAK,CAAC,CAAC;YACtE,CAAC;QACH,CAAC;QAED,OAAO,IAAI,CAAC,MAAM,EAAE,CAAC,IAAI,CACvB,IAAA,eAAG,EAAC,CAAC,IAAI,EAAE,EAAE;YACX,MAAM,mBAAmB,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAEvC,IAAI,CAAC;gBACH,IAAI,aAAkB,CAAC;gBAGvB,IAAI,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,EAAE,CAAC;oBAClC,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;wBACvB,IAAI,CAAC;4BACH,IAAI,CAAC,aAAa,CAAC,qBAAqB,CAAC;gCACvC,SAAS;gCACT,KAAK,EAAE,aAAa;gCACpB,MAAM,EAAE,0BAA0B;gCAClC,OAAO,EAAE,EAAE,YAAY,EAAE,kBAAkB,EAAE;6BAC9C,CAAC,CAAC;wBACL,CAAC;wBAAC,OAAO,KAAK,EAAE,CAAC;4BACf,OAAO,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;wBAC3D,CAAC;oBACH,CAAC;oBACD,aAAa,GAAG,IAAI,CAAC;gBACvB,CAAC;qBAAM,CAAC;oBAEN,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;wBACvB,IAAI,CAAC;4BACH,IAAI,CAAC,aAAa,CAAC,qBAAqB,CAAC;gCACvC,SAAS;gCACT,KAAK,EAAE,aAAa;gCACpB,MAAM,EAAE,2BAA2B;gCACnC,OAAO,EAAE;oCACP,YAAY,EAAE,uBAAuB;oCACrC,YAAY,EAAE,OAAO,IAAI;iCAC1B;6BACF,CAAC,CAAC;wBACL,CAAC;wBAAC,OAAO,KAAK,EAAE,CAAC;4BACf,OAAO,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC;wBAC5D,CAAC;oBACH,CAAC;oBACD,aAAa,GAAG,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;gBACnF,CAAC;gBAGD,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;oBACvB,IAAI,CAAC;wBACH,IAAI,CAAC,aAAa,CAAC,qBAAqB,CAAC;4BACvC,SAAS;4BACT,KAAK,EAAE,aAAa;4BACpB,MAAM,EAAE,4BAA4B;4BACpC,QAAQ,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,mBAAmB;4BAC1C,OAAO,EAAE;gCACP,kBAAkB,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;6BAC3C;yBACF,CAAC,CAAC;oBACL,CAAC;oBAAC,OAAO,KAAK,EAAE,CAAC;wBACf,OAAO,CAAC,KAAK,CAAC,oCAAoC,EAAE,KAAK,CAAC,CAAC;oBAC7D,CAAC;gBACH,CAAC;gBAED,OAAO,aAAa,CAAC;YAEvB,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBAEf,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;oBACvB,IAAI,CAAC;wBACH,IAAI,CAAC,aAAa,CAAC,qBAAqB,CAAC;4BACvC,SAAS;4BACT,KAAK,EAAE,aAAa;4BACpB,MAAM,EAAE,yBAAyB;4BACjC,QAAQ,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,mBAAmB;4BAC1C,KAAK;yBACN,CAAC,CAAC;oBACL,CAAC;oBAAC,OAAO,QAAQ,EAAE,CAAC;wBAClB,OAAO,CAAC,KAAK,CAAC,iCAAiC,EAAE,QAAQ,CAAC,CAAC;oBAC7D,CAAC;gBACH,CAAC;gBACD,MAAM,KAAK,CAAC;YACd,CAAC;QACH,CAAC,CAAC,CACH,CAAC;IACJ,CAAC;IAKO,kBAAkB,CAAC,IAAS;QAClC,OAAO,CACL,IAAI;YACJ,OAAO,IAAI,KAAK,QAAQ;YACxB,MAAM,IAAI,IAAI;YACd,SAAS,IAAI,IAAI;YACjB,WAAW,IAAI,IAAI,CACpB,CAAC;IACJ,CAAC;IAKO,iBAAiB;QACvB,OAAO,OAAO,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;IACxE,CAAC;CACF,CAAA;AAvKY,8DAAyB;oCAAzB,yBAAyB;IADrC,IAAA,mBAAU,GAAE;qCAGyB,iDAAsB;QACxB,8BAAa;GAHpC,yBAAyB,CAuKrC"}