{"version": 3, "file": "response.interceptor.js", "sourceRoot": "", "sources": ["../../../../src/common/response/interceptors/response.interceptor.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,2CAKwB;AACxB,uCAAyC;AAEzC,8CAAqC;AAErC,mFAA8E;AAC9E,yEAAgG;AAQzF,IAAM,mBAAmB,GAAzB,MAAM,mBAAmB;IAEX;IACA;IAFnB,YACmB,eAAuC,EACvC,SAAoB;QADpB,oBAAe,GAAf,eAAe,CAAwB;QACvC,cAAS,GAAT,SAAS,CAAW;IACpC,CAAC;IAEJ,SAAS,CAAC,OAAyB,EAAE,IAAiB;QACpD,MAAM,OAAO,GAAG,OAAO,CAAC,YAAY,EAAE,CAAC,UAAU,EAAW,CAAC;QAC7D,MAAM,OAAO,GAAG,OAAO,CAAC,UAAU,EAAE,CAAC;QAGrC,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAC/B,wCAAmB,EACnB,OAAO,CACR,IAAI,EAAE,CAAC;QAGR,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC;YAC9B,SAAS,EAAE,IAAI,CAAC,iBAAiB,EAAE;YACnC,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;YACrB,IAAI,EAAE,OAAO,CAAC,IAAI;YAClB,MAAM,EAAE,OAAO,CAAC,MAAM;YACtB,EAAE,EAAE,OAAO,CAAC,EAAE;YACd,SAAS,EAAE,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC;YACpC,MAAM,EAAG,OAAe,CAAC,IAAI,EAAE,EAAE;SAClC,CAAC,CAAC;QAEH,OAAO,IAAI,CAAC,MAAM,EAAE,CAAC,IAAI,CACvB,IAAA,eAAG,EAAC,CAAC,IAAI,EAAE,EAAE;YAEX,IAAI,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,EAAE,CAAC;gBAClC,OAAO,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;YAC1D,CAAC;YAGD,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,SAAS,EAAE,CAAC;gBACxC,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,OAAO,CAC3C,IAAI,EACJ,MAAM,CAAC,cAAc,EACrB;oBACE,oBAAoB,EAAE,MAAM,CAAC,oBAAoB;oBACjD,gBAAgB,EAAE,MAAM,CAAC,gBAAgB;iBAC1C,CACF,CAAC;gBACF,OAAO,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,QAAQ,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;YAC9D,CAAC;YAGD,IAAI,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,EAAE,CAAC;gBAC/B,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,SAAS,CAC7C,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,IAAI,EAAE,EAC5B;oBACE,IAAI,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC;oBAC7C,IAAI,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,IAAI,IAAI,CAAC,IAAI,IAAI,EAAE;oBAC9C,KAAK,EAAE,IAAI,CAAC,UAAU,EAAE,KAAK,IAAI,IAAI,CAAC,KAAK,IAAI,CAAC;iBACjD,EACD,MAAM,CAAC,cAAc,EACrB;oBACE,oBAAoB,EAAE,MAAM,CAAC,oBAAoB;oBACjD,gBAAgB,EAAE,MAAM,CAAC,gBAAgB;iBAC1C,CACF,CAAC;gBACF,OAAO,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,QAAQ,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;YAC9D,CAAC;YAGD,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,OAAO,CAC3C,IAAI,EACJ,MAAM,CAAC,cAAc,EACrB;gBACE,oBAAoB,EAAE,MAAM,CAAC,oBAAoB;gBACjD,gBAAgB,EAAE,MAAM,CAAC,gBAAgB;aAC1C,CACF,CAAC;YACF,OAAO,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,QAAQ,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;QAC9D,CAAC,CAAC,CACH,CAAC;IACJ,CAAC;IAKO,kBAAkB,CAAC,IAAS;QAClC,OAAO,CACL,IAAI;YACJ,OAAO,IAAI,KAAK,QAAQ;YACxB,MAAM,IAAI,IAAI;YACd,SAAS,IAAI,IAAI;YACjB,WAAW,IAAI,IAAI,CACpB,CAAC;IACJ,CAAC;IAKO,eAAe,CAAC,IAAS;QAC/B,OAAO,CACL,IAAI;YACJ,OAAO,IAAI,KAAK,QAAQ;YACxB,CACE,CAAC,MAAM,IAAI,IAAI,IAAI,YAAY,IAAI,IAAI,CAAC;gBACxC,CAAC,MAAM,IAAI,IAAI,IAAI,YAAY,IAAI,IAAI,CAAC;gBACxC,CAAC,MAAM,IAAI,IAAI,IAAI,MAAM,IAAI,IAAI,IAAI,OAAO,IAAI,IAAI,CAAC;gBACrD,CAAC,MAAM,IAAI,IAAI,IAAI,MAAM,IAAI,IAAI,IAAI,OAAO,IAAI,IAAI,CAAC,CACtD,CACF,CAAC;IACJ,CAAC;IAKO,iBAAiB;QACvB,OAAO,OAAO,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;IACxE,CAAC;CACF,CAAA;AAlHY,kDAAmB;8BAAnB,mBAAmB;IAD/B,IAAA,mBAAU,GAAE;qCAGyB,iDAAsB;QAC5B,gBAAS;GAH5B,mBAAmB,CAkH/B;AAOM,IAAM,yBAAyB,GAA/B,MAAM,yBAAyB;IACP;IAA7B,YAA6B,eAAuC;QAAvC,oBAAe,GAAf,eAAe,CAAwB;IAAG,CAAC;IAExE,SAAS,CAAC,OAAyB,EAAE,IAAiB;QACpD,MAAM,OAAO,GAAG,OAAO,CAAC,YAAY,EAAE,CAAC,UAAU,EAAW,CAAC;QAG7D,MAAM,YAAY,GAAG;YACnB,SAAS;YACT,UAAU;YACV,iBAAiB;YACjB,WAAW;YACX,UAAU;SACX,CAAC;QAEF,IAAI,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC;YAC3D,OAAO,IAAI,CAAC,MAAM,EAAE,CAAC;QACvB,CAAC;QAGD,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC;YAC9B,SAAS,EAAE,IAAI,CAAC,iBAAiB,EAAE;YACnC,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;YACrB,IAAI,EAAE,OAAO,CAAC,IAAI;YAClB,MAAM,EAAE,OAAO,CAAC,MAAM;YACtB,EAAE,EAAE,OAAO,CAAC,EAAE;YACd,SAAS,EAAE,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC;YACpC,MAAM,EAAG,OAAe,CAAC,IAAI,EAAE,EAAE;SAClC,CAAC,CAAC;QAEH,OAAO,IAAI,CAAC,MAAM,EAAE,CAAC,IAAI,CACvB,IAAA,eAAG,EAAC,CAAC,IAAI,EAAE,EAAE;YAEX,IAAI,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,EAAE,CAAC;gBAClC,OAAO,IAAI,CAAC;YACd,CAAC;YAGD,OAAO,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAC5C,CAAC,CAAC,CACH,CAAC;IACJ,CAAC;IAKO,kBAAkB,CAAC,IAAS;QAClC,OAAO,CACL,IAAI;YACJ,OAAO,IAAI,KAAK,QAAQ;YACxB,MAAM,IAAI,IAAI;YACd,SAAS,IAAI,IAAI;YACjB,WAAW,IAAI,IAAI,CACpB,CAAC;IACJ,CAAC;IAKO,iBAAiB;QACvB,OAAO,OAAO,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;IACxE,CAAC;CACF,CAAA;AA9DY,8DAAyB;oCAAzB,yBAAyB;IADrC,IAAA,mBAAU,GAAE;qCAEmC,iDAAsB;GADzD,yBAAyB,CA8DrC"}