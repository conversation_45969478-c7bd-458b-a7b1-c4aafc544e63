{"version": 3, "file": "response.interceptor.js", "sourceRoot": "", "sources": ["../../../../src/common/response/interceptors/response.interceptor.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,2CAKwB;AACxB,uCAAyC;AAEzC,8CAAqC;AAErC,gEAA4D;AAC5D,mFAA8E;AAC9E,yEAAgG;AAQzF,IAAM,mBAAmB,GAAzB,MAAM,mBAAmB;IAEX;IACA;IACA;IAHnB,YACmB,eAAuC,EACvC,SAAoB,EACpB,aAA4B;QAF5B,oBAAe,GAAf,eAAe,CAAwB;QACvC,cAAS,GAAT,SAAS,CAAW;QACpB,kBAAa,GAAb,aAAa,CAAe;IAC5C,CAAC;IAEJ,SAAS,CAAC,OAAyB,EAAE,IAAiB;QACpD,MAAM,OAAO,GAAG,OAAO,CAAC,YAAY,EAAE,CAAC,UAAU,EAAW,CAAC;QAC7D,MAAM,OAAO,GAAG,OAAO,CAAC,UAAU,EAAE,CAAC;QACrC,MAAM,eAAe,GAAG,OAAO,CAAC,QAAQ,EAAE,CAAC;QAC3C,MAAM,UAAU,GAAG,OAAO,CAAC,IAAI,CAAC;QAGhC,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAC/B,wCAAmB,EACnB,OAAO,CACR,IAAI,EAAE,CAAC;QAGR,MAAM,SAAS,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAC3C,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC;YAC9B,SAAS;YACT,SAAS;YACT,IAAI,EAAE,OAAO,CAAC,IAAI;YAClB,MAAM,EAAE,OAAO,CAAC,MAAM;YACtB,EAAE,EAAE,OAAO,CAAC,EAAE;YACd,SAAS,EAAE,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC;YACpC,MAAM,EAAG,OAAe,CAAC,IAAI,EAAE,EAAE;YACjC,MAAM,EAAE,eAAe,CAAC,IAAI;YAC5B,SAAS,EAAE,UAAU;SACtB,CAAC,CAAC;QAGH,IAAI,CAAC,aAAa,CAAC,qBAAqB,CAAC;YACvC,SAAS;YACT,KAAK,EAAE,aAAa;YACpB,MAAM,EAAE,kBAAkB;YAC1B,OAAO,EAAE;gBACP,UAAU,EAAE,eAAe,CAAC,IAAI;gBAChC,MAAM,EAAE,UAAU;gBAClB,IAAI,EAAE,OAAO,CAAC,IAAI;gBAClB,UAAU,EAAE,OAAO,CAAC,MAAM;gBAC1B,SAAS,EAAE,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,MAAM,GAAG,CAAC;gBACzC,YAAY,EAAE,MAAM,CAAC,MAAM;aAC5B;SACF,CAAC,CAAC;QAEH,OAAO,IAAI,CAAC,MAAM,EAAE,CAAC,IAAI,CACvB,IAAA,eAAG,EAAC,CAAC,IAAI,EAAE,EAAE;YACX,MAAM,mBAAmB,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAEvC,IAAI,CAAC;gBACH,IAAI,aAAkB,CAAC;gBAGvB,IAAI,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,EAAE,CAAC;oBAClC,IAAI,CAAC,aAAa,CAAC,qBAAqB,CAAC;wBACvC,SAAS;wBACT,KAAK,EAAE,aAAa;wBACpB,MAAM,EAAE,4BAA4B;wBACpC,OAAO,EAAE,EAAE,YAAY,EAAE,UAAU,EAAE;qBACtC,CAAC,CAAC;oBACH,aAAa,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;gBACnE,CAAC;qBAEI,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,SAAS,EAAE,CAAC;oBAC7C,IAAI,CAAC,aAAa,CAAC,qBAAqB,CAAC;wBACvC,SAAS;wBACT,KAAK,EAAE,aAAa;wBACpB,MAAM,EAAE,oBAAoB;wBAC5B,OAAO,EAAE,EAAE,YAAY,EAAE,MAAM,EAAE;qBAClC,CAAC,CAAC;oBACH,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,OAAO,CAC3C,IAAI,EACJ,MAAM,CAAC,cAAc,EACrB;wBACE,oBAAoB,EAAE,MAAM,CAAC,oBAAoB;wBACjD,gBAAgB,EAAE,MAAM,CAAC,gBAAgB;qBAC1C,CACF,CAAC;oBACF,aAAa,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,QAAQ,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;gBACvE,CAAC;qBAEI,IAAI,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,EAAE,CAAC;oBACpC,IAAI,CAAC,aAAa,CAAC,qBAAqB,CAAC;wBACvC,SAAS;wBACT,KAAK,EAAE,aAAa;wBACpB,MAAM,EAAE,oBAAoB;wBAC5B,OAAO,EAAE;4BACP,YAAY,EAAE,WAAW;4BACzB,SAAS,EAAE,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,IAAI,EAAE,CAAC,CAAC,MAAM;4BAChD,IAAI,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC;4BAC7C,KAAK,EAAE,IAAI,CAAC,UAAU,EAAE,KAAK,IAAI,IAAI,CAAC,KAAK,IAAI,CAAC;yBACjD;qBACF,CAAC,CAAC;oBACH,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,SAAS,CAC7C,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,IAAI,EAAE,EAC5B;wBACE,IAAI,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC;wBAC7C,IAAI,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,IAAI,IAAI,CAAC,IAAI,IAAI,EAAE;wBAC9C,KAAK,EAAE,IAAI,CAAC,UAAU,EAAE,KAAK,IAAI,IAAI,CAAC,KAAK,IAAI,CAAC;qBACjD,EACD,MAAM,CAAC,cAAc,EACrB;wBACE,oBAAoB,EAAE,MAAM,CAAC,oBAAoB;wBACjD,gBAAgB,EAAE,MAAM,CAAC,gBAAgB;qBAC1C,CACF,CAAC;oBACF,aAAa,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,QAAQ,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;gBACvE,CAAC;qBAEI,CAAC;oBACJ,IAAI,CAAC,aAAa,CAAC,qBAAqB,CAAC;wBACvC,SAAS;wBACT,KAAK,EAAE,aAAa;wBACpB,MAAM,EAAE,kBAAkB;wBAC1B,OAAO,EAAE;4BACP,YAAY,EAAE,SAAS;4BACvB,OAAO,EAAE,CAAC,CAAC,IAAI;4BACf,QAAQ,EAAE,OAAO,IAAI;yBACtB;qBACF,CAAC,CAAC;oBACH,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,OAAO,CAC3C,IAAI,EACJ,MAAM,CAAC,cAAc,EACrB;wBACE,oBAAoB,EAAE,MAAM,CAAC,oBAAoB;wBACjD,gBAAgB,EAAE,MAAM,CAAC,gBAAgB;qBAC1C,CACF,CAAC;oBACF,aAAa,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,QAAQ,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;gBACvE,CAAC;gBAGD,IAAI,CAAC,aAAa,CAAC,qBAAqB,CAAC;oBACvC,SAAS;oBACT,KAAK,EAAE,aAAa;oBACpB,MAAM,EAAE,qBAAqB;oBAC7B,QAAQ,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,mBAAmB;oBAC1C,OAAO,EAAE;wBACP,kBAAkB,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;wBAC1C,WAAW,EAAE,MAAM,CAAC,MAAM,IAAI,UAAU;qBACzC;iBACF,CAAC,CAAC;gBAEH,OAAO,aAAa,CAAC;YAEvB,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBAEf,IAAI,CAAC,aAAa,CAAC,qBAAqB,CAAC;oBACvC,SAAS;oBACT,KAAK,EAAE,aAAa;oBACpB,MAAM,EAAE,kBAAkB;oBAC1B,QAAQ,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,mBAAmB;oBAC1C,KAAK;iBACN,CAAC,CAAC;gBACH,MAAM,KAAK,CAAC;YACd,CAAC;QACH,CAAC,CAAC,CACH,CAAC;IACJ,CAAC;IAKO,kBAAkB,CAAC,IAAS;QAClC,OAAO,CACL,IAAI;YACJ,OAAO,IAAI,KAAK,QAAQ;YACxB,MAAM,IAAI,IAAI;YACd,SAAS,IAAI,IAAI;YACjB,WAAW,IAAI,IAAI,CACpB,CAAC;IACJ,CAAC;IAKO,eAAe,CAAC,IAAS;QAC/B,OAAO,CACL,IAAI;YACJ,OAAO,IAAI,KAAK,QAAQ;YACxB,CACE,CAAC,MAAM,IAAI,IAAI,IAAI,YAAY,IAAI,IAAI,CAAC;gBACxC,CAAC,MAAM,IAAI,IAAI,IAAI,YAAY,IAAI,IAAI,CAAC;gBACxC,CAAC,MAAM,IAAI,IAAI,IAAI,MAAM,IAAI,IAAI,IAAI,OAAO,IAAI,IAAI,CAAC;gBACrD,CAAC,MAAM,IAAI,IAAI,IAAI,MAAM,IAAI,IAAI,IAAI,OAAO,IAAI,IAAI,CAAC,CACtD,CACF,CAAC;IACJ,CAAC;IAKO,iBAAiB;QACvB,OAAO,OAAO,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;IACxE,CAAC;CACF,CAAA;AAxMY,kDAAmB;8BAAnB,mBAAmB;IAD/B,IAAA,mBAAU,GAAE;qCAGyB,iDAAsB;QAC5B,gBAAS;QACL,8BAAa;GAJpC,mBAAmB,CAwM/B;AAOM,IAAM,yBAAyB,GAA/B,MAAM,yBAAyB;IAEjB;IACA;IAFnB,YACmB,eAAuC,EACvC,aAA4B;QAD5B,oBAAe,GAAf,eAAe,CAAwB;QACvC,kBAAa,GAAb,aAAa,CAAe;IAC5C,CAAC;IAEJ,SAAS,CAAC,OAAyB,EAAE,IAAiB;QACpD,MAAM,OAAO,GAAG,OAAO,CAAC,YAAY,EAAE,CAAC,UAAU,EAAW,CAAC;QAG7D,MAAM,YAAY,GAAG;YACnB,SAAS;YACT,UAAU;YACV,iBAAiB;YACjB,WAAW;YACX,UAAU;SACX,CAAC;QAEF,IAAI,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC;YAC3D,OAAO,IAAI,CAAC,MAAM,EAAE,CAAC;QACvB,CAAC;QAGD,MAAM,SAAS,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAC3C,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC;YAC9B,SAAS;YACT,SAAS;YACT,IAAI,EAAE,OAAO,CAAC,IAAI;YAClB,MAAM,EAAE,OAAO,CAAC,MAAM;YACtB,EAAE,EAAE,OAAO,CAAC,EAAE;YACd,SAAS,EAAE,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC;YACpC,MAAM,EAAG,OAAe,CAAC,IAAI,EAAE,EAAE;SAClC,CAAC,CAAC;QAGH,IAAI,CAAC,aAAa,CAAC,qBAAqB,CAAC;YACvC,SAAS;YACT,KAAK,EAAE,aAAa;YACpB,MAAM,EAAE,yBAAyB;YACjC,OAAO,EAAE;gBACP,IAAI,EAAE,OAAO,CAAC,IAAI;gBAClB,MAAM,EAAE,OAAO,CAAC,MAAM;gBACtB,SAAS,EAAE,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC;gBACpC,EAAE,EAAE,OAAO,CAAC,EAAE;aACf;SACF,CAAC,CAAC;QAEH,OAAO,IAAI,CAAC,MAAM,EAAE,CAAC,IAAI,CACvB,IAAA,eAAG,EAAC,CAAC,IAAI,EAAE,EAAE;YACX,MAAM,mBAAmB,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAEvC,IAAI,CAAC;gBACH,IAAI,aAAkB,CAAC;gBAGvB,IAAI,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,EAAE,CAAC;oBAClC,IAAI,CAAC,aAAa,CAAC,qBAAqB,CAAC;wBACvC,SAAS;wBACT,KAAK,EAAE,aAAa;wBACpB,MAAM,EAAE,0BAA0B;wBAClC,OAAO,EAAE,EAAE,YAAY,EAAE,kBAAkB,EAAE;qBAC9C,CAAC,CAAC;oBACH,aAAa,GAAG,IAAI,CAAC;gBACvB,CAAC;qBAAM,CAAC;oBAEN,IAAI,CAAC,aAAa,CAAC,qBAAqB,CAAC;wBACvC,SAAS;wBACT,KAAK,EAAE,aAAa;wBACpB,MAAM,EAAE,2BAA2B;wBACnC,OAAO,EAAE;4BACP,YAAY,EAAE,uBAAuB;4BACrC,YAAY,EAAE,OAAO,IAAI;yBAC1B;qBACF,CAAC,CAAC;oBACH,aAAa,GAAG,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;gBACrD,CAAC;gBAGD,IAAI,CAAC,aAAa,CAAC,qBAAqB,CAAC;oBACvC,SAAS;oBACT,KAAK,EAAE,aAAa;oBACpB,MAAM,EAAE,4BAA4B;oBACpC,QAAQ,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,mBAAmB;oBAC1C,OAAO,EAAE;wBACP,kBAAkB,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;qBAC3C;iBACF,CAAC,CAAC;gBAEH,OAAO,aAAa,CAAC;YAEvB,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBAEf,IAAI,CAAC,aAAa,CAAC,qBAAqB,CAAC;oBACvC,SAAS;oBACT,KAAK,EAAE,aAAa;oBACpB,MAAM,EAAE,yBAAyB;oBACjC,QAAQ,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,mBAAmB;oBAC1C,KAAK;iBACN,CAAC,CAAC;gBACH,MAAM,KAAK,CAAC;YACd,CAAC;QACH,CAAC,CAAC,CACH,CAAC;IACJ,CAAC;IAKO,kBAAkB,CAAC,IAAS;QAClC,OAAO,CACL,IAAI;YACJ,OAAO,IAAI,KAAK,QAAQ;YACxB,MAAM,IAAI,IAAI;YACd,SAAS,IAAI,IAAI;YACjB,WAAW,IAAI,IAAI,CACpB,CAAC;IACJ,CAAC;IAKO,iBAAiB;QACvB,OAAO,OAAO,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;IACxE,CAAC;CACF,CAAA;AA9HY,8DAAyB;oCAAzB,yBAAyB;IADrC,IAAA,mBAAU,GAAE;qCAGyB,iDAAsB;QACxB,8BAAa;GAHpC,yBAAyB,CA8HrC"}