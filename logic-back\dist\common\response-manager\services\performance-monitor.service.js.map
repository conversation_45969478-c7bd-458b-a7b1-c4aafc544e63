{"version": 3, "file": "performance-monitor.service.js", "sourceRoot": "", "sources": ["../../../../src/common/response-manager/services/performance-monitor.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAAoD;AACpD,yBAAyB;AACzB,mCAAmC;AAOnC,gEAA4D;AAqBrD,IAAM,yBAAyB,GAA/B,MAAM,yBAAyB;IAKjB;IACmC;IALrC,cAAc,GAAG,IAAI,GAAG,EAA0B,CAAC;IACnD,cAAc,GAAG,KAAK,CAAC;IAExC,YACmB,MAAqB,EACc,MAA6B;QADhE,WAAM,GAAN,MAAM,CAAe;QACc,WAAM,GAAN,MAAM,CAAuB;QAGjF,IAAI,CAAC,iBAAiB,EAAE,CAAC;IAC3B,CAAC;IAKD,eAAe,CAAC,SAAiB;QAC/B,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE,CAAC;YAC/B,OAAO;QACT,CAAC;QAED,MAAM,cAAc,GAAmB;YACrC,SAAS;YACT,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;YACrB,WAAW,EAAE,OAAO,CAAC,WAAW,EAAE;YAClC,aAAa,EAAE,OAAO,CAAC,QAAQ,EAAE;YACjC,YAAY,EAAE,CAAC;YACf,SAAS,EAAE,CAAC;YACZ,WAAW,EAAE,CAAC;YACd,gBAAgB,EAAE,CAAC;SACpB,CAAC;QAEF,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,SAAS,EAAE,cAAc,CAAC,CAAC;QAEnD,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,WAAW,SAAS,EAAE,EAAE,oBAAoB,CAAC,CAAC;IAClE,CAAC;IAKD,aAAa,CAAC,SAAiB;QAC7B,MAAM,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QAEhD,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE,CAAC;YACxC,OAAO,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAClC,CAAC;QAED,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC3B,MAAM,SAAS,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;QACxC,MAAM,WAAW,GAAG,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QAEzD,MAAM,OAAO,GAAuB;YAClC,aAAa,EAAE,OAAO,GAAG,IAAI,CAAC,SAAS;YACvC,WAAW,EAAE,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,WAAW,EAAE,SAAS,CAAC;YACnE,QAAQ,EAAE,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC;YAC7C,YAAY,EAAE,IAAI,CAAC,YAAY;YAC/B,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,gBAAgB,EAAE,IAAI,CAAC,gBAAgB;SACxC,CAAC;QAGF,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;QAGtC,IAAI,CAAC,qBAAqB,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;QAE/C,OAAO,OAAO,CAAC;IACjB,CAAC;IAKD,aAAa,CAAC,SAAiB;QAC7B,MAAM,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QAChD,IAAI,IAAI,EAAE,CAAC;YACT,IAAI,CAAC,YAAY,EAAE,CAAC;QACtB,CAAC;IACH,CAAC;IAKD,oBAAoB,CAAC,SAAiB,EAAE,GAAY;QAClD,MAAM,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QAChD,IAAI,IAAI,EAAE,CAAC;YACT,IAAI,GAAG,EAAE,CAAC;gBACR,IAAI,CAAC,SAAS,EAAE,CAAC;YACnB,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,WAAW,EAAE,CAAC;YACrB,CAAC;QACH,CAAC;IACH,CAAC;IAKD,qBAAqB,CAAC,SAAiB;QACrC,MAAM,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QAChD,IAAI,IAAI,EAAE,CAAC;YACT,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAC1B,CAAC;IACH,CAAC;IAKD,gBAAgB;QAQd,MAAM,WAAW,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC;QAClC,MAAM,UAAU,GAAG,EAAE,CAAC,OAAO,EAAE,CAAC;QAChC,MAAM,UAAU,GAAG,WAAW,GAAG,UAAU,CAAC;QAE5C,OAAO;YACL,QAAQ,EAAE,IAAI,CAAC,kBAAkB,EAAE;YACnC,WAAW,EAAE,CAAC,UAAU,GAAG,WAAW,CAAC,GAAG,GAAG;YAC7C,UAAU;YACV,WAAW;YACX,WAAW,EAAE,EAAE,CAAC,OAAO,EAAE;YACzB,MAAM,EAAE,EAAE,CAAC,MAAM,EAAE;SACpB,CAAC;IACJ,CAAC;IAKD,wBAAwB;QACtB,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC;IAClC,CAAC;IAKO,oBAAoB,CAC1B,WAA+B,EAC/B,SAA6B;QAG7B,MAAM,YAAY,GAAG,SAAS,CAAC,QAAQ,GAAG,WAAW,CAAC,QAAQ,CAAC;QAC/D,MAAM,YAAY,GAAG,SAAS,CAAC,QAAQ,GAAG,WAAW,CAAC,QAAQ,CAAC;QAE/D,OAAO,YAAY,GAAG,YAAY,CAAC;IACrC,CAAC;IAKO,iBAAiB,CAAC,QAAyB;QAEjD,MAAM,SAAS,GAAG,QAAQ,CAAC,IAAI,GAAG,QAAQ,CAAC,MAAM,CAAC;QAClD,OAAO,SAAS,GAAG,IAAI,CAAC;IAC1B,CAAC;IAKO,kBAAkB;QACxB,MAAM,IAAI,GAAG,EAAE,CAAC,IAAI,EAAE,CAAC;QACvB,IAAI,SAAS,GAAG,CAAC,CAAC;QAClB,IAAI,SAAS,GAAG,CAAC,CAAC;QAElB,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;YACjB,KAAK,MAAM,IAAI,IAAI,GAAG,CAAC,KAAK,EAAE,CAAC;gBAC7B,SAAS,IAAI,GAAG,CAAC,KAAK,CAAC,IAA8B,CAAC,CAAC;YACzD,CAAC;YACD,SAAS,IAAI,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC;QAC9B,CAAC,CAAC,CAAC;QAEH,OAAO,GAAG,GAAG,CAAC,SAAS,GAAG,SAAS,CAAC,GAAG,GAAG,CAAC;IAC7C,CAAC;IAKO,iBAAiB;QACvB,OAAO;YACL,aAAa,EAAE,CAAC;YAChB,WAAW,EAAE,CAAC;YACd,QAAQ,EAAE,CAAC;YACX,YAAY,EAAE,CAAC;YACf,SAAS,EAAE,CAAC;YACZ,WAAW,EAAE,CAAC;YACd,gBAAgB,EAAE,CAAC;SACpB,CAAC;IACJ,CAAC;IAKO,qBAAqB,CAAC,SAAiB,EAAE,OAA2B;QAC1E,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,qBAAqB,EAAE,CAAC;YACvC,OAAO;QACT,CAAC;QAED,MAAM,OAAO,GAAG;YACd,SAAS;YACT,aAAa,EAAE,OAAO,CAAC,aAAa;YACpC,WAAW,EAAE,OAAO,CAAC,WAAW;YAChC,QAAQ,EAAE,OAAO,CAAC,QAAQ;YAC1B,YAAY,EAAE,OAAO,CAAC,YAAY;YAClC,SAAS,EAAE,OAAO,CAAC,SAAS;YAC5B,WAAW,EAAE,OAAO,CAAC,WAAW;YAChC,gBAAgB,EAAE,OAAO,CAAC,gBAAgB;YAC1C,YAAY,EAAE,OAAO,CAAC,SAAS,GAAG,OAAO,CAAC,WAAW,GAAG,CAAC;gBACvD,CAAC,CAAC,CAAC,OAAO,CAAC,SAAS,GAAG,CAAC,OAAO,CAAC,SAAS,GAAG,OAAO,CAAC,WAAW,CAAC,CAAC,GAAG,GAAG;gBACvE,CAAC,CAAC,CAAC;SACN,CAAC;QAGF,IAAI,QAAQ,GAA8B,MAAM,CAAC;QAEjD,IAAI,OAAO,CAAC,aAAa,GAAG,IAAI,EAAE,CAAC;YACjC,QAAQ,GAAG,OAAO,CAAC;QACrB,CAAC;aAAM,IAAI,OAAO,CAAC,aAAa,GAAG,IAAI,EAAE,CAAC;YACxC,QAAQ,GAAG,MAAM,CAAC;QACpB,CAAC;QAED,MAAM,OAAO,GAAG,SAAS,SAAS,aAAa,OAAO,CAAC,aAAa,WAAW,CAAC,OAAO,CAAC,WAAW,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,aAAa,OAAO,CAAC,YAAY,GAAG,CAAC;QAElK,QAAQ,QAAQ,EAAE,CAAC;YACjB,KAAK,MAAM;gBACT,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC;gBAClD,MAAM;YACR,KAAK,MAAM;gBACT,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC;gBACnD,MAAM;YACR,KAAK,OAAO;gBACV,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC;gBACpD,MAAM;QACV,CAAC;IACH,CAAC;IAKO,iBAAiB;QACvB,WAAW,CAAC,GAAG,EAAE;YACf,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAChC,CAAC,EAAE,MAAM,CAAC,CAAC;IACb,CAAC;IAKO,sBAAsB;QAC5B,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACvB,MAAM,cAAc,GAAG,MAAM,CAAC;QAE9B,IAAI,YAAY,GAAG,CAAC,CAAC;QACrB,KAAK,MAAM,CAAC,SAAS,EAAE,IAAI,CAAC,IAAI,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,EAAE,CAAC;YAC9D,IAAI,GAAG,GAAG,IAAI,CAAC,SAAS,GAAG,cAAc,EAAE,CAAC;gBAC1C,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;gBACtC,YAAY,EAAE,CAAC;YACjB,CAAC;QACH,CAAC;QAED,IAAI,YAAY,GAAG,CAAC,EAAE,CAAC;YACrB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,YAAY,aAAa,EAAE,oBAAoB,CAAC,CAAC;QAC3E,CAAC;QAGD,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;YACnD,MAAM,aAAa,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,CAAC;iBAC5D,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,SAAS,CAAC,CAAC;YAErD,MAAM,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC;YAChE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,EAAE,CAAC,EAAE,EAAE,CAAC;gBAClC,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAClD,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,QAAQ,aAAa,EAAE,oBAAoB,CAAC,CAAC;QACvE,CAAC;IACH,CAAC;CACF,CAAA;AAtRY,8DAAyB;oCAAzB,yBAAyB;IADrC,IAAA,mBAAU,GAAE;IAOR,WAAA,IAAA,eAAM,EAAC,yBAAyB,CAAC,CAAA;qCADT,8BAAa;GAL7B,yBAAyB,CAsRrC"}