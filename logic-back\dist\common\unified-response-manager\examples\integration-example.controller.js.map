{"version": 3, "file": "integration-example.controller.js", "sourceRoot": "", "sources": ["../../../../src/common/unified-response-manager/examples/integration-example.controller.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAKA,2CAAmF;AACnF,6CAAwD;AAGxD,+FAQsD;AAW/C,IAAM,4BAA4B,GAAlC,MAAM,4BAA4B;IAEgB;IADvD,YACuD,eAAwC;QAAxC,oBAAe,GAAf,eAAe,CAAyB;IAC5F,CAAC;IAQJ,aAAa;QACX,OAAO,oBAAoB,CAAC;IAC9B,CAAC;IAID,aAAa;QACX,OAAO;YACL,EAAE,EAAE,CAAC;YACL,IAAI,EAAE,IAAI;YACV,KAAK,EAAE,sBAAsB;YAC7B,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC;IACJ,CAAC;IAID,YAAY;QACV,OAAO;YACL,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE;YACtB,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE;YACtB,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE;SACvB,CAAC;IACJ,CAAC;IAID,YAAY;QACV,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC;IACrC,CAAC;IAaK,AAAN,KAAK,CAAC,uBAAuB,CAAS,IAAS;QAE7C,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC;QAEvD,OAAO;YACL,OAAO,EAAE,WAAW;YACpB,KAAK,EAAE,IAAI;YACX,WAAW,EAAE,IAAI,IAAI,EAAE;SACxB,CAAC;IACJ,CAAC;IAQK,AAAN,KAAK,CAAC,wBAAwB,CAAS,IAAS;QAE9C,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC;QAEvD,OAAO;YACL,OAAO,EAAE,SAAS;YAClB,KAAK,EAAE,IAAI;YACX,WAAW,EAAE,IAAI,IAAI,EAAE;YACvB,WAAW,EAAE,WAAW;SACzB,CAAC;IACJ,CAAC;IAQK,AAAN,KAAK,CAAC,aAAa,CAAc,EAAU;QACzC,OAAO;YACL,EAAE;YACF,MAAM,EAAE,QAAQ;YAChB,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,QAAQ,EAAE,IAAI;SACf,CAAC;IACJ,CAAC;IAQK,AAAN,KAAK,CAAC,YAAY,CAAS,KAAY;QAErC,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC;QAEvD,OAAO;YACL,OAAO,EAAE,cAAc,KAAK,CAAC,MAAM,IAAI;YACvC,cAAc,EAAE,KAAK,CAAC,MAAM;YAC5B,OAAO,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC;gBACnC,KAAK;gBACL,IAAI;gBACJ,SAAS,EAAE,IAAI;gBACf,WAAW,EAAE,IAAI,IAAI,EAAE;aACxB,CAAC,CAAC;SACJ,CAAC;IACJ,CAAC;IAQK,AAAN,KAAK,CAAC,eAAe;QAEnB,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC;QAEvD,OAAO;YACL,OAAO,EAAE,UAAU;YACnB,QAAQ,EAAE,KAAK;YACf,WAAW,EAAE,IAAI,IAAI,EAAE;SACxB,CAAC;IACJ,CAAC;IAQK,AAAN,KAAK,CAAC,gBAAgB,CAAS,IAAS;QACtC,OAAO;YACL,OAAO,EAAE,UAAU;YACnB,QAAQ,EAAE,MAAM;YAChB,KAAK,EAAE,IAAI;YACX,WAAW,EAAE,IAAI,IAAI,EAAE;SACxB,CAAC;IACJ,CAAC;IAOK,AAAN,KAAK,CAAC,UAAU,CAAS,IAAS;QAEhC,OAAO;YACL,OAAO,EAAE,IAAI;YACb,IAAI,EAAE;gBACJ,EAAE,EAAE,IAAI,CAAC,GAAG,EAAE;gBACd,GAAG,IAAI;aACR;YACD,OAAO,EAAE,WAAW;YACpB,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,aAAa,EAAE,GAAG;SACnB,CAAC;IACJ,CAAC;IAIK,AAAN,KAAK,CAAC,QAAQ,CAAc,EAAU;QAEpC,OAAO;YACL,OAAO,EAAE,IAAI;YACb,IAAI,EAAE;gBACJ,EAAE;gBACF,IAAI,EAAE,KAAK,EAAE,EAAE;gBACf,MAAM,EAAE,QAAQ;aACjB;YACD,OAAO,EAAE,WAAW;YACpB,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,aAAa,EAAE,EAAE;YACjB,SAAS,EAAE,IAAI;YACf,QAAQ,EAAE,UAAU,EAAE,EAAE;SACzB,CAAC;IACJ,CAAC;IAQD,cAAc;QACZ,OAAO;YACL,YAAY,EAAE,IAAI;YAClB,OAAO,EAAE,kBAAkB;YAC3B,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,IAAI,EAAE,YAAY;SACnB,CAAC;IACJ,CAAC;IAQK,AAAN,KAAK,CAAC,aAAa,CAAS,IAAS;QAEnC,MAAM,OAAO,GAAG,IAAI,CAAC,eAAe,CAAC,aAAa,CAChD,EAAE,IAAI,EAAE,EAAE,EAAE,EAAE,aAAa,EAAE,EAAS,EACtC,SAAS,EACT,eAAe,CAChB,CAAC;QAEF,IAAI,CAAC;YAEH,MAAM,MAAM,GAAG;gBACb,OAAO,EAAE,QAAQ;gBACjB,IAAI;gBACJ,WAAW,EAAE,IAAI,IAAI,EAAE;aACxB,CAAC;YAGF,OAAO,MAAM,IAAI,CAAC,eAAe,CAAC,cAAc,CAAC,MAAM,EAAE,OAAO,EAAE;gBAChE,OAAO,EAAE,IAAI;gBACb,QAAQ,EAAE,CAAC;aACZ,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YAEf,OAAO,MAAM,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;QAChE,CAAC;IACH,CAAC;IAOK,AAAN,KAAK,CAAC,aAAa;QACjB,OAAO,MAAM,IAAI,CAAC,eAAe,CAAC,aAAa,EAAE,CAAC;IACpD,CAAC;IAOD,OAAO;QACL,OAAO,IAAI,CAAC;IACd,CAAC;IAID,YAAY;QACV,OAAO,SAAS,CAAC;IACnB,CAAC;IAID,UAAU;QACR,OAAO,IAAI,CAAC;IACd,CAAC;IAID,SAAS;QACP,OAAO,EAAE,CAAC;IACZ,CAAC;IAWK,AAAN,KAAK,CAAC,gBAAgB,CAAS,QAAa;QAE1C,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC;QAEvD,OAAO;YACL,MAAM,EAAE,QAAQ,IAAI,CAAC,GAAG,EAAE,EAAE;YAC5B,QAAQ,EAAE,QAAQ,CAAC,QAAQ;YAC3B,KAAK,EAAE,QAAQ,CAAC,KAAK;YACrB,YAAY,EAAE,IAAI,IAAI,EAAE;YACxB,MAAM,EAAE,QAAQ;SACjB,CAAC;IACJ,CAAC;IAQK,AAAN,KAAK,CAAC,cAAc,CAAmB,OAAe;QACpD,OAAO;YACL,OAAO;YACP,MAAM,EAAE,YAAY;YACpB,QAAQ,EAAE,EAAE;YACZ,mBAAmB,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC;YAClD,WAAW,EAAE,IAAI,IAAI,EAAE;SACxB,CAAC;IACJ,CAAC;IAQK,AAAN,KAAK,CAAC,UAAU,CAAS,YAAiB;QAExC,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;QAExD,OAAO;YACL,QAAQ,EAAE,UAAU,IAAI,CAAC,GAAG,EAAE,EAAE;YAChC,MAAM,EAAE,YAAY;YACpB,MAAM,EAAE,WAAW;YACnB,OAAO,EAAE,6BAA6B;YACtC,WAAW,EAAE,IAAI;YACjB,UAAU,EAAE,IAAI,IAAI,EAAE;SACvB,CAAC;IACJ,CAAC;CACF,CAAA;AA1UY,oEAA4B;AAWvC;IAFC,IAAA,YAAG,EAAC,aAAa,CAAC;IAClB,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,WAAW,EAAE,CAAC;;;;iEAGtC;AAID;IAFC,IAAA,YAAG,EAAC,aAAa,CAAC;IAClB,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,UAAU,EAAE,CAAC;;;;iEAQrC;AAID;IAFC,IAAA,YAAG,EAAC,YAAY,CAAC;IACjB,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,UAAU,EAAE,CAAC;;;;gEAOrC;AAID;IAFC,IAAA,YAAG,EAAC,YAAY,CAAC;IACjB,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,QAAQ,EAAE,CAAC;;;;gEAGnC;AAaK;IARL,IAAA,aAAI,EAAC,iBAAiB,CAAC;IACvB,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,SAAS,EAAE,CAAC;IACpC,IAAA,8CAAe,EAAC;QACf,UAAU,EAAE,SAAS;QACrB,MAAM,EAAE,gBAAgB;QACxB,OAAO,EAAE,IAAI;QACb,QAAQ,EAAE,CAAC;KACZ,CAAC;IAC6B,WAAA,IAAA,aAAI,GAAE,CAAA;;;;2EASpC;AAQK;IANL,IAAA,aAAI,EAAC,4BAA4B,CAAC;IAClC,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,UAAU,EAAE,CAAC;IACrC,IAAA,sDAAuB,EAAC;QACvB,UAAU,EAAE,SAAS;QACrB,MAAM,EAAE,0BAA0B;KACnC,CAAC;IAC8B,WAAA,IAAA,aAAI,GAAE,CAAA;;;;4EAUrC;AAQK;IANL,IAAA,YAAG,EAAC,wBAAwB,CAAC;IAC7B,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,WAAW,EAAE,CAAC;IACtC,IAAA,+CAAgB,EAAC;QAChB,UAAU,EAAE,SAAS;QACrB,MAAM,EAAE,eAAe;KACxB,CAAC;IACmB,WAAA,IAAA,cAAK,EAAC,IAAI,CAAC,CAAA;;;;iEAO/B;AAQK;IANL,IAAA,aAAI,EAAC,iBAAiB,CAAC;IACvB,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,WAAW,EAAE,CAAC;IACtC,IAAA,4CAAa,EAAC;QACb,UAAU,EAAE,SAAS;QACrB,MAAM,EAAE,cAAc;KACvB,CAAC;IACkB,WAAA,IAAA,aAAI,GAAE,CAAA;;;;gEAczB;AAQK;IANL,IAAA,YAAG,EAAC,wBAAwB,CAAC;IAC7B,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,WAAW,EAAE,CAAC;IACtC,IAAA,kDAAmB,EAAC;QACnB,UAAU,EAAE,SAAS;QACrB,MAAM,EAAE,iBAAiB;KAC1B,CAAC;;;;mEAUD;AAQK;IANL,IAAA,aAAI,EAAC,yBAAyB,CAAC;IAC/B,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,WAAW,EAAE,CAAC;IACtC,IAAA,mDAAoB,EAAC;QACpB,UAAU,EAAE,SAAS;QACrB,MAAM,EAAE,kBAAkB;KAC3B,CAAC;IACsB,WAAA,IAAA,aAAI,GAAE,CAAA;;;;oEAO7B;AAOK;IAFL,IAAA,aAAI,EAAC,aAAa,CAAC;IACnB,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,aAAa,EAAE,CAAC;IACvB,WAAA,IAAA,aAAI,GAAE,CAAA;;;;8DAYvB;AAIK;IAFL,IAAA,YAAG,EAAC,eAAe,CAAC;IACpB,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,aAAa,EAAE,CAAC;IACzB,WAAA,IAAA,cAAK,EAAC,IAAI,CAAC,CAAA;;;;4DAe1B;AAQD;IAHC,IAAA,YAAG,EAAC,cAAc,CAAC;IACnB,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,UAAU,EAAE,CAAC;IACrC,IAAA,qDAAsB,GAAE;;;;kEAQxB;AAQK;IAHL,IAAA,aAAI,EAAC,gBAAgB,CAAC;IACtB,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,WAAW,EAAE,CAAC;IACtC,IAAA,qDAAsB,GAAE;IACJ,WAAA,IAAA,aAAI,GAAE,CAAA;;;;iEAyB1B;AAOK;IAFL,IAAA,YAAG,EAAC,qBAAqB,CAAC;IAC1B,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,SAAS,EAAE,CAAC;;;;iEAGpC;AAOD;IAFC,IAAA,YAAG,EAAC,YAAY,CAAC;IACjB,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,QAAQ,EAAE,CAAC;;;;2DAGnC;AAID;IAFC,IAAA,YAAG,EAAC,iBAAiB,CAAC;IACtB,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,aAAa,EAAE,CAAC;;;;gEAGxC;AAID;IAFC,IAAA,YAAG,EAAC,eAAe,CAAC;IACpB,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC;;;;8DAGlC;AAID;IAFC,IAAA,YAAG,EAAC,cAAc,CAAC;IACnB,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC;;;;6DAGjC;AAWK;IANL,IAAA,aAAI,EAAC,4BAA4B,CAAC;IAClC,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,UAAU,EAAE,CAAC;IACrC,IAAA,mDAAoB,EAAC;QACpB,UAAU,EAAE,MAAM;QAClB,MAAM,EAAE,UAAU;KACnB,CAAC;IACsB,WAAA,IAAA,aAAI,GAAE,CAAA;;;;oEAW7B;AAQK;IANL,IAAA,YAAG,EAAC,gCAAgC,CAAC;IACrC,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,YAAY,EAAE,CAAC;IACvC,IAAA,+CAAgB,EAAC;QAChB,UAAU,EAAE,OAAO;QACnB,MAAM,EAAE,WAAW;KACpB,CAAC;IACoB,WAAA,IAAA,cAAK,EAAC,SAAS,CAAC,CAAA;;;;kEAQrC;AAQK;IANL,IAAA,aAAI,EAAC,sBAAsB,CAAC;IAC5B,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,UAAU,EAAE,CAAC;IACrC,IAAA,4CAAa,EAAC;QACb,UAAU,EAAE,MAAM;QAClB,MAAM,EAAE,QAAQ;KACjB,CAAC;IACgB,WAAA,IAAA,aAAI,GAAE,CAAA;;;;8DAYvB;uCAzUU,4BAA4B;IAFxC,IAAA,iBAAO,EAAC,aAAa,CAAC;IACtB,IAAA,mBAAU,EAAC,sBAAsB,CAAC;IAG9B,WAAA,IAAA,eAAM,EAAC,0BAA0B,CAAC,CAAA;;GAF1B,4BAA4B,CA0UxC"}