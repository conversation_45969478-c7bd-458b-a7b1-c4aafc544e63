import { <PERSON><PERSON><PERSON>, OnModuleInit, MiddlewareConsumer, NestModule } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { ScratchModule } from './scratch/scratch.module';
import { WebModule } from './web/web.module';
import { UtilModule } from './util/util.module';
import { CommonServicesModule } from './common/services/common-services.module';
import { TPSModule } from './tps/tps.module';
import { AiVoiceprintRecognitionModule } from './scratch/ai_voiceprint_recognition/ai_voiceprint_recognition.module';
import { UserRoleTemplateTaskService } from './web/user_role/user_role_template_task.service';
import { UserRoleModule } from './web/user_role/user_role.module';
import { PaymentModule } from './payment/payment.module';
import { LoggerModule } from './common/logger/logger.module';
import { HttpLoggerMiddleware } from './common/logger/http-logger.middleware';
import { LoggerService } from './common/logger/logger.service';
import { ResponseModule } from './common/response/response.module';


@Module({
  imports: [
    LoggerModule,
    ResponseModule, // 确保响应管理器在最前面加载
    WebModule,
    ScratchModule,
    UtilModule,
    CommonServicesModule,
    TPSModule,
    AiVoiceprintRecognitionModule,
    UserRoleModule,
    PaymentModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule implements OnModuleInit, NestModule {
  constructor(
    private readonly userRoleTemplateTaskService?: UserRoleTemplateTaskService,
    private readonly loggerService?: LoggerService
  ) {}

  configure(consumer: MiddlewareConsumer) {
    consumer
      .apply(HttpLoggerMiddleware)
      .forRoutes('*');
  }

  async onModuleInit() {
    // 使用日志服务记录模块初始化
    if (this.loggerService) {
      this.loggerService.log('App模块初始化...', 'AppModule');
    }

    // 如果服务注入成功，手动调用模板更新
    if (this.userRoleTemplateTaskService) {
      try {
        if (this.loggerService) {
          this.loggerService.log('通过App模块手动触发模板更新...', 'AppModule');
        }
        await this.userRoleTemplateTaskService.onModuleInit();
      } catch (error) {
        if (this.loggerService) {
          this.loggerService.error('手动触发模板更新失败', error.stack, 'AppModule');
        }
      }
    } else {
      if (this.loggerService) {
        this.loggerService.warn('UserRoleTemplateTaskService未注入到AppModule，无法手动调用', 'AppModule');
      }
    }
  }
}
