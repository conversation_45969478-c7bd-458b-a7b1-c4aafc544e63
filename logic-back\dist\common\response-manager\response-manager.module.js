"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var ResponseManagerModule_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ResponseManagerTestModule = exports.ResponseManagerFeatureModule = exports.ResponseManagerModule = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const response_manager_service_1 = require("./services/response-manager.service");
const request_pool_service_1 = require("./services/request-pool.service");
const performance_monitor_service_1 = require("./services/performance-monitor.service");
const response_manager_interface_1 = require("./interfaces/response-manager.interface");
const logger_service_1 = require("../logger/logger.service");
const DEFAULT_CONFIG = {
    defaultFormat: response_manager_interface_1.ResponseFormat.HTTP,
    enableRequestPool: true,
    maxConcurrency: 10,
    requestTimeout: 30000,
    enableMetrics: true,
    enableDetailedLogging: true,
    redis: {
        host: 'localhost',
        port: 6379,
        db: 0
    },
    cache: {
        defaultTTL: 300,
        maxSize: 1000
    }
};
let ResponseManagerModule = ResponseManagerModule_1 = class ResponseManagerModule {
    static forRoot(options = {}) {
        const config = { ...DEFAULT_CONFIG, ...options.config };
        return {
            module: ResponseManagerModule_1,
            global: options.isGlobal !== false,
            imports: [config_1.ConfigModule],
            providers: [
                {
                    provide: 'RESPONSE_MANAGER_CONFIG',
                    useValue: config
                },
                logger_service_1.LoggerService,
                {
                    provide: 'PERFORMANCE_MONITOR',
                    useClass: performance_monitor_service_1.PerformanceMonitorService
                },
                {
                    provide: 'REQUEST_POOL',
                    useClass: request_pool_service_1.RequestPoolService
                },
                {
                    provide: 'RESPONSE_MANAGER',
                    useClass: response_manager_service_1.ResponseManagerService
                },
                {
                    provide: response_manager_interface_1.IResponseManager,
                    useExisting: 'RESPONSE_MANAGER'
                },
                {
                    provide: response_manager_interface_1.IRequestPool,
                    useExisting: 'REQUEST_POOL'
                },
                {
                    provide: response_manager_interface_1.IPerformanceMonitor,
                    useExisting: 'PERFORMANCE_MONITOR'
                }
            ],
            exports: [
                'RESPONSE_MANAGER_CONFIG',
                'RESPONSE_MANAGER',
                'REQUEST_POOL',
                'PERFORMANCE_MONITOR',
                response_manager_interface_1.IResponseManager,
                response_manager_interface_1.IRequestPool,
                response_manager_interface_1.IPerformanceMonitor
            ]
        };
    }
    static forRootAsync(options) {
        return {
            module: ResponseManagerModule_1,
            global: options.isGlobal !== false,
            imports: [config_1.ConfigModule, ...(options.imports || [])],
            providers: [
                {
                    provide: 'RESPONSE_MANAGER_CONFIG',
                    useFactory: options.useFactory,
                    inject: options.inject || []
                },
                logger_service_1.LoggerService,
                {
                    provide: 'PERFORMANCE_MONITOR',
                    useClass: performance_monitor_service_1.PerformanceMonitorService
                },
                {
                    provide: 'REQUEST_POOL',
                    useClass: request_pool_service_1.RequestPoolService
                },
                {
                    provide: 'RESPONSE_MANAGER',
                    useClass: response_manager_service_1.ResponseManagerService
                },
                {
                    provide: response_manager_interface_1.IResponseManager,
                    useExisting: 'RESPONSE_MANAGER'
                },
                {
                    provide: response_manager_interface_1.IRequestPool,
                    useExisting: 'REQUEST_POOL'
                },
                {
                    provide: response_manager_interface_1.IPerformanceMonitor,
                    useExisting: 'PERFORMANCE_MONITOR'
                }
            ],
            exports: [
                'RESPONSE_MANAGER_CONFIG',
                'RESPONSE_MANAGER',
                'REQUEST_POOL',
                'PERFORMANCE_MONITOR',
                response_manager_interface_1.IResponseManager,
                response_manager_interface_1.IRequestPool,
                response_manager_interface_1.IPerformanceMonitor
            ]
        };
    }
    static forRootWithEnv(options = {}) {
        return this.forRootAsync({
            isGlobal: options.isGlobal,
            imports: [config_1.ConfigModule],
            inject: [config_1.ConfigService],
            useFactory: (configService) => {
                const envConfig = {
                    defaultFormat: response_manager_interface_1.ResponseFormat.HTTP,
                    enableRequestPool: configService.get('RESPONSE_MANAGER_ENABLE_POOL', true),
                    maxConcurrency: configService.get('RESPONSE_MANAGER_MAX_CONCURRENCY', 10),
                    requestTimeout: configService.get('RESPONSE_MANAGER_TIMEOUT', 30000),
                    enableMetrics: configService.get('RESPONSE_MANAGER_ENABLE_METRICS', true),
                    enableDetailedLogging: configService.get('RESPONSE_MANAGER_ENABLE_LOGGING', true),
                    redis: {
                        host: configService.get('REDIS_HOST', 'localhost'),
                        port: configService.get('REDIS_PORT', 6379),
                        password: configService.get('REDIS_PASSWORD'),
                        db: configService.get('REDIS_DB', 0)
                    },
                    cache: {
                        defaultTTL: configService.get('RESPONSE_MANAGER_CACHE_TTL', 300),
                        maxSize: configService.get('RESPONSE_MANAGER_CACHE_SIZE', 1000)
                    }
                };
                return { ...DEFAULT_CONFIG, ...envConfig, ...options.config };
            }
        });
    }
};
exports.ResponseManagerModule = ResponseManagerModule;
exports.ResponseManagerModule = ResponseManagerModule = ResponseManagerModule_1 = __decorate([
    (0, common_1.Global)(),
    (0, common_1.Module)({})
], ResponseManagerModule);
let ResponseManagerFeatureModule = class ResponseManagerFeatureModule {
};
exports.ResponseManagerFeatureModule = ResponseManagerFeatureModule;
exports.ResponseManagerFeatureModule = ResponseManagerFeatureModule = __decorate([
    (0, common_1.Module)({
        providers: [
            logger_service_1.LoggerService,
            performance_monitor_service_1.PerformanceMonitorService,
            request_pool_service_1.RequestPoolService,
            response_manager_service_1.ResponseManagerService
        ],
        exports: [
            response_manager_service_1.ResponseManagerService,
            request_pool_service_1.RequestPoolService,
            performance_monitor_service_1.PerformanceMonitorService
        ]
    })
], ResponseManagerFeatureModule);
let ResponseManagerTestModule = class ResponseManagerTestModule {
};
exports.ResponseManagerTestModule = ResponseManagerTestModule;
exports.ResponseManagerTestModule = ResponseManagerTestModule = __decorate([
    (0, common_1.Module)({
        providers: [
            {
                provide: 'RESPONSE_MANAGER_CONFIG',
                useValue: {
                    ...DEFAULT_CONFIG,
                    enableRequestPool: false,
                    enableMetrics: false,
                    enableDetailedLogging: false
                }
            },
            logger_service_1.LoggerService,
            {
                provide: 'PERFORMANCE_MONITOR',
                useClass: performance_monitor_service_1.PerformanceMonitorService
            },
            {
                provide: 'REQUEST_POOL',
                useClass: request_pool_service_1.RequestPoolService
            },
            {
                provide: 'RESPONSE_MANAGER',
                useClass: response_manager_service_1.ResponseManagerService
            }
        ],
        exports: [
            'RESPONSE_MANAGER',
            'REQUEST_POOL',
            'PERFORMANCE_MONITOR'
        ]
    })
], ResponseManagerTestModule);
//# sourceMappingURL=response-manager.module.js.map