import { CommandResult } from '../../../util/ip_location/application/interfaces/command-handler.interface';
import { QueryResult } from '../../../util/ip_location/application/interfaces/query-handler.interface';
import { HttpResponse } from '../../../web/http_response_result/http-response.interface';
export interface RequestContext {
    requestId: string;
    userId?: string;
    sessionId?: string;
    clientIp?: string;
    userAgent?: string;
    path?: string;
    method?: string;
    module?: string;
    operation?: string;
    startTime: number;
    metadata?: Record<string, any>;
}
export interface PerformanceMetrics {
    executionTime: number;
    memoryUsage?: number;
    cpuUsage?: number;
    dbQueryCount?: number;
    cacheHits?: number;
    cacheMisses?: number;
    externalApiCalls?: number;
}
export interface RequestPoolStatus {
    queueSize: number;
    processingCount: number;
    maxConcurrency: number;
    averageWaitTime: number;
    averageProcessingTime: number;
    successRate: number;
}
export interface EnhancedResponse<T = any> {
    context: RequestContext;
    data?: T;
    success: boolean;
    message?: string;
    errors?: string[];
    code: number;
    metrics: PerformanceMetrics;
    timestamp: Date;
    fromCache?: boolean;
    cacheKey?: string;
    poolInfo?: {
        position?: number;
        estimatedWaitTime?: number;
    };
}
export declare enum ResponseFormat {
    HTTP = "http",
    COMMAND = "command",
    QUERY = "query",
    ENHANCED = "enhanced",
    API = "api"
}
export interface ResponseManagerConfig {
    defaultFormat: ResponseFormat;
    enableRequestPool: boolean;
    maxConcurrency: number;
    requestTimeout: number;
    enableMetrics: boolean;
    enableDetailedLogging: boolean;
    redis: {
        host: string;
        port: number;
        password?: string;
        db: number;
    };
    cache: {
        defaultTTL: number;
        maxSize: number;
    };
}
export interface IResponseManager {
    handleCommandResult<T>(commandResult: CommandResult<T>, context: Partial<RequestContext>, format?: ResponseFormat): Promise<EnhancedResponse<T>>;
    handleQueryResult<T>(queryResult: QueryResult<T>, context: Partial<RequestContext>, format?: ResponseFormat): Promise<EnhancedResponse<T>>;
    createContext(request: any, module?: string, operation?: string): RequestContext;
    toHttpResponse<T>(enhancedResponse: EnhancedResponse<T>): HttpResponse<T>;
    getPoolStatus(): Promise<RequestPoolStatus>;
    logResponse<T>(enhancedResponse: EnhancedResponse<T>, level?: 'info' | 'warn' | 'error'): void;
}
export interface IRequestPool {
    enqueue<T>(requestId: string, handler: () => Promise<T>, priority?: number, timeout?: number): Promise<T>;
    getStatus(): Promise<RequestPoolStatus>;
    cleanup(): Promise<void>;
}
export interface IPerformanceMonitor {
    startMonitoring(requestId: string): void;
    endMonitoring(requestId: string): PerformanceMetrics;
    recordDbQuery(requestId: string): void;
    recordCacheOperation(requestId: string, hit: boolean): void;
    recordExternalApiCall(requestId: string): void;
}
