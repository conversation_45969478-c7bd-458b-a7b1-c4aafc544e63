{"version": 3, "file": "unified-response-test.controller.js", "sourceRoot": "", "sources": ["../../../../src/util/ip_location/examples/unified-response-test.controller.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAKA,2CAAmF;AACnF,6CAAwD;AAGxD,qIAM4F;AAG5F,6GAAuG;AACvG,iGAA2F;AAC3F,qGAA+F;AAK/F,8FAAuF;AACvF,wGAAiG;AACjG,4FAAsF;AAO/E,IAAM,uCAAuC,GAA7C,MAAM,uCAAuC;IAE/B;IACA;IACA;IACoC;IAJvD,YACmB,iBAA+C,EAC/C,YAAoC,EACpC,cAAwC,EACJ,eAAoB;QAHxD,sBAAiB,GAAjB,iBAAiB,CAA8B;QAC/C,iBAAY,GAAZ,YAAY,CAAwB;QACpC,mBAAc,GAAd,cAAc,CAA0B;QACJ,oBAAe,GAAf,eAAe,CAAK;IACxE,CAAC;IAQE,AAAN,KAAK,CAAC,WAAW,CAAc,KAAa,SAAS;QAEnD,MAAM,KAAK,GAAG,IAAI,+CAAoB,CAAC,EAAS,EAAE,KAAK,CAAC,CAAC;QACzD,OAAO,MAAM,IAAI,CAAC,YAAY,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAC;IAC9D,CAAC;IAIK,AAAN,KAAK,CAAC,aAAa,CAAS,OAAuC;QAEjE,MAAM,KAAK,GAAG,IAAI,8CAAoB,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,OAAO,CAAC,EAAS,CAAC,CAAC;QACpF,OAAO,MAAM,IAAI,CAAC,YAAY,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAC;IAC9D,CAAC;IAaK,AAAN,KAAK,CAAC,gBAAgB,CAAc,KAAa,SAAS;QACxD,MAAM,KAAK,GAAG,IAAI,+CAAoB,CAAC,EAAS,EAAE,KAAK,CAAC,CAAC;QACzD,OAAO,MAAM,IAAI,CAAC,YAAY,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAC;IAC9D,CAAC;IAQK,AAAN,KAAK,CAAC,kBAAkB,CAAS,OAAuC;QACtE,MAAM,KAAK,GAAG,IAAI,8CAAoB,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,OAAO,CAAC,EAAS,CAAC,CAAC;QACpF,OAAO,MAAM,IAAI,CAAC,YAAY,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAC;IAC9D,CAAC;IAQK,AAAN,KAAK,CAAC,kBAAkB,CAAkB,MAAc;QACtD,MAAM,KAAK,GAAG,IAAI,yDAAyB,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,EAAE,CAAC,CAAC;QAClE,OAAO,MAAM,IAAI,CAAC,YAAY,CAAC,0BAA0B,CAAC,KAAK,CAAC,CAAC;IACnE,CAAC;IAWK,AAAN,KAAK,CAAC,iBAAiB;QAErB,MAAM,MAAM,GAAG,eAAe,CAAC;QAC/B,MAAM,KAAK,GAAG,IAAI,+CAAoB,CAAC,MAAa,EAAE,KAAK,CAAC,CAAC;QAC7D,OAAO,MAAM,IAAI,CAAC,YAAY,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAC;IAC9D,CAAC;IAOD,SAAS;QACP,OAAO,sBAAsB,CAAC;IAChC,CAAC;IAID,SAAS;QACP,OAAO;YACL,MAAM,EAAE,YAAY;YACpB,OAAO,EAAE,OAAO;YAChB,QAAQ,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;YAClC,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC;IACJ,CAAC;IAID,QAAQ;QACN,OAAO;YACL,EAAE,EAAE,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE;YAChC,EAAE,EAAE,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE;YAChC,EAAE,EAAE,EAAE,iBAAiB,EAAE,OAAO,EAAE,IAAI,EAAE;SACzC,CAAC;IACJ,CAAC;IAID,UAAU;QACR,MAAM,IAAI,KAAK,CAAC,oBAAoB,CAAC,CAAC;IACxC,CAAC;IAOD,aAAa;QACX,OAAO;YACL,OAAO,EAAE,IAAI;YACb,IAAI,EAAE;gBACJ,EAAE,EAAE,SAAS;gBACb,OAAO,EAAE,IAAI;gBACb,QAAQ,EAAE,QAAQ;gBAClB,IAAI,EAAE,KAAK;aACZ;YACD,OAAO,EAAE,YAAY;YACrB,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,aAAa,EAAE,GAAG;YAClB,SAAS,EAAE,IAAI;YACf,QAAQ,EAAE,qBAAqB;SAChC,CAAC;IACJ,CAAC;IAID,WAAW;QACT,OAAO;YACL,OAAO,EAAE,KAAK;YACd,IAAI,EAAE,IAAI;YACV,OAAO,EAAE,UAAU;YACnB,MAAM,EAAE,CAAC,UAAU,EAAE,WAAW,CAAC;YACjC,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,aAAa,EAAE,EAAE;SAClB,CAAC;IACJ,CAAC;IAID,eAAe;QACb,OAAO;YACL,IAAI,EAAE,GAAG;YACT,GAAG,EAAE,MAAM;YACX,IAAI,EAAE;gBACJ,EAAE,EAAE,SAAS;gBACb,QAAQ,EAAE,IAAI;aACf;SACF,CAAC;IACJ,CAAC;IAQD,cAAc;QACZ,OAAO;YACL,YAAY,EAAE,IAAI;YAClB,MAAM,EAAE,YAAY;YACpB,OAAO,EAAE,kBAAkB;YAC3B,IAAI,EAAE;gBACJ,EAAE,EAAE,WAAW;gBACf,QAAQ,EAAE,WAAW;aACtB;YACD,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC;IACJ,CAAC;IAQK,AAAN,KAAK,CAAC,aAAa,CAAS,OAAuB;QAEjD,MAAM,OAAO,GAAG,IAAI,CAAC,eAAe,CAAC,aAAa,CAChD,EAAE,IAAI,EAAE,EAAE,EAAE,EAAE,WAAW,EAAE,EAAS,EACpC,gBAAgB,EAChB,eAAe,CAChB,CAAC;QAEF,IAAI,CAAC;YAEH,MAAM,KAAK,GAAG,IAAI,+CAAoB,CAAC,OAAO,CAAC,EAAS,EAAE,KAAK,CAAC,CAAC;YACjE,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAC;YAGpE,OAAO,MAAM,IAAI,CAAC,eAAe,CAAC,cAAc,CAAC,MAAM,EAAE,OAAO,EAAE;gBAChE,OAAO,EAAE,IAAI;gBACb,QAAQ,EAAE,CAAC;aACZ,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YAEf,OAAO,MAAM,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;QAChE,CAAC;IACH,CAAC;IAOK,AAAN,KAAK,CAAC,aAAa;QACjB,OAAO,MAAM,IAAI,CAAC,eAAe,CAAC,aAAa,EAAE,CAAC;IACpD,CAAC;IAWK,AAAN,KAAK,CAAC,aAAa,CAAS,OAA0B;QACpD,MAAM,OAAO,GAAU,EAAE,CAAC;QAE1B,KAAK,MAAM,EAAE,IAAI,OAAO,CAAC,GAAG,EAAE,CAAC;YAC7B,IAAI,CAAC;gBACH,MAAM,KAAK,GAAG,IAAI,+CAAoB,CAAC,EAAS,EAAE,KAAK,CAAC,CAAC;gBACzD,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAC;gBACpE,OAAO,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;YAC9C,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,CAAC;YAC7D,CAAC;QACH,CAAC;QAED,OAAO;YACL,OAAO,EAAE,IAAI;YACb,IAAI,EAAE,OAAO;YACb,OAAO,EAAE,cAAc,OAAO,CAAC,GAAG,CAAC,MAAM,MAAM;YAC/C,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,aAAa,EAAE,OAAO,CAAC,MAAM,GAAG,GAAG;SACpC,CAAC;IACJ,CAAC;IAYK,AAAN,KAAK,CAAC,YAAY,CAAS,OAAsC;QAE/D,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,OAAO,CAAC,KAAK,IAAI,IAAI,CAAC,CAAC,CAAC;QAEzE,MAAM,KAAK,GAAG,IAAI,+CAAoB,CAAC,OAAO,CAAC,EAAS,EAAE,KAAK,CAAC,CAAC;QACjE,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAC;QAEpE,OAAO;YACL,GAAG,MAAM;YACT,OAAO,EAAE,MAAM,OAAO,CAAC,KAAK,UAAU;YACtC,SAAS,EAAE,OAAO,CAAC,KAAK;SACzB,CAAC;IACJ,CAAC;CACF,CAAA;AAtRY,0FAAuC;AAc5C;IAFL,IAAA,YAAG,EAAC,eAAe,CAAC;IACpB,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,kBAAkB,EAAE,CAAC;IAC3B,WAAA,IAAA,cAAK,EAAC,IAAI,CAAC,CAAA;;;;0EAI7B;AAIK;IAFL,IAAA,aAAI,EAAC,iBAAiB,CAAC;IACvB,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,kBAAkB,EAAE,CAAC;IACzB,WAAA,IAAA,aAAI,GAAE,CAAA;;;;4EAI1B;AAaK;IARL,IAAA,YAAG,EAAC,oBAAoB,CAAC;IACzB,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,cAAc,EAAE,CAAC;IACzC,IAAA,8CAAe,EAAC;QACf,UAAU,EAAE,gBAAgB;QAC5B,MAAM,EAAE,SAAS;QACjB,OAAO,EAAE,KAAK;QACd,QAAQ,EAAE,CAAC;KACZ,CAAC;IACsB,WAAA,IAAA,cAAK,EAAC,IAAI,CAAC,CAAA;;;;+EAGlC;AAQK;IANL,IAAA,aAAI,EAAC,sBAAsB,CAAC;IAC5B,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,cAAc,EAAE,CAAC;IACzC,IAAA,sDAAuB,EAAC;QACvB,UAAU,EAAE,gBAAgB;QAC5B,MAAM,EAAE,WAAW;KACpB,CAAC;IACwB,WAAA,IAAA,aAAI,GAAE,CAAA;;;;iFAG/B;AAQK;IANL,IAAA,YAAG,EAAC,8BAA8B,CAAC;IACnC,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,cAAc,EAAE,CAAC;IACzC,IAAA,4CAAa,EAAC;QACb,UAAU,EAAE,gBAAgB;QAC5B,MAAM,EAAE,WAAW;KACpB,CAAC;IACwB,WAAA,IAAA,cAAK,EAAC,QAAQ,CAAC,CAAA;;;;iFAGxC;AAWK;IANL,IAAA,YAAG,EAAC,qBAAqB,CAAC;IAC1B,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,eAAe,EAAE,CAAC;IAC1C,IAAA,+CAAgB,EAAC;QAChB,UAAU,EAAE,gBAAgB;QAC5B,MAAM,EAAE,cAAc;KACvB,CAAC;;;;gFAMD;AAOD;IAFC,IAAA,YAAG,EAAC,cAAc,CAAC;IACnB,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,SAAS,EAAE,CAAC;;;;wEAGpC;AAID;IAFC,IAAA,YAAG,EAAC,cAAc,CAAC;IACnB,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,QAAQ,EAAE,CAAC;;;;wEAQnC;AAID;IAFC,IAAA,YAAG,EAAC,aAAa,CAAC;IAClB,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC;;;;uEAOjC;AAID;IAFC,IAAA,YAAG,EAAC,aAAa,CAAC;IAClB,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,QAAQ,EAAE,CAAC;;;;yEAGnC;AAOD;IAFC,IAAA,YAAG,EAAC,mBAAmB,CAAC;IACxB,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,SAAS,EAAE,CAAC;;;;4EAgBpC;AAID;IAFC,IAAA,YAAG,EAAC,iBAAiB,CAAC;IACtB,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,SAAS,EAAE,CAAC;;;;0EAUpC;AAID;IAFC,IAAA,YAAG,EAAC,qBAAqB,CAAC;IAC1B,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,gBAAgB,EAAE,CAAC;;;;8EAU3C;AAQD;IAHC,IAAA,YAAG,EAAC,cAAc,CAAC;IACnB,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,UAAU,EAAE,CAAC;IACrC,IAAA,qDAAsB,GAAE;;;;6EAYxB;AAQK;IAHL,IAAA,aAAI,EAAC,gBAAgB,CAAC;IACtB,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,WAAW,EAAE,CAAC;IACtC,IAAA,qDAAsB,GAAE;IACJ,WAAA,IAAA,aAAI,GAAE,CAAA;;;;4EAsB1B;AAOK;IAFL,IAAA,YAAG,EAAC,qBAAqB,CAAC;IAC1B,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,SAAS,EAAE,CAAC;;;;4EAGpC;AAWK;IANL,IAAA,aAAI,EAAC,iBAAiB,CAAC;IACvB,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,QAAQ,EAAE,CAAC;IACnC,IAAA,4CAAa,EAAC;QACb,UAAU,EAAE,gBAAgB;QAC5B,MAAM,EAAE,eAAe;KACxB,CAAC;IACmB,WAAA,IAAA,aAAI,GAAE,CAAA;;;;4EAoB1B;AAYK;IAPL,IAAA,aAAI,EAAC,qBAAqB,CAAC;IAC3B,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,QAAQ,EAAE,CAAC;IACnC,IAAA,8CAAe,EAAC;QACf,UAAU,EAAE,gBAAgB;QAC5B,MAAM,EAAE,cAAc;QACtB,OAAO,EAAE,KAAK;KACf,CAAC;IACkB,WAAA,IAAA,aAAI,GAAE,CAAA;;;;2EAYzB;kDArRU,uCAAuC;IAFnD,IAAA,iBAAO,EAAC,oBAAoB,CAAC;IAC7B,IAAA,mBAAU,EAAC,mCAAmC,CAAC;IAM3C,WAAA,IAAA,eAAM,EAAC,0BAA0B,CAAC,CAAA;qCAHC,8DAA4B;QACjC,kDAAsB;QACpB,sDAAwB;GAJhD,uCAAuC,CAsRnD"}