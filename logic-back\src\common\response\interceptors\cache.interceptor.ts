import {
  Injectable,
  NestInterceptor,
  ExecutionContext,
  CallHandler,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { Observable, of } from 'rxjs';
import { tap } from 'rxjs/operators';
import { Request } from 'express';
import { ResponseCacheService } from '../services/response-cache.service';
import { LoggerService } from '../../logger/logger.service';
import { CACHE_CONFIG_KEY, CacheDecoratorConfig } from '../decorators/cache.decorator';
import { BaseResponse } from '../interfaces/response.interface';

/**
 * 缓存拦截器
 * 自动处理响应缓存的读取和写入
 */
@Injectable()
export class CacheInterceptor implements NestInterceptor {
  constructor(
    private readonly cacheService: ResponseCacheService,
    private readonly reflector: Reflector,
    private readonly loggerService: LoggerService,
  ) {}

  async intercept(context: ExecutionContext, next: CallHandler): Promise<Observable<any>> {
    const request = context.switchToHttp().getRequest<Request>();
    const handler = context.getHandler();
    
    // 获取缓存配置
    const cacheConfig = this.reflector.get<CacheDecoratorConfig>(
      CACHE_CONFIG_KEY,
      handler,
    );

    // 如果没有缓存配置或缓存被禁用，直接执行
    if (!cacheConfig || !cacheConfig.enabled) {
      return next.handle();
    }

    // 检查是否应该使用缓存
    if (!this.shouldUseCache(request, cacheConfig)) {
      return next.handle();
    }

    const method = request.method;
    const path = request.path;
    const params = this.extractParams(request, cacheConfig);
    const userId = this.extractUserId(request, cacheConfig);
    const requestId = this.generateRequestId();

    try {
      // 尝试从缓存获取响应
      const cachedResponse = await this.cacheService.get(
        method,
        path,
        params,
        userId,
        cacheConfig
      );

      if (cachedResponse) {
        // 缓存命中，直接返回缓存的响应
        this.loggerService.logResponseProcessing({
          requestId,
          stage: 'interceptor',
          action: 'cache_hit',
          details: {
            method,
            path,
            userId,
            cacheKey: this.generateCacheKey(method, path, params, userId),
          }
        });

        return of(cachedResponse);
      }

      // 缓存未命中，执行原始方法并缓存结果
      return next.handle().pipe(
        tap(async (response) => {
          try {
            // 检查是否应该缓存响应
            if (this.shouldCacheResponse(response, cacheConfig)) {
              await this.cacheService.set(
                method,
                path,
                response,
                params,
                userId,
                cacheConfig.defaultTtl,
                cacheConfig
              );

              this.loggerService.logResponseProcessing({
                requestId,
                stage: 'interceptor',
                action: 'cache_set',
                details: {
                  method,
                  path,
                  userId,
                  ttl: cacheConfig.defaultTtl,
                  responseSize: JSON.stringify(response).length,
                }
              });
            }
          } catch (error) {
            // 缓存写入失败不应该影响正常响应
            this.loggerService.error('Failed to cache response', error.stack, 'CacheInterceptor');
          }
        })
      );

    } catch (error) {
      // 缓存读取失败，继续执行原始方法
      this.loggerService.error('Failed to read from cache', error.stack, 'CacheInterceptor');
      return next.handle();
    }
  }

  /**
   * 检查是否应该使用缓存
   */
  private shouldUseCache(request: Request, config: CacheDecoratorConfig): boolean {
    // 检查自定义条件
    if (config.condition && !config.condition(request)) {
      return false;
    }

    // 只缓存GET请求（除非明确配置）
    if (request.method !== 'GET' && !config.includeParams) {
      return false;
    }

    // 检查请求头中的缓存控制
    const cacheControl = request.get('Cache-Control');
    if (cacheControl && cacheControl.includes('no-cache')) {
      return false;
    }

    return true;
  }

  /**
   * 检查是否应该缓存响应
   */
  private shouldCacheResponse(response: any, config: CacheDecoratorConfig): boolean {
    // 检查自定义条件
    if (config.condition && !config.condition(null, response)) {
      return false;
    }

    // 不缓存错误响应
    if (response.success === false || response.code >= 400) {
      return false;
    }

    // 不缓存空数据（除非明确配置）
    if (!response.data && !config.includeParams) {
      return false;
    }

    // 检查响应大小
    const responseSize = JSON.stringify(response).length;
    if (config.maxSize && responseSize > config.maxSize) {
      return false;
    }

    return true;
  }

  /**
   * 提取请求参数
   */
  private extractParams(request: Request, config: CacheDecoratorConfig): any {
    if (!config.includeParams) {
      return null;
    }

    return {
      query: request.query,
      params: request.params,
    };
  }

  /**
   * 提取用户ID
   */
  private extractUserId(request: Request, config: CacheDecoratorConfig): string | number | undefined {
    if (!config.includeUserId) {
      return undefined;
    }

    const user = (request as any).user;
    return user?.id || user?.userId;
  }

  /**
   * 生成缓存键
   */
  private generateCacheKey(
    method: string,
    path: string,
    params?: any,
    userId?: string | number
  ): string {
    const paramsStr = params ? JSON.stringify(params) : '';
    const userStr = userId ? `_user:${userId}` : '';
    return `${method}:${path}${userStr}${paramsStr}`;
  }

  /**
   * 生成请求ID
   */
  private generateRequestId(): string {
    return `cache_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

/**
 * 缓存清理拦截器
 * 用于在特定操作后清理相关缓存
 */
@Injectable()
export class CacheClearInterceptor implements NestInterceptor {
  constructor(
    private readonly cacheService: ResponseCacheService,
    private readonly reflector: Reflector,
    private readonly loggerService: LoggerService,
  ) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest<Request>();
    const handler = context.getHandler();
    
    // 获取缓存配置
    const cacheConfig = this.reflector.get<CacheDecoratorConfig>(
      CACHE_CONFIG_KEY,
      handler,
    );

    return next.handle().pipe(
      tap(async (response) => {
        // 只在成功响应后清理缓存
        if (response.success !== false && response.code < 400) {
          await this.clearRelatedCache(request, cacheConfig);
        }
      })
    );
  }

  /**
   * 清理相关缓存
   */
  private async clearRelatedCache(request: Request, config?: CacheDecoratorConfig): Promise<void> {
    if (!config || !config.tags) {
      return;
    }

    try {
      // 根据标签清理缓存
      for (const tag of config.tags) {
        await this.cacheService.deleteByPattern(`*:${tag}:*`);
      }

      this.loggerService.logResponseProcessing({
        requestId: this.generateRequestId(),
        stage: 'interceptor',
        action: 'cache_clear',
        details: {
          method: request.method,
          path: request.path,
          tags: config.tags,
        }
      });
    } catch (error) {
      this.loggerService.error('Failed to clear cache', error.stack, 'CacheClearInterceptor');
    }
  }

  /**
   * 生成请求ID
   */
  private generateRequestId(): string {
    return `cache_clear_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

/**
 * 缓存预热拦截器
 * 用于预热常用的缓存数据
 */
@Injectable()
export class CacheWarmupInterceptor implements NestInterceptor {
  constructor(
    private readonly cacheService: ResponseCacheService,
    private readonly loggerService: LoggerService,
  ) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest<Request>();
    
    return next.handle().pipe(
      tap(async (response) => {
        // 在后台预热相关缓存
        this.warmupRelatedCache(request, response);
      })
    );
  }

  /**
   * 预热相关缓存
   */
  private async warmupRelatedCache(request: Request, response: any): Promise<void> {
    try {
      // 这里可以实现预热逻辑
      // 例如：预热相关的分页数据、用户数据等
      
      this.loggerService.logResponseProcessing({
        requestId: this.generateRequestId(),
        stage: 'interceptor',
        action: 'cache_warmup',
        details: {
          method: request.method,
          path: request.path,
        }
      });
    } catch (error) {
      this.loggerService.error('Failed to warmup cache', error.stack, 'CacheWarmupInterceptor');
    }
  }

  /**
   * 生成请求ID
   */
  private generateRequestId(): string {
    return `cache_warmup_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}
