# IP地理位置服务模块

基于DDD架构设计的IP地理位置服务，集成了响应管理器，提供统一的响应格式、智能缓存和详细日志记录。

## 🚀 功能特性

- 🌍 **IP地理位置查询** - 支持多种IP数据库和在线服务
- 🔒 **登录风险评估** - 基于地理位置的安全风险分析
- 📊 **用户位置统计** - 用户常用登录地统计和分析
- 🛡️ **可信位置管理** - 用户可信登录地设置和管理
- 📝 **统一响应格式** - 标准化的API响应格式
- 🚀 **智能缓存** - Redis缓存提升查询性能
- 📊 **详细日志** - 完整的请求响应日志记录
- ⚡ **性能监控** - 自动性能监控和告警

## 📁 模块结构

```
src/util/ip_location/
├── controllers/
│   └── ip-location.controller.ts     # 控制器层
├── services/
│   ├── ip-location-facade.service.ts # 门面服务
│   ├── ip-location.service.ts        # 核心业务服务
│   └── risk-assessment.service.ts    # 风险评估服务
├── entities/
│   └── user-common-location.entity.ts # 数据实体
├── dto/
│   ├── ip-query-request.dto.ts       # 请求DTO
│   └── response.dto.ts               # 响应DTO
├── tests/
│   └── ip-location-response.integration.spec.ts # 集成测试
├── ip-location.module.ts             # 模块定义
├── RESPONSE_INTEGRATION.md           # 响应管理器集成指南
└── README.md                         # 本文档
```

## 🔧 快速开始

### 1. 模块导入

```typescript
import { IpLocationModule } from './util/ip_location/ip-location.module';

@Module({
  imports: [
    IpLocationModule, // IP地理位置模块
    // 其他模块...
  ],
})
export class AppModule {}
```

### 2. 基础使用

```typescript
import { IpLocationController } from './util/ip_location/controllers/ip-location.controller';

// 注入控制器或直接使用API
```

## 📚 API接口

### 1. IP地理位置查询

```http
GET /ip-location/query?ip=**************&includeRisk=false
```

**响应：**
```json
{
  "success": true,
  "code": 200,
  "message": "IP地理位置查询成功",
  "data": {
    "location": {
      "country": "中国",
      "province": "北京市",
      "city": "北京市",
      "isp": "电信"
    },
    "meta": {
      "executionTime": 45,
      "fromCache": false,
      "ip": "**************"
    }
  },
  "timestamp": "2024-01-01T00:00:00.000Z",
  "requestId": "req_1704067200000_abc123"
}
```

### 2. 登录风险检查

```http
POST /ip-location/check-risk
Content-Type: application/json

{
  "userId": 123,
  "ipAddress": "**************",
  "userAgent": "Mozilla/5.0...",
  "sessionId": "session_123"
}
```

### 3. 用户位置统计

```http
GET /ip-location/user/123/stats?days=30&includeTrusted=true
```

### 4. 设置可信登录地

```http
POST /ip-location/user/123/trust
Content-Type: application/json

{
  "province": "北京市",
  "city": "北京市",
  "reason": "用户常用登录地"
}
```

### 5. 获取当前IP位置

```http
GET /ip-location/current
```

### 6. 健康检查

```http
GET /ip-location/health
```

### 7. 缓存管理

```http
POST /ip-location/cache/clear?type=all
POST /ip-location/cache/clear?type=user&target=123
POST /ip-location/cache/clear?type=ip&target=**************
```

## 🚀 缓存策略

### 缓存配置

- **IP查询缓存**: 5分钟 (`@MediumCache(300)`)
- **用户统计缓存**: 10分钟 (`@UserCache(600)`)
- **当前IP缓存**: 3分钟 (`@MediumCache(180)`)
- **风险检查**: 不缓存 (`@NoCache()`)
- **健康检查**: 不缓存 (`@NoCache()`)

### 缓存键策略

```typescript
// IP查询缓存键
GET:/ip-location/query?ip=**************

// 用户统计缓存键
GET:/ip-location/user/123/stats_user:123

// 当前IP缓存键
GET:/ip-location/current
```

## 📊 监控和日志

### 自动日志记录

- **API请求日志**: 记录所有API请求和响应
- **缓存操作日志**: 记录缓存命中、设置、清理操作
- **性能监控日志**: 记录执行时间超过阈值的请求
- **错误追踪日志**: 记录所有异常和错误信息

### 性能指标

- 响应时间监控（阈值：1秒）
- 缓存命中率统计
- 错误率监控
- 并发请求监控

## 🔒 错误处理

### 错误类型

- **VALIDATION**: 参数验证错误
- **BUSINESS**: 业务逻辑错误
- **AUTH**: 认证授权错误
- **SYSTEM**: 系统内部错误

### 错误响应格式

```json
{
  "success": false,
  "code": 400,
  "message": "参数验证失败",
  "errors": ["用户ID必须是正整数"],
  "errorType": "VALIDATION",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "requestId": "req_1704067200000_error123"
}
```

## 🧪 测试

### 运行测试

```bash
# 单元测试
npm test ip-location

# 集成测试
npm test ip-location-response.integration

# 测试覆盖率
npm run test:cov ip-location
```

### 测试用例

- IP地理位置查询测试
- 登录风险评估测试
- 用户位置统计测试
- 缓存功能测试
- 错误处理测试
- 响应格式一致性测试

## 🔧 配置

### 环境变量

```env
# Redis配置（用于缓存）
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# IP数据库配置
IP_DATABASE_PATH=/path/to/ip.db
IP_API_KEY=your_api_key

# 日志配置
LOG_LEVEL=info
LOG_RESPONSE_BODY=false
```

### 模块配置

```typescript
// 在模块中配置
@Module({
  imports: [
    ResponseModule,  // 响应管理器
    RedisModule,     // Redis缓存
    TypeOrmModule.forFeature([UserCommonLocation]),
  ],
  // ...
})
export class IpLocationModule {}
```

## 📈 性能优化

### 缓存优化

1. **合理设置TTL**: 根据数据变化频率设置缓存时间
2. **缓存预热**: 对热点数据进行预热
3. **缓存清理**: 及时清理无效缓存

### 查询优化

1. **数据库索引**: 为常用查询字段添加索引
2. **批量查询**: 支持批量IP查询
3. **异步处理**: 非关键路径使用异步处理

## 🔗 相关文档

- [响应管理器集成指南](./RESPONSE_INTEGRATION.md)
- [响应管理器基础文档](../../common/response/README.md)
- [缓存功能指南](../../common/response/CACHE_GUIDE.md)
- [DDD架构设计文档](./docs/DDD_ARCHITECTURE.md)

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。
