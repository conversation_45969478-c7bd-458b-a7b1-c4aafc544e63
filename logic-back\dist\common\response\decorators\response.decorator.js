"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.RESPONSE_CONFIG_KEY = void 0;
exports.Response = Response;
exports.SuccessResponse = SuccessResponse;
exports.PaginatedResponse = PaginatedResponse;
exports.SimpleResponse = SimpleResponse;
exports.RestfulResponse = RestfulResponse;
exports.ErrorResponse = ErrorResponse;
exports.CommonErrorResponses = CommonErrorResponses;
exports.ApiResponseDocs = ApiResponseDocs;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const response_interface_1 = require("../interfaces/response.interface");
exports.RESPONSE_CONFIG_KEY = 'response_config';
function Response(config) {
    const decorators = [(0, common_1.SetMetadata)(exports.RESPONSE_CONFIG_KEY, config || {})];
    if (config?.swagger) {
        decorators.push((0, swagger_1.ApiResponse)({
            status: 200,
            description: config.swagger.description || '操作成功',
            schema: config.swagger.example ? {
                example: config.swagger.example
            } : undefined,
            type: config.swagger.type,
        }));
    }
    return (0, common_1.applyDecorators)(...decorators);
}
function SuccessResponse(message, example) {
    return Response({
        successMessage: message,
        swagger: {
            description: message || '操作成功',
            example: example || {
                success: true,
                code: 200,
                message: message || '操作成功',
                data: {},
                timestamp: new Date().toISOString(),
                requestId: 'uuid-example'
            }
        }
    });
}
function PaginatedResponse(message, itemType) {
    return Response({
        successMessage: message,
        swagger: {
            description: message || '获取分页数据成功',
            example: {
                success: true,
                code: 200,
                message: message || '获取分页数据成功',
                data: [],
                pagination: {
                    page: 1,
                    size: 10,
                    total: 100,
                    totalPages: 10,
                    hasNext: true,
                    hasPrev: false
                },
                timestamp: new Date().toISOString(),
                requestId: 'uuid-example'
            },
            type: itemType
        }
    });
}
function SimpleResponse(message) {
    return Response({
        format: response_interface_1.ResponseFormat.SIMPLE,
        successMessage: message,
        swagger: {
            description: message || '操作成功',
            example: {
                code: 200,
                msg: message || '操作成功',
                data: {}
            }
        }
    });
}
function RestfulResponse(type) {
    return Response({
        format: response_interface_1.ResponseFormat.RESTFUL,
        swagger: {
            description: '操作成功',
            type: type
        }
    });
}
function ErrorResponse(status, message, example) {
    return (0, common_1.applyDecorators)((0, swagger_1.ApiResponse)({
        status,
        description: message,
        schema: {
            example: example || {
                success: false,
                code: status,
                message,
                timestamp: new Date().toISOString(),
                requestId: 'uuid-example'
            }
        }
    }));
}
function CommonErrorResponses() {
    return (0, common_1.applyDecorators)(ErrorResponse(400, '请求参数错误', {
        success: false,
        code: 400,
        message: '请求参数错误',
        errors: ['参数验证失败'],
        errorType: 'VALIDATION',
        timestamp: new Date().toISOString(),
        requestId: 'uuid-example'
    }), ErrorResponse(401, '未授权访问', {
        success: false,
        code: 401,
        message: '未授权访问',
        errorType: 'AUTH',
        timestamp: new Date().toISOString(),
        requestId: 'uuid-example'
    }), ErrorResponse(403, '权限不足', {
        success: false,
        code: 403,
        message: '权限不足',
        errorType: 'AUTH',
        timestamp: new Date().toISOString(),
        requestId: 'uuid-example'
    }), ErrorResponse(404, '资源不存在', {
        success: false,
        code: 404,
        message: '资源不存在',
        errorType: 'BUSINESS',
        timestamp: new Date().toISOString(),
        requestId: 'uuid-example'
    }), ErrorResponse(500, '系统内部错误', {
        success: false,
        code: 500,
        message: '系统内部错误',
        errorType: 'SYSTEM',
        timestamp: new Date().toISOString(),
        requestId: 'uuid-example'
    }));
}
function ApiResponseDocs(config) {
    const decorators = [];
    if (config?.paginated) {
        decorators.push(PaginatedResponse(config.success?.message, config.success?.type));
    }
    else if (config?.simple) {
        decorators.push(SimpleResponse(config.success?.message));
    }
    else if (config?.restful) {
        decorators.push(RestfulResponse(config.success?.type));
    }
    else {
        decorators.push(SuccessResponse(config.success?.message, config.success?.example));
    }
    decorators.push(CommonErrorResponses());
    return (0, common_1.applyDecorators)(...decorators);
}
//# sourceMappingURL=response.decorator.js.map