import { UnifiedResponseOptions } from '../interceptors/unified-response.interceptor';
export declare const IP_LOCATION_RESPONSE_METADATA = "ip_location_response_metadata";
export interface IpLocationResponseConfig extends UnifiedResponseOptions {
    enableLocationCache?: boolean;
    locationCacheTTL?: number;
    enableRiskAssessment?: boolean;
    logLocationAccess?: boolean;
}
export declare function IpLocationResponse(config?: IpLocationResponseConfig): <TFunction extends Function, Y>(target: TFunction | object, propertyKey?: string | symbol, descriptor?: TypedPropertyDescriptor<Y>) => void;
export declare function IpQueryResponse(config?: Omit<IpLocationResponseConfig, 'action'>): <TFunction extends Function, Y>(target: TFunction | object, propertyKey?: string | symbol, descriptor?: TypedPropertyDescriptor<Y>) => void;
export declare function RiskAssessmentResponse(config?: Omit<IpLocationResponseConfig, 'action'>): <TFunction extends Function, Y>(target: TFunction | object, propertyKey?: string | symbol, descriptor?: TypedPropertyDescriptor<Y>) => void;
export declare function LocationStatsResponse(config?: Omit<IpLocationResponseConfig, 'action'>): <TFunction extends Function, Y>(target: TFunction | object, propertyKey?: string | symbol, descriptor?: TypedPropertyDescriptor<Y>) => void;
export declare function TrustLocationResponse(config?: Omit<IpLocationResponseConfig, 'action'>): <TFunction extends Function, Y>(target: TFunction | object, propertyKey?: string | symbol, descriptor?: TypedPropertyDescriptor<Y>) => void;
export declare function IpLocationHealthResponse(config?: Omit<IpLocationResponseConfig, 'action'>): <TFunction extends Function, Y>(target: TFunction | object, propertyKey?: string | symbol, descriptor?: TypedPropertyDescriptor<Y>) => void;
export declare function CurrentIpResponse(config?: Omit<IpLocationResponseConfig, 'action'>): <TFunction extends Function, Y>(target: TFunction | object, propertyKey?: string | symbol, descriptor?: TypedPropertyDescriptor<Y>) => void;
export declare function getIpLocationResponseConfig(target: any): IpLocationResponseConfig | undefined;
export declare function isIpLocationResponseEnabled(target: any): boolean;
export declare function mergeIpLocationResponseConfig(base: IpLocationResponseConfig, override: Partial<IpLocationResponseConfig>): IpLocationResponseConfig;
