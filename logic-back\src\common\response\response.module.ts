import { Module, Global } from '@nestjs/common';
import { APP_INTERCEPTOR, APP_FILTER } from '@nestjs/core';
import { LoggerModule } from '../logger/logger.module';
import { RedisModule } from '../../util/database/redis/redis.module';
import { ResponseManagerService } from './services/response-manager.service';
import { ResponseCacheService } from './services/response-cache.service';
import { ResponseInterceptor, GlobalResponseInterceptor } from './interceptors/response.interceptor';
import { CacheInterceptor, CacheClearInterceptor, CacheWarmupInterceptor } from './interceptors/cache.interceptor';
import { ResponseExceptionFilter, HttpExceptionFilter } from './filters/response-exception.filter';
import { CacheManagementController } from './controllers/cache-management.controller';

/**
 * 响应管理模块
 * 提供统一的响应格式化、错误处理和缓存功能
 */
@Global()
@Module({
  imports: [LoggerModule, RedisModule],
  controllers: [CacheManagementController],
  providers: [
    ResponseManagerService,
    ResponseCacheService,
    {
      provide: APP_INTERCEPTOR,
      useClass: GlobalResponseInterceptor,
    },
    {
      provide: APP_FILTER,
      useClass: ResponseExceptionFilter,
    },
  ],
  exports: [ResponseManagerService, ResponseCacheService],
})
export class ResponseModule {}

/**
 * 响应管理模块（带自定义拦截器）
 * 用于需要自定义响应处理和缓存的场景
 */
@Module({
  imports: [LoggerModule, RedisModule],
  providers: [
    ResponseManagerService,
    ResponseCacheService,
    ResponseInterceptor,
    CacheInterceptor,
    CacheClearInterceptor,
    CacheWarmupInterceptor,
    ResponseExceptionFilter,
    HttpExceptionFilter,
  ],
  exports: [
    ResponseManagerService,
    ResponseCacheService,
    ResponseInterceptor,
    CacheInterceptor,
    CacheClearInterceptor,
    CacheWarmupInterceptor,
    ResponseExceptionFilter,
    HttpExceptionFilter,
  ],
})
export class CustomResponseModule {}
