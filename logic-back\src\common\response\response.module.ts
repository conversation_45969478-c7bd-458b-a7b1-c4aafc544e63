import { Module, Global } from '@nestjs/common';
import { APP_INTERCEPTOR, APP_FILTER } from '@nestjs/core';
import { LoggerModule } from '../logger/logger.module';
import { ResponseManagerService } from './services/response-manager.service';
import { ResponseInterceptor, GlobalResponseInterceptor } from './interceptors/response.interceptor';
import { ResponseExceptionFilter, HttpExceptionFilter } from './filters/response-exception.filter';

/**
 * 响应管理模块
 * 提供统一的响应格式化和错误处理功能
 */
@Global()
@Module({
  imports: [LoggerModule],
  providers: [
    ResponseManagerService,
    {
      provide: APP_INTERCEPTOR,
      useClass: GlobalResponseInterceptor,
    },
    {
      provide: APP_FILTER,
      useClass: ResponseExceptionFilter,
    },
  ],
  exports: [ResponseManagerService],
})
export class ResponseModule {}

/**
 * 响应管理模块（带自定义拦截器）
 * 用于需要自定义响应处理的场景
 */
@Module({
  imports: [LoggerModule],
  providers: [
    ResponseManagerService,
    ResponseInterceptor,
    ResponseExceptionFilter,
    HttpExceptionFilter,
  ],
  exports: [
    ResponseManagerService,
    ResponseInterceptor,
    ResponseExceptionFilter,
    HttpExceptionFilter,
  ],
})
export class CustomResponseModule {}
