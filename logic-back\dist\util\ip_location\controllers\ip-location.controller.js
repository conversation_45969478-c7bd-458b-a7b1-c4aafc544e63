"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.IpLocationController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const response_1 = require("../../../common/response");
const ip_location_facade_service_1 = require("../application/services/ip-location-facade.service");
const ip_query_request_dto_1 = require("../application/dto/requests/ip-query.request.dto");
const risk_check_request_dto_1 = require("../application/dto/requests/risk-check.request.dto");
const trust_location_request_dto_1 = require("../application/dto/requests/trust-location.request.dto");
const location_info_response_dto_1 = require("../application/dto/responses/location-info.response.dto");
const risk_assessment_response_dto_1 = require("../application/dto/responses/risk-assessment.response.dto");
const location_stats_response_dto_1 = require("../application/dto/responses/location-stats.response.dto");
let IpLocationController = class IpLocationController {
    ipLocationFacadeService;
    responseManager;
    cacheService;
    constructor(ipLocationFacadeService, responseManager, cacheService) {
        this.ipLocationFacadeService = ipLocationFacadeService;
        this.responseManager = responseManager;
        this.cacheService = cacheService;
    }
    async queryIpLocationV2(query) {
        try {
            const result = await this.ipLocationFacadeService.getLocationByIP(query.ip, query.includeRisk);
            if (!result.success) {
                throw new response_1.BusinessException(result.error || 'IP地理位置查询失败', 'IP_QUERY_FAILED');
            }
            return {
                location: result.data,
                meta: {
                    executionTime: result.executionTime,
                    fromCache: result.fromCache,
                    ip: query.ip,
                    includeRisk: query.includeRisk || false,
                }
            };
        }
        catch (error) {
            if (error instanceof response_1.BusinessException) {
                throw error;
            }
            throw new response_1.BusinessException('IP地理位置查询服务异常', 'IP_SERVICE_ERROR');
        }
    }
    async checkLoginRisk(request) {
        try {
            if (!request.userId || !request.ipAddress) {
                throw new response_1.ValidationException('参数验证失败', [
                    !request.userId ? '用户ID不能为空' : '',
                    !request.ipAddress ? 'IP地址不能为空' : ''
                ].filter(Boolean));
            }
            const result = await this.ipLocationFacadeService.assessLoginRisk(request.userId, request.ipAddress, request.userAgent, request.sessionId);
            if (!result.success) {
                throw new response_1.BusinessException(result.error || '登录风险评估失败', 'RISK_ASSESSMENT_FAILED');
            }
            return {
                riskAssessment: result.data,
                meta: {
                    userId: request.userId,
                    ipAddress: request.ipAddress,
                    assessmentTime: new Date().toISOString(),
                }
            };
        }
        catch (error) {
            if (error instanceof response_1.BusinessException || error instanceof response_1.ValidationException) {
                throw error;
            }
            throw new response_1.BusinessException('风险评估服务异常', 'RISK_SERVICE_ERROR');
        }
    }
    async getUserLocationStats(userId, days = 30, includeTrusted = true) {
        try {
            if (!userId || userId <= 0) {
                throw new response_1.ValidationException('参数验证失败', ['用户ID必须是正整数']);
            }
            if (days && (days <= 0 || days > 365)) {
                throw new response_1.ValidationException('参数验证失败', ['统计天数必须在1-365之间']);
            }
            const result = await this.ipLocationFacadeService.getUserLocationStats(userId, days);
            if (!result.success) {
                throw new response_1.BusinessException(result.error || '获取用户位置统计失败', 'USER_STATS_FAILED');
            }
            return {
                statistics: result.data,
                meta: {
                    userId,
                    days,
                    includeTrusted,
                    generatedAt: new Date().toISOString(),
                }
            };
        }
        catch (error) {
            if (error instanceof response_1.BusinessException || error instanceof response_1.ValidationException) {
                throw error;
            }
            throw new response_1.BusinessException('用户位置统计服务异常', 'STATS_SERVICE_ERROR');
        }
    }
    async setTrustedLocation(userId, request) {
        try {
            const errors = [];
            if (!userId || userId <= 0) {
                errors.push('用户ID必须是正整数');
            }
            if (!request.province) {
                errors.push('省份不能为空');
            }
            if (!request.city) {
                errors.push('城市不能为空');
            }
            if (errors.length > 0) {
                throw new response_1.ValidationException('参数验证失败', errors);
            }
            const result = await this.ipLocationFacadeService.setTrustedLocation(userId, request.province, request.city, request.reason);
            if (!result.success) {
                throw new response_1.BusinessException(result.error || '设置可信位置失败', 'SET_TRUST_LOCATION_FAILED');
            }
            await this.clearUserLocationCache(userId);
            return {
                trustedLocation: result.data,
                meta: {
                    userId,
                    province: request.province,
                    city: request.city,
                    reason: request.reason,
                    setAt: new Date().toISOString(),
                }
            };
        }
        catch (error) {
            if (error instanceof response_1.BusinessException || error instanceof response_1.ValidationException) {
                throw error;
            }
            throw new response_1.BusinessException('设置可信位置服务异常', 'TRUST_LOCATION_SERVICE_ERROR');
        }
    }
    async getCurrentIpLocation(request) {
        try {
            const clientIp = this.extractClientIP(request);
            if (!clientIp) {
                throw new response_1.BusinessException('无法获取客户端IP地址', 'CLIENT_IP_NOT_FOUND');
            }
            const result = await this.ipLocationFacadeService.getLocationByIP(clientIp, false);
            if (!result.success) {
                throw new response_1.BusinessException(result.error || '获取当前IP位置失败', 'CURRENT_IP_QUERY_FAILED');
            }
            return {
                currentLocation: result.data,
                meta: {
                    clientIp,
                    requestTime: new Date().toISOString(),
                    userAgent: request.get('User-Agent'),
                }
            };
        }
        catch (error) {
            if (error instanceof response_1.BusinessException) {
                throw error;
            }
            throw new response_1.BusinessException('获取当前IP位置服务异常', 'CURRENT_IP_SERVICE_ERROR');
        }
    }
    async healthCheck() {
        try {
            const cacheStats = await this.cacheService.getStats();
            return {
                service: 'ip-location',
                status: 'UP',
                timestamp: new Date().toISOString(),
                version: '2.0.0',
                cache: {
                    enabled: true,
                    totalKeys: cacheStats.totalKeys,
                    totalSize: cacheStats.totalSize,
                },
                uptime: process.uptime(),
            };
        }
        catch (error) {
            return {
                service: 'ip-location',
                status: 'DEGRADED',
                timestamp: new Date().toISOString(),
                version: '2.0.0',
                error: 'Health check partially failed',
            };
        }
    }
    async clearCache(type = 'all', target) {
        try {
            let deletedCount = 0;
            let message = '';
            switch (type) {
                case 'user':
                    if (!target) {
                        throw new response_1.ValidationException('参数验证失败', ['清理用户缓存时必须提供用户ID']);
                    }
                    await this.clearUserLocationCache(parseInt(target));
                    message = `已清理用户 ${target} 的位置缓存`;
                    break;
                case 'ip':
                    if (!target) {
                        throw new response_1.ValidationException('参数验证失败', ['清理IP缓存时必须提供IP地址']);
                    }
                    await this.clearIpLocationCache(target);
                    message = `已清理IP ${target} 的位置缓存`;
                    break;
                case 'all':
                default:
                    deletedCount = await this.cacheService.deleteByPattern('*ip*location*');
                    message = `已清理所有IP地理位置缓存，共 ${deletedCount} 项`;
                    break;
            }
            return {
                type,
                target,
                deletedCount,
                message,
                clearedAt: new Date().toISOString(),
            };
        }
        catch (error) {
            if (error instanceof response_1.ValidationException) {
                throw error;
            }
            throw new response_1.BusinessException('缓存清理失败', 'CACHE_CLEAR_FAILED');
        }
    }
    extractClientIP(request) {
        const forwarded = request.headers['x-forwarded-for'];
        const realIp = request.headers['x-real-ip'];
        const cfConnectingIp = request.headers['cf-connecting-ip'];
        const xClientIp = request.headers['x-client-ip'];
        const xClusterClientIp = request.headers['x-cluster-client-ip'];
        const clientIp = request.socket?.remoteAddress ||
            request?.ip;
        console.log('🖥️ [Backend] IP地址提取详情:', {
            请求路径: request.url,
            请求方法: request.method,
            请求头IP信息: {
                'x-forwarded-for': forwarded,
                'x-real-ip': realIp,
                'cf-connecting-ip': cfConnectingIp,
                'x-client-ip': xClientIp,
                'x-cluster-client-ip': xClusterClientIp,
            },
            连接IP信息: {
                'socket.remoteAddress': request.socket?.remoteAddress,
                'request.ip': request?.ip,
                '最终连接IP': clientIp
            },
            时间戳: new Date().toISOString()
        });
        const ipSources = [
            forwarded?.split(',')[0]?.trim(),
            cfConnectingIp,
            realIp,
            xClientIp,
            xClusterClientIp,
            clientIp
        ];
        for (const ip of ipSources) {
            if (ip) {
                const cleanIp = this.cleanAndValidateIP(ip);
                if (cleanIp) {
                    console.log('✅ [Backend] IP地址提取成功:', {
                        原始IP: ip,
                        清理后IP: cleanIp,
                        来源: this.getIpSourceName(ip, {
                            forwarded: forwarded?.split(',')[0]?.trim(),
                            cfConnectingIp,
                            realIp,
                            xClientIp,
                            xClusterClientIp,
                            clientIp
                        }),
                        时间戳: new Date().toISOString()
                    });
                    return cleanIp;
                }
            }
        }
        console.log('⚠️ [Backend] 使用默认IP地址:', {
            原因: '所有IP源都无效',
            默认IP: '127.0.0.1',
            时间戳: new Date().toISOString()
        });
        return '127.0.0.1';
    }
    cleanAndValidateIP(ip) {
        if (!ip)
            return null;
        let cleanIp = ip.trim();
        cleanIp = cleanIp.replace('::ffff:', '');
        if (cleanIp === '::1') {
            return '127.0.0.1';
        }
        if (this.isValidIP(cleanIp)) {
            return cleanIp;
        }
        return null;
    }
    isValidIP(ip) {
        const ipv4Regex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
        const ipv6Regex = /^([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$|^::1$|^::$/;
        return ipv4Regex.test(ip) || ipv6Regex.test(ip);
    }
    getIpSourceName(ip, sources) {
        if (ip === sources.forwarded)
            return 'x-forwarded-for';
        if (ip === sources.cfConnectingIp)
            return 'cf-connecting-ip (Cloudflare)';
        if (ip === sources.realIp)
            return 'x-real-ip';
        if (ip === sources.xClientIp)
            return 'x-client-ip';
        if (ip === sources.xClusterClientIp)
            return 'x-cluster-client-ip';
        if (ip === sources.clientIp)
            return 'connection.remoteAddress';
        return '未知来源';
    }
    async clearUserLocationCache(userId) {
        try {
            await this.cacheService.deleteByPattern(`*user:${userId}*stats*`);
            await this.cacheService.deleteByPattern(`*user:${userId}*location*`);
            this.responseManager.logResponseCache('invalidate', `user_location_cache_${userId}`);
        }
        catch (error) {
            console.warn(`Failed to clear user location cache for user ${userId}:`, error);
        }
    }
    async clearIpLocationCache(ip) {
        try {
            await this.cacheService.deleteByPattern(`*${ip}*`);
            this.responseManager.logResponseCache('invalidate', `ip_location_cache_${ip}`);
        }
        catch (error) {
            console.warn(`Failed to clear IP location cache for IP ${ip}:`, error);
        }
    }
};
exports.IpLocationController = IpLocationController;
__decorate([
    (0, common_1.Get)('query'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({
        summary: '查询IP地理位置 (DDD架构)',
        description: '基于DDD架构的IP地理位置查询，性能更优，功能更强'
    }),
    (0, swagger_1.ApiQuery)({ name: 'ip', description: 'IP地址', example: '**************' }),
    (0, swagger_1.ApiQuery)({
        name: 'includeRisk',
        description: '是否包含风险评估',
        required: false,
        type: Boolean,
        example: false
    }),
    (0, response_1.ApiSuccessResponse)('IP地理位置查询成功'),
    (0, response_1.MediumCache)(300),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [ip_query_request_dto_1.IpQueryRequestDto]),
    __metadata("design:returntype", Promise)
], IpLocationController.prototype, "queryIpLocationV2", null);
__decorate([
    (0, common_1.Post)('check-risk'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({
        summary: '登录风险检查',
        description: '基于IP地址和用户历史进行登录风险评估'
    }),
    (0, response_1.ApiResponseDocs)({
        success: {
            message: '风险评估成功',
            type: risk_assessment_response_dto_1.RiskAssessmentResponseDto
        }
    }),
    (0, response_1.NoCache)(),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [risk_check_request_dto_1.RiskCheckRequestDto]),
    __metadata("design:returntype", Promise)
], IpLocationController.prototype, "checkLoginRisk", null);
__decorate([
    (0, common_1.Get)('user/:userId/stats'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({
        summary: '获取用户位置统计',
        description: '获取用户的常用登录地统计信息'
    }),
    (0, swagger_1.ApiParam)({ name: 'userId', description: '用户ID', type: Number }),
    (0, swagger_1.ApiQuery)({
        name: 'days',
        description: '统计天数',
        required: false,
        type: Number,
        example: 30
    }),
    (0, swagger_1.ApiQuery)({
        name: 'includeTrusted',
        description: '是否包含可信位置',
        required: false,
        type: Boolean,
        example: true
    }),
    (0, response_1.ApiResponseDocs)({
        success: {
            message: '获取用户位置统计成功',
            type: location_stats_response_dto_1.LocationStatsResponseDto
        }
    }),
    (0, response_1.UserCache)(600),
    __param(0, (0, common_1.Param)('userId')),
    __param(1, (0, common_1.Query)('days')),
    __param(2, (0, common_1.Query)('includeTrusted')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Number, Boolean]),
    __metadata("design:returntype", Promise)
], IpLocationController.prototype, "getUserLocationStats", null);
__decorate([
    (0, common_1.Post)('user/:userId/trust'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({
        summary: '设置可信登录地',
        description: '将指定位置设置为用户的可信登录地'
    }),
    (0, swagger_1.ApiParam)({ name: 'userId', description: '用户ID', type: Number }),
    (0, response_1.ApiSuccessResponse)('可信登录地设置成功'),
    (0, response_1.NoCache)(),
    __param(0, (0, common_1.Param)('userId')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, trust_location_request_dto_1.TrustLocationRequestDto]),
    __metadata("design:returntype", Promise)
], IpLocationController.prototype, "setTrustedLocation", null);
__decorate([
    (0, common_1.Get)('current'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({
        summary: '获取当前IP位置',
        description: '获取当前请求IP的地理位置信息'
    }),
    (0, response_1.ApiResponseDocs)({
        success: {
            message: '获取当前IP位置成功',
            type: location_info_response_dto_1.LocationInfoResponseDto
        }
    }),
    (0, response_1.MediumCache)(180),
    __param(0, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], IpLocationController.prototype, "getCurrentIpLocation", null);
__decorate([
    (0, common_1.Get)('health'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({
        summary: '健康检查',
        description: 'IP地理位置服务健康检查'
    }),
    (0, response_1.ApiSuccessResponse)('服务健康检查成功'),
    (0, response_1.NoCache)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], IpLocationController.prototype, "healthCheck", null);
__decorate([
    (0, common_1.Post)('cache/clear'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({
        summary: '清理缓存',
        description: '清理IP地理位置相关的缓存数据'
    }),
    (0, swagger_1.ApiQuery)({
        name: 'type',
        description: '缓存类型：all(全部), user(用户), ip(IP)',
        required: false,
        example: 'all'
    }),
    (0, swagger_1.ApiQuery)({
        name: 'target',
        description: '目标ID（用户ID或IP地址）',
        required: false
    }),
    (0, response_1.ApiSuccessResponse)('缓存清理成功'),
    (0, response_1.NoCache)(),
    __param(0, (0, common_1.Query)('type')),
    __param(1, (0, common_1.Query)('target')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], IpLocationController.prototype, "clearCache", null);
exports.IpLocationController = IpLocationController = __decorate([
    (0, swagger_1.ApiTags)('IP地理位置'),
    (0, swagger_1.ApiBearerAuth)('access-token'),
    (0, common_1.Controller)('api/v1/ip-location'),
    (0, common_1.UsePipes)(new common_1.ValidationPipe({
        transform: true,
        transformOptions: { enableImplicitConversion: true },
        whitelist: true,
        forbidNonWhitelisted: true
    })),
    __metadata("design:paramtypes", [ip_location_facade_service_1.IpLocationFacadeService,
        response_1.ResponseManagerService,
        response_1.ResponseCacheService])
], IpLocationController);
//# sourceMappingURL=ip-location.controller.js.map