import { LoggerService as NestLoggerService } from '@nestjs/common';
import { Logger } from 'winston';
export declare class LoggerService implements NestLoggerService {
    private readonly logger;
    constructor(logger: Logger);
    log(message: any, context?: string): void;
    error(message: any, trace?: string, context?: string): void;
    warn(message: any, context?: string): void;
    debug(message: any, context?: string): void;
    verbose(message: any, context?: string): void;
    logHttpRequest(req: any, res: any, responseTime: number): void;
    logDatabase(operation: string, table: string, data?: any, error?: any): void;
    logPayment(action: string, data: any, error?: any): void;
    logAuth(action: string, userId?: string, details?: any, error?: any): void;
    logBusiness(module: string, action: string, data?: any, error?: any): void;
    logStartup(message: string, details?: any): void;
    logPerformance(operation: string, duration: number, details?: any): void;
    logSecurity(event: string, details: any, severity?: 'low' | 'medium' | 'high'): void;
    logApiResponse(data: {
        requestId?: string;
        method: string;
        path: string;
        statusCode: number;
        success: boolean;
        message: string;
        executionTime?: number;
        userId?: string | number;
        ip?: string;
        userAgent?: string;
        requestBody?: any;
        responseData?: any;
        errors?: string[];
        errorType?: string;
        module?: string;
        operation?: string;
    }): void;
    logResponseProcessing(data: {
        requestId?: string;
        stage: 'interceptor' | 'filter' | 'manager';
        action: string;
        details?: any;
        duration?: number;
        error?: any;
    }): void;
    logResponseTransform(data: {
        requestId?: string;
        fromFormat: string;
        toFormat: string;
        originalData?: any;
        transformedData?: any;
        duration?: number;
    }): void;
    logResponseCache(data: {
        requestId?: string;
        action: 'hit' | 'miss' | 'set' | 'invalidate';
        cacheKey?: string;
        ttl?: number;
        size?: number;
    }): void;
    private sanitizeData;
}
