export interface ResponseLoggingConfig {
    enabled: boolean;
    logRequestBody: boolean;
    logResponseBody: boolean;
    logSensitiveData: boolean;
    performanceThreshold: number;
    logSuccessResponses: boolean;
    logErrorResponses: boolean;
    logProcessingSteps: boolean;
    logTransformations: boolean;
    logCacheOperations: boolean;
    excludePaths: string[];
    excludeUserAgents: string[];
    logLevels: {
        success: 'debug' | 'info' | 'warn' | 'error';
        error: 'debug' | 'info' | 'warn' | 'error';
        performance: 'debug' | 'info' | 'warn' | 'error';
        processing: 'debug' | 'info' | 'warn' | 'error';
        transformation: 'debug' | 'info' | 'warn' | 'error';
        cache: 'debug' | 'info' | 'warn' | 'error';
    };
}
export declare const defaultResponseLoggingConfig: ResponseLoggingConfig;
export declare const productionResponseLoggingConfig: ResponseLoggingConfig;
export declare const developmentResponseLoggingConfig: ResponseLoggingConfig;
export declare function getResponseLoggingConfig(): ResponseLoggingConfig;
export declare function shouldLogPath(path: string, config?: ResponseLoggingConfig): boolean;
export declare function shouldLogUserAgent(userAgent: string | undefined, config?: ResponseLoggingConfig): boolean;
export declare function shouldLogPerformance(executionTime: number, config?: ResponseLoggingConfig): boolean;
export declare function shouldLogRequestBody(config?: ResponseLoggingConfig): boolean;
export declare function shouldLogResponseBody(config?: ResponseLoggingConfig): boolean;
export declare function shouldLogSensitiveData(config?: ResponseLoggingConfig): boolean;
export declare function shouldLogProcessingSteps(config?: ResponseLoggingConfig): boolean;
export declare function shouldLogTransformations(config?: ResponseLoggingConfig): boolean;
export declare function shouldLogCacheOperations(config?: ResponseLoggingConfig): boolean;
export declare function getLogLevel(type: keyof ResponseLoggingConfig['logLevels'], config?: ResponseLoggingConfig): string;
export declare class ResponseLoggingUtils {
    private static config;
    static updateConfig(newConfig: Partial<ResponseLoggingConfig>): void;
    static getConfig(): ResponseLoggingConfig;
    static shouldLog(path: string, userAgent?: string): boolean;
    static getLogContext(request: any, response?: any): any;
    static sanitizeData(data: any): any;
    private static deepSanitize;
}
