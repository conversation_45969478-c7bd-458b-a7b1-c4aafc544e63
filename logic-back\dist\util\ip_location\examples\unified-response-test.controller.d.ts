import { IpLocationApplicationService } from '../application/services/ip-location-application.service';
import { IpLocationQueryService } from '../application/services/ip-location-query.service';
import { IpLocationCommandService } from '../application/services/ip-location-command.service';
export declare class IpLocationUnifiedResponseTestController {
    private readonly ipLocationService;
    private readonly queryService;
    private readonly commandService;
    private readonly responseManager;
    constructor(ipLocationService: IpLocationApplicationService, queryService: IpLocationQueryService, commandService: IpLocationCommandService, responseManager: any);
    autoQueryIp(ip?: string): Promise<import("../application/interfaces/query-handler.interface").QueryResult<import("../application/services/ip-location-query.service").LocationQueryResult>>;
    autoRiskCheck(request: {
        userId: string;
        ip: string;
    }): Promise<import("../application/interfaces/query-handler.interface").QueryResult<import("../application/services/ip-location-query.service").RiskAssessmentResult>>;
    decoratedQueryIp(ip?: string): Promise<import("../application/interfaces/query-handler.interface").QueryResult<import("../application/services/ip-location-query.service").LocationQueryResult>>;
    decoratedRiskCheck(request: {
        userId: string;
        ip: string;
    }): Promise<import("../application/interfaces/query-handler.interface").QueryResult<import("../application/services/ip-location-query.service").RiskAssessmentResult>>;
    decoratedUserStats(userId: string): Promise<import("../application/interfaces/query-handler.interface").QueryResult<import("../application/services/ip-location-query.service").UserLocationStatsResult>>;
    realtimeCurrentIp(): Promise<import("../application/interfaces/query-handler.interface").QueryResult<import("../application/services/ip-location-query.service").LocationQueryResult>>;
    getString(): string;
    getObject(): any;
    getArray(): any[];
    throwError(): never;
    getDddSuccess(): any;
    getDddError(): any;
    getHttpResponse(): any;
    getRawResponse(): any;
    manualProcess(request: {
        ip: string;
    }): Promise<any>;
    getPoolStatus(): Promise<any>;
    batchQueryIps(request: {
        ips: string[];
    }): Promise<{
        success: boolean;
        data: any[];
        message: string;
        timestamp: Date;
        executionTime: number;
    }>;
    delayedQuery(request: {
        ip: string;
        delay: number;
    }): Promise<{
        message: string;
        delayTime: number;
        success: boolean;
        data?: import("../application/services/ip-location-query.service").LocationQueryResult | undefined;
        errors?: string[];
        timestamp: Date;
        executionTime?: number;
        fromCache?: boolean;
        cacheKey?: string;
    }>;
}
