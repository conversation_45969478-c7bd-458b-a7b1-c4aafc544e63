import { ResponseManagerService } from '../services/response-manager.service';
export declare class ResponseExampleController {
    private readonly responseManager;
    constructor(responseManager: ResponseManagerService);
    basicSuccess(): Promise<{
        id: number;
        name: string;
        createTime: Date;
    }>;
    manualSuccess(): Promise<import("../interfaces/response.interface").SuccessResponse<{
        id: number;
        name: string;
        createTime: Date;
    }>>;
    paginatedData(page?: number, size?: number): Promise<{
        data: {
            id: number;
            name: string;
            createTime: Date;
        }[];
        pagination: {
            page: number;
            size: number;
            total: number;
        };
    }>;
    manualPaginated(page?: number, size?: number): Promise<import("../interfaces/response.interface").PaginatedResponse<{
        id: number;
        name: string;
        createTime: Date;
    }>>;
    simpleFormat(): Promise<{
        message: string;
        value: number;
    }>;
    restfulFormat(): Promise<{
        id: number;
        name: string;
        createTime: Date;
    }>;
    customFormat(): Promise<{
        customField: string;
        data: {
            id: number;
            name: string;
        };
    }>;
    businessError(): Promise<void>;
    validationError(body: any): Promise<import("../interfaces/response.interface").SuccessResponse<any>>;
    httpError(id: string): Promise<import("../interfaces/response.interface").SuccessResponse<{
        id: string;
    }>>;
    systemError(): Promise<void>;
    nullData(): Promise<null>;
    manualError(): Promise<import("../interfaces/response.interface").ErrorResponse<any>>;
    fullDocs(): Promise<{
        id: number;
        title: string;
        content: string;
        createTime: Date;
    }>;
    loggingExample(): Promise<{
        id: number;
        message: string;
        timestamp: Date;
        features: string[];
    }>;
    performanceTest(delay?: number): Promise<{
        message: string;
        delay: string;
        note: string;
    }>;
    sensitiveData(data: any): Promise<{
        message: string;
        note: string;
        processedFields: string[];
    }>;
}
