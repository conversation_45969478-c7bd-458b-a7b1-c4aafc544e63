import { ResponseManagerService } from '../services/response-manager.service';
import { ResponseCacheService } from '../services/response-cache.service';
export declare class ResponseExampleController {
    private readonly responseManager;
    private readonly cacheService;
    constructor(responseManager: ResponseManagerService, cacheService: ResponseCacheService);
    basicSuccess(): Promise<{
        id: number;
        name: string;
        createTime: Date;
    }>;
    manualSuccess(): Promise<import("../interfaces/response.interface").SuccessResponse<{
        id: number;
        name: string;
        createTime: Date;
    }>>;
    paginatedData(page?: number, size?: number): Promise<{
        data: {
            id: number;
            name: string;
            createTime: Date;
        }[];
        pagination: {
            page: number;
            size: number;
            total: number;
        };
    }>;
    manualPaginated(page?: number, size?: number): Promise<import("../interfaces/response.interface").PaginatedResponse<{
        id: number;
        name: string;
        createTime: Date;
    }>>;
    simpleFormat(): Promise<{
        message: string;
        value: number;
    }>;
    restfulFormat(): Promise<{
        id: number;
        name: string;
        createTime: Date;
    }>;
    customFormat(): Promise<{
        customField: string;
        data: {
            id: number;
            name: string;
        };
    }>;
    businessError(): Promise<void>;
    validationError(body: any): Promise<import("../interfaces/response.interface").SuccessResponse<any>>;
    httpError(id: string): Promise<import("../interfaces/response.interface").SuccessResponse<{
        id: string;
    }>>;
    systemError(): Promise<void>;
    nullData(): Promise<null>;
    manualError(): Promise<import("../interfaces/response.interface").ErrorResponse<any>>;
    fullDocs(): Promise<{
        id: number;
        title: string;
        content: string;
        createTime: Date;
    }>;
    loggingExample(): Promise<{
        id: number;
        message: string;
        timestamp: Date;
        features: string[];
    }>;
    performanceTest(delay?: number): Promise<{
        message: string;
        delay: string;
        note: string;
    }>;
    sensitiveData(data: any): Promise<{
        message: string;
        note: string;
        processedFields: string[];
    }>;
    shortCacheExample(): Promise<{
        message: string;
        timestamp: Date;
        cacheInfo: string;
        data: {
            randomValue: number;
            processedAt: string;
        };
    }>;
    mediumCacheExample(): Promise<{
        message: string;
        timestamp: Date;
        cacheInfo: string;
        data: {
            expensiveCalculation: number;
            processedAt: string;
        };
    }>;
    longCacheExample(): Promise<{
        message: string;
        timestamp: Date;
        cacheInfo: string;
        data: {
            staticData: string;
            processedAt: string;
        };
    }>;
    userCacheExample(): Promise<{
        message: string;
        note: string;
        timestamp: Date;
        userData: {
            preferences: string;
            lastLogin: string;
        };
    }>;
    paramCacheExample(category?: string): Promise<{
        message: string;
        note: string;
        category: string;
        timestamp: Date;
        data: {
            categoryData: string;
            processedAt: string;
        };
    }>;
    fullCacheExample(filter?: string): Promise<{
        message: string;
        note: string;
        filter: string;
        timestamp: Date;
        data: {
            filteredData: string;
            processedAt: string;
        };
    }>;
    staticCacheExample(): Promise<{
        message: string;
        note: string;
        timestamp: Date;
        staticData: {
            appConfig: {
                version: string;
                features: string[];
            };
            constants: {
                maxFileSize: string;
                supportedFormats: string[];
            };
        };
    }>;
    disabledCacheExample(): Promise<{
        message: string;
        note: string;
        timestamp: Date;
        realTimeData: {
            currentTime: string;
            randomValue: number;
        };
    }>;
    manualCacheExample(action?: string): Promise<{
        action: string;
        message: string;
        data: {
            message: string;
            timestamp: Date;
            value: number;
        };
        cached?: undefined;
        deleted?: undefined;
        stats?: undefined;
        currentAction?: undefined;
    } | {
        action: string;
        message: string;
        cached: boolean;
        data: import("../interfaces/response.interface").BaseResponse<any> | null;
        deleted?: undefined;
        stats?: undefined;
        currentAction?: undefined;
    } | {
        action: string;
        message: string;
        deleted: boolean;
        data?: undefined;
        cached?: undefined;
        stats?: undefined;
        currentAction?: undefined;
    } | {
        action: string;
        message: string;
        stats: {
            totalKeys: number;
            totalSize: number;
            avgTtl: number;
        };
        data?: undefined;
        cached?: undefined;
        deleted?: undefined;
        currentAction?: undefined;
    } | {
        message: string;
        currentAction: string;
        action?: undefined;
        data?: undefined;
        cached?: undefined;
        deleted?: undefined;
        stats?: undefined;
    }>;
}
