{"version": 3, "file": "unified-response-manager.service.js", "sourceRoot": "", "sources": ["../../../../src/common/unified-response-manager/services/unified-response-manager.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAAoD;AAepD,uGAAmH;AAGnH,gEAA4D;AAOrD,IAAM,6BAA6B,GAAnC,MAAM,6BAA6B;IAErB;IACwB;IACO;IACG;IACC;IALtD,YACmB,MAAqB,EACG,WAAyB,EAClB,kBAAuC,EACpC,YAAmC,EAClC,MAA6B;QAJhE,WAAM,GAAN,MAAM,CAAe;QACG,gBAAW,GAAX,WAAW,CAAc;QAClB,uBAAkB,GAAlB,kBAAkB,CAAqB;QACpC,iBAAY,GAAZ,YAAY,CAAuB;QAClC,WAAM,GAAN,MAAM,CAAuB;IAChF,CAAC;IAKJ,KAAK,CAAC,cAAc,CAClB,MAAW,EACX,OAAgC,EAChC,UAII,EAAE;QAEN,MAAM,WAAW,GAAG,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;QAGhD,IAAI,CAAC,kBAAkB,CAAC,eAAe,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;QAE/D,IAAI,CAAC;YACH,IAAI,eAAoB,CAAC;YAGzB,MAAM,OAAO,GAAG,OAAO,CAAC,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC;YAEjE,IAAI,OAAO,EAAE,CAAC;gBAEZ,eAAe,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,WAAW,EAAE,OAAO,CAAC,CAAC;YAC7E,CAAC;iBAAM,CAAC;gBAEN,eAAe,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;YACpE,CAAC;YAGD,MAAM,eAAe,GAAG,IAAI,CAAC,qBAAqB,CAChD,eAAe,EACf,WAAW,EACX,IAAI,CACL,CAAC;YAGF,IAAI,CAAC,WAAW,CAAC,eAAe,EAAE,MAAM,CAAC,CAAC;YAG1C,OAAO,IAAI,CAAC,cAAc,CAAC,eAAe,CAAoB,CAAC;QAEjE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YAEf,OAAO,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;QAC1C,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,WAAW,CACf,KAAU,EACV,OAAgC;QAEhC,MAAM,WAAW,GAAG,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;QAGhD,MAAM,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;QAG5D,MAAM,eAAe,GAA2B;YAC9C,OAAO,EAAE,WAAW;YACpB,IAAI,EAAE,SAAS;YACf,OAAO,EAAE,KAAK;YACd,OAAO,EAAE,SAAS,CAAC,OAAO;YAC1B,MAAM,EAAE,SAAS,CAAC,MAAM;YACxB,IAAI,EAAE,SAAS,CAAC,IAAI;YACpB,OAAO,EAAE,IAAI,CAAC,kBAAkB,CAAC,aAAa,CAAC,WAAW,CAAC,SAAS,CAAC;YACrE,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,YAAY,EAAE,OAAO;SACtB,CAAC;QAGF,IAAI,CAAC,WAAW,CAAC,eAAe,EAAE,OAAO,CAAC,CAAC;QAG3C,OAAO,IAAI,CAAC,cAAc,CAAC,eAAe,CAAC,CAAC;IAC9C,CAAC;IAKD,aAAa,CACX,OAAY,EACZ,UAAmB,EACnB,MAAe;QAEf,MAAM,SAAS,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAE3C,MAAM,OAAO,GAAmB;YAC9B,SAAS;YACT,MAAM,EAAE,OAAO,CAAC,IAAI,EAAE,EAAE,IAAI,OAAO,CAAC,MAAM;YAC1C,QAAQ,EAAE,OAAO,CAAC,EAAE,IAAI,OAAO,CAAC,UAAU,EAAE,aAAa;YACzD,SAAS,EAAE,OAAO,CAAC,OAAO,EAAE,CAAC,YAAY,CAAC;YAC1C,IAAI,EAAE,OAAO,CAAC,IAAI,IAAI,OAAO,CAAC,GAAG;YACjC,MAAM,EAAE,OAAO,CAAC,MAAM;YACtB,UAAU;YACV,MAAM;YACN,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;YACrB,QAAQ,EAAE;gBACR,OAAO,EAAE,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,OAAO,CAAC;gBAC9C,KAAK,EAAE,OAAO,CAAC,KAAK;gBACpB,MAAM,EAAE,OAAO,CAAC,MAAM;aACvB;SACF,CAAC;QAEF,OAAO,OAAO,CAAC;IACjB,CAAC;IAKD,KAAK,CAAC,aAAa;QACjB,OAAO,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE,CAAC;IACtC,CAAC;IAKD,WAAW,CACT,eAAmC,EACnC,QAAmC,MAAM;QAEzC,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,YAAY,EAAE,GAAG,eAAe,CAAC;QAErF,MAAM,OAAO,GAAG;YACd,SAAS,EAAE,OAAO,CAAC,SAAS;YAC5B,UAAU,EAAE,OAAO,CAAC,UAAU;YAC9B,MAAM,EAAE,OAAO,CAAC,MAAM;YACtB,MAAM,EAAE,OAAO,CAAC,MAAM;YACtB,OAAO;YACP,OAAO;YACP,MAAM;YACN,YAAY;YACZ,aAAa,EAAE,OAAO,CAAC,aAAa;YACpC,WAAW,EAAE,OAAO,CAAC,WAAW;YAChC,YAAY,EAAE,OAAO,CAAC,YAAY;YAClC,SAAS,EAAE,OAAO,CAAC,SAAS;YAC5B,WAAW,EAAE,OAAO,CAAC,WAAW;YAChC,aAAa,EAAE,OAAO,CAAC,aAAa;YACpC,IAAI,EAAE,OAAO,CAAC,IAAI;YAClB,MAAM,EAAE,OAAO,CAAC,MAAM;YACtB,QAAQ,EAAE,OAAO,CAAC,QAAQ;SAC3B,CAAC;QAEF,MAAM,UAAU,GAAG,IAAI,OAAO,CAAC,SAAS,KAAK,OAAO,CAAC,UAAU,IAAI,OAAO,CAAC,MAAM,MAAM,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,YAAY,GAAG,CAAC;QAE9I,QAAQ,KAAK,EAAE,CAAC;YACd,KAAK,MAAM;gBACT,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,UAAU,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC;gBACrD,MAAM;YACR,KAAK,MAAM;gBACT,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC;gBACtD,MAAM;YACR,KAAK,OAAO;gBACV,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,UAAU,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC;gBACvD,MAAM;QACV,CAAC;QAGD,IAAI,CAAC,MAAM,CAAC,WAAW,CACrB,OAAO,CAAC,UAAU,IAAI,SAAS,EAC/B,OAAO,CAAC,MAAM,IAAI,SAAS,EAC3B,OAAO,EACP,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,OAAO,CAAC,CAC/D,CAAC;IACJ,CAAC;IAKO,KAAK,CAAC,eAAe,CAC3B,MAAW,EACX,OAAuB,EACvB,OAAY;QAEZ,MAAM,IAAI,GAAgB;YACxB,SAAS,EAAE,OAAO,CAAC,SAAS;YAC5B,OAAO,EAAE,KAAK,IAAI,EAAE,CAAC,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,OAAO,CAAC;YAC1D,QAAQ,EAAE,OAAO,CAAC,QAAQ,IAAI,CAAC;YAC/B,OAAO,EAAE,OAAO,CAAC,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC,cAAc;YACtD,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;YACrB,OAAO;SACR,CAAC;QAEF,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAClC,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAC7D,MAAM,aAAa,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,cAAc,CAAC;QAGlD,IAAI,CAAC,kBAAkB,CAAC,mBAAmB,CAAC,OAAO,CAAC,SAAS,EAAE,aAAa,CAAC,CAAC;QAE9E,OAAO,eAAe,CAAC;IACzB,CAAC;IAKO,KAAK,CAAC,eAAe,CAAC,MAAW,EAAE,QAAwB;QAEjE,MAAM,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;QAG1D,QAAQ,YAAY,EAAE,CAAC;YACrB,KAAK,eAAe;gBAElB,OAAO,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;YAEjD,KAAK,YAAY;gBAEf,OAAO,MAAM,CAAC;YAEhB,KAAK,OAAO;gBAEV,MAAM,MAAM,CAAC;YAEf;gBAEE,OAAO,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;QACnD,CAAC;IACH,CAAC;IAKO,qBAAqB,CAC3B,eAAoB,EACpB,OAAuB,EACvB,OAAgB;QAEhB,MAAM,OAAO,GAAG,IAAI,CAAC,kBAAkB,CAAC,aAAa,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;QACzE,MAAM,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,eAAe,CAAC,CAAC;QAGnE,IAAI,YAAY,KAAK,YAAY,EAAE,CAAC;YAClC,OAAO;gBACL,OAAO;gBACP,IAAI,EAAE,eAAe,CAAC,IAAI;gBAC1B,OAAO,EAAE,eAAe,CAAC,OAAO;gBAChC,OAAO,EAAE,eAAe,CAAC,OAAO,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC;gBAC/E,MAAM,EAAE,eAAe,CAAC,MAAM;gBAC9B,IAAI,EAAE,eAAe,CAAC,OAAO,CAAC,CAAC,CAAC,sCAAY,CAAC,CAAC,CAAC,oCAAU;gBACzD,OAAO,EAAE;oBACP,GAAG,OAAO;oBACV,aAAa,EAAE,eAAe,CAAC,aAAa,IAAI,OAAO,CAAC,aAAa;iBACtE;gBACD,SAAS,EAAE,eAAe,CAAC,SAAS,IAAI,IAAI,IAAI,EAAE;gBAClD,YAAY;aACb,CAAC;QACJ,CAAC;QAGD,MAAM,eAAe,GAAG,IAAI,CAAC,YAAY,CAAC,gBAAgB,CAAC,eAAe,CAAC,CAAC;QAC5E,MAAM,eAAe,GAAG,IAAI,CAAC,YAAY,CAAC,kBAAkB,CAAC,eAAe,CAAC,CAAC;QAE9E,OAAO;YACL,OAAO;YACP,IAAI,EAAE,eAAe;YACrB,OAAO,EAAE,eAAe;YACxB,OAAO,EAAE,eAAe;YACxB,MAAM,EAAE,eAAe,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;YAC9C,IAAI,EAAE,eAAe,CAAC,CAAC,CAAC,sCAAY,CAAC,CAAC,CAAC,oCAAU;YACjD,OAAO;YACP,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,YAAY;SACb,CAAC;IACJ,CAAC;IAKO,cAAc,CAAI,eAAmC;QAC3D,OAAO;YACL,IAAI,EAAE,eAAe,CAAC,IAAI;YAC1B,GAAG,EAAE,eAAe,CAAC,OAAO,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC;YAC3E,IAAI,EAAE,eAAe,CAAC,IAAS;SAChC,CAAC;IACJ,CAAC;IAKO,iBAAiB;QACvB,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC7B,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC;QAChD,OAAO,OAAO,SAAS,IAAI,MAAM,EAAE,CAAC;IACtC,CAAC;IAKO,aAAa,CAAC,cAAuC;QAC3D,MAAM,SAAS,GAAG,cAAc,CAAC,SAAS,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAEvE,OAAO;YACL,SAAS;YACT,MAAM,EAAE,cAAc,CAAC,MAAM;YAC7B,QAAQ,EAAE,cAAc,CAAC,QAAQ;YACjC,SAAS,EAAE,cAAc,CAAC,SAAS;YACnC,IAAI,EAAE,cAAc,CAAC,IAAI;YACzB,MAAM,EAAE,cAAc,CAAC,MAAM;YAC7B,UAAU,EAAE,cAAc,CAAC,UAAU,IAAI,SAAS;YAClD,MAAM,EAAE,cAAc,CAAC,MAAM,IAAI,SAAS;YAC1C,SAAS,EAAE,cAAc,CAAC,SAAS,IAAI,IAAI,CAAC,GAAG,EAAE;YACjD,QAAQ,EAAE,cAAc,CAAC,QAAQ,IAAI,EAAE;SACxC,CAAC;IACJ,CAAC;IAKO,eAAe,CAAC,OAAY;QAClC,IAAI,CAAC,OAAO;YAAE,OAAO,EAAE,CAAC;QAExB,MAAM,SAAS,GAAG,EAAE,GAAG,OAAO,EAAE,CAAC;QAGjC,OAAO,SAAS,CAAC,aAAa,CAAC;QAC/B,OAAO,SAAS,CAAC,MAAM,CAAC;QACxB,OAAO,SAAS,CAAC,WAAW,CAAC,CAAC;QAE9B,OAAO,SAAS,CAAC;IACnB,CAAC;CACF,CAAA;AA/UY,sEAA6B;wCAA7B,6BAA6B;IADzC,IAAA,mBAAU,GAAE;IAIR,WAAA,IAAA,eAAM,EAAC,cAAc,CAAC,CAAA;IACtB,WAAA,IAAA,eAAM,EAAC,qBAAqB,CAAC,CAAA;IAC7B,WAAA,IAAA,eAAM,EAAC,wBAAwB,CAAC,CAAA;IAChC,WAAA,IAAA,eAAM,EAAC,yBAAyB,CAAC,CAAA;qCAJT,8BAAa;GAF7B,6BAA6B,CA+UzC"}