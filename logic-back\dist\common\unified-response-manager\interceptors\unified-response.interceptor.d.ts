import { NestInterceptor, ExecutionContext, CallHandler } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { Observable } from 'rxjs';
import { IUnifiedResponseManager } from '../interfaces/unified-response.interface';
export declare const UNIFIED_RESPONSE_METADATA = "unified_response_metadata";
export interface UnifiedResponseOptions {
    enabled?: boolean;
    usePool?: boolean;
    priority?: number;
    timeout?: number;
    controller?: string;
    action?: string;
}
export declare class UnifiedResponseInterceptor implements NestInterceptor {
    private readonly reflector;
    private readonly responseManager;
    constructor(reflector: Reflector, responseManager: IUnifiedResponseManager);
    intercept(context: ExecutionContext, next: CallHandler): Observable<any>;
}
export declare function UnifiedResponse(options?: UnifiedResponseOptions): (target: any, propertyKey: string, descriptor: PropertyDescriptor) => PropertyDescriptor;
export declare function HighPerformanceResponse(options?: Omit<UnifiedResponseOptions, 'usePool' | 'priority'>): (target: any, propertyKey: string, descriptor: PropertyDescriptor) => PropertyDescriptor;
export declare function RealtimeResponse(options?: Omit<UnifiedResponseOptions, 'usePool'>): (target: any, propertyKey: string, descriptor: PropertyDescriptor) => PropertyDescriptor;
export declare function BatchResponse(options?: Omit<UnifiedResponseOptions, 'usePool' | 'priority'>): (target: any, propertyKey: string, descriptor: PropertyDescriptor) => PropertyDescriptor;
export declare function LowPriorityResponse(options?: Omit<UnifiedResponseOptions, 'priority'>): (target: any, propertyKey: string, descriptor: PropertyDescriptor) => PropertyDescriptor;
export declare function HighPriorityResponse(options?: Omit<UnifiedResponseOptions, 'priority'>): (target: any, propertyKey: string, descriptor: PropertyDescriptor) => PropertyDescriptor;
export declare function DisableUnifiedResponse(): (target: any, propertyKey: string, descriptor: PropertyDescriptor) => PropertyDescriptor;
export declare function getUnifiedResponseOptions(target: any): UnifiedResponseOptions | undefined;
export declare function isUnifiedResponseEnabled(target: any): boolean;
