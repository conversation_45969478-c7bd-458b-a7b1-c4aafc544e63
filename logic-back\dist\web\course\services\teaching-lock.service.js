"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var TeachingLockService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.TeachingLockService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const distributed_lock_1 = require("../../../payment/lock/distributed.lock");
let TeachingLockService = TeachingLockService_1 = class TeachingLockService {
    distributedLock;
    configService;
    logger = new common_1.Logger(TeachingLockService_1.name);
    constructor(distributedLock, configService) {
        this.distributedLock = distributedLock;
        this.configService = configService;
    }
    async withDistributedLock(key, callback, ttl) {
        const defaultTtl = this.configService.get('teaching.lock.timeout', 60000);
        const lockTtl = ttl || defaultTtl;
        this.logger.log(`尝试获取分布式锁: ${key}, 超时时间: ${lockTtl}ms`);
        const startTime = Date.now();
        try {
            const result = await this.distributedLock.withLock(key, async () => {
                this.logger.log(`成功获取分布式锁: ${key}, 耗时: ${Date.now() - startTime}ms`);
                try {
                    const callbackResult = await callback();
                    this.logger.log(`锁内操作执行完成: ${key}, 总耗时: ${Date.now() - startTime}ms`);
                    return callbackResult;
                }
                catch (error) {
                    this.logger.error(`锁内操作执行失败: ${key}, 错误: ${error.message}`, error.stack);
                    throw error;
                }
            }, lockTtl);
            this.logger.log(`分布式锁已释放: ${key}, 总时间: ${Date.now() - startTime}ms`);
            return result;
        }
        catch (error) {
            this.logger.error(`分布式锁操作失败: ${key}, 错误: ${error.message}, 总时间: ${Date.now() - startTime}ms`, error.stack);
            throw error;
        }
    }
    getLockConfig() {
        return {
            timeout: this.configService.get('teaching.lock.timeout', 60000),
            maxRetries: this.configService.get('teaching.lock.maxRetries', 3),
            retryDelay: this.configService.get('teaching.lock.retryDelay', 1000),
        };
    }
};
exports.TeachingLockService = TeachingLockService;
exports.TeachingLockService = TeachingLockService = TeachingLockService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [distributed_lock_1.DistributedLock,
        config_1.ConfigService])
], TeachingLockService);
//# sourceMappingURL=teaching-lock.service.js.map