# 响应管理器快速开始指南

## 🚀 5分钟快速上手

### 1. 导入模块

在 `app.module.ts` 中添加：

```typescript
import { ResponseModule } from './common/response';

@Module({
  imports: [
    ResponseModule, // 添加这一行
    // ... 其他模块
  ],
})
export class AppModule {}
```

### 2. 基础使用

#### 最简单的方式 - 自动响应包装

```typescript
import { Controller, Get } from '@nestjs/common';
import { ApiSuccessResponse } from './common/response';

@Controller('api/users')
export class UserController {
  @Get()
  @ApiSuccessResponse('获取用户列表成功')
  async getUsers() {
    // 直接返回数据，会自动包装成标准响应格式
    return [
      { id: 1, name: '张三' },
      { id: 2, name: '李四' },
    ];
  }
}
```

**响应结果：**
```json
{
  "success": true,
  "code": 200,
  "message": "获取用户列表成功",
  "data": [
    { "id": 1, "name": "张三" },
    { "id": 2, "name": "李四" }
  ],
  "timestamp": "2024-01-01T00:00:00.000Z",
  "requestId": "req_1704067200000_abc123",
  "executionTime": 15
}
```

#### 分页响应

```typescript
@Get('paginated')
@ApiPaginatedResponse('获取分页用户列表成功')
async getPaginatedUsers(@Query('page') page: number = 1, @Query('size') size: number = 10) {
  const users = await this.userService.findPaginated(page, size);
  
  return {
    data: users.data,
    pagination: { page, size, total: users.total }
  };
}
```

#### 手动创建响应

```typescript
import { ResponseManagerService } from './common/response';

@Controller('api/users')
export class UserController {
  constructor(private readonly responseManager: ResponseManagerService) {}

  @Post()
  async createUser(@Body() userData: any) {
    const user = await this.userService.create(userData);
    return this.responseManager.success(user, '用户创建成功');
  }
}
```

### 3. 错误处理

#### 业务异常

```typescript
import { BusinessException } from './common/response';

@Get(':id')
async getUser(@Param('id') id: string) {
  const user = await this.userService.findById(id);
  
  if (!user) {
    throw new BusinessException('用户不存在', 'USER_NOT_FOUND');
  }
  
  return user;
}
```

#### 验证异常

```typescript
import { ValidationException } from './common/response';

@Post()
async createUser(@Body() userData: any) {
  const errors: string[] = [];
  
  if (!userData.name) {
    errors.push('姓名不能为空');
  }
  
  if (!userData.email) {
    errors.push('邮箱不能为空');
  }
  
  if (errors.length > 0) {
    throw new ValidationException('数据验证失败', errors);
  }
  
  return await this.userService.create(userData);
}
```

### 4. 不同响应格式

#### 简化格式（兼容现有前端）

```typescript
@Get('simple')
@ApiSimpleResponse('获取数据成功')
async getSimpleData() {
  return { id: 1, name: '张三' };
}
```

**响应结果：**
```json
{
  "code": 200,
  "msg": "获取数据成功",
  "data": { "id": 1, "name": "张三" }
}
```

#### RESTful格式

```typescript
@Get('restful')
@ApiRestfulResponse()
async getRestfulData() {
  return { id: 1, name: '张三' };
}
```

**响应结果：**
```json
{ "id": 1, "name": "张三" }
```

### 5. API文档自动生成

```typescript
import { ApiResponseDocs } from './common/response';

@Get()
@ApiOperation({ summary: '获取用户信息' })
@ApiResponseDocs({
  success: {
    message: '获取用户成功',
    example: { id: 1, name: '张三', email: '<EMAIL>' }
  }
})
async getUser() {
  return { id: 1, name: '张三', email: '<EMAIL>' };
}
```

## 🎯 常用装饰器速查

| 装饰器 | 用途 | 示例 |
|--------|------|------|
| `@ApiSuccessResponse()` | 成功响应 | `@ApiSuccessResponse('操作成功')` |
| `@ApiPaginatedResponse()` | 分页响应 | `@ApiPaginatedResponse('获取列表成功')` |
| `@ApiSimpleResponse()` | 简化格式 | `@ApiSimpleResponse('获取数据成功')` |
| `@ApiRestfulResponse()` | RESTful格式 | `@ApiRestfulResponse()` |
| `@ApiResponseDocs()` | 完整文档 | `@ApiResponseDocs({ success: {...} })` |

## 🔧 常用异常类

| 异常类 | 用途 | 示例 |
|--------|------|------|
| `BusinessException` | 业务异常 | `throw new BusinessException('用户不存在')` |
| `ValidationException` | 验证异常 | `throw new ValidationException('验证失败', errors)` |

## 📝 与现有系统兼容

如果你的项目已经使用了 `HttpResponseResultService`，可以逐步迁移：

```typescript
// 旧方式（继续可用）
return this.httpResponseResultService.success(data, '操作成功');

// 新方式（推荐）
@ApiSuccessResponse('操作成功')
async getData() {
  return data; // 自动包装
}
```

## 🚨 注意事项

1. **响应格式兼容性**：使用 `@ApiSimpleResponse` 保持与现有前端的兼容性
2. **错误处理**：新的错误格式可能需要前端适配
3. **性能**：响应管理器使用 REQUEST 作用域，每个请求独立实例

## 🔍 调试技巧

查看请求上下文：
```typescript
const context = this.responseManager.getContext();
console.log('Request context:', context);
```

## 📚 更多信息

- 完整文档：[README.md](./README.md)
- 集成指南：[INTEGRATION.md](./INTEGRATION.md)
- 使用示例：[response-example.controller.ts](./examples/response-example.controller.ts)

## 🎉 开始使用

现在你已经掌握了响应管理器的基础用法！开始在你的控制器中使用这些装饰器，享受统一、优雅的API响应格式吧！
