import { ExceptionFilter, ArgumentsHost, HttpException } from '@nestjs/common';
import { ResponseManagerService } from '../services/response-manager.service';
export declare class BusinessException extends Error {
    readonly errorCode?: string | undefined;
    readonly statusCode: number;
    constructor(message: string, errorCode?: string | undefined, statusCode?: number);
}
export declare class ValidationException extends Error {
    readonly errors: string[];
    readonly statusCode: number;
    constructor(message: string, errors?: string[], statusCode?: number);
}
export declare class ResponseExceptionFilter implements ExceptionFilter {
    private readonly responseManager;
    private readonly logger;
    constructor(responseManager: ResponseManagerService);
    catch(exception: unknown, host: ArgumentsHost): void;
    private createHttpErrorResponse;
    private logError;
    private generateRequestId;
}
export declare class HttpExceptionFilter implements ExceptionFilter {
    private readonly responseManager;
    private readonly logger;
    constructor(responseManager: ResponseManagerService);
    catch(exception: HttpException, host: ArgumentsHost): void;
    private generateRequestId;
}
