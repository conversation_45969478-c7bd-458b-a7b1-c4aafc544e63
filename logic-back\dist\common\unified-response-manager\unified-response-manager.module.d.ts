import { DynamicModule } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { UnifiedResponseConfig } from './interfaces/unified-response.interface';
export interface UnifiedResponseModuleOptions {
    isGlobal?: boolean;
    config?: Partial<UnifiedResponseConfig>;
    configFactory?: (configService: ConfigService) => UnifiedResponseConfig;
}
export declare class UnifiedResponseManagerModule {
    static forRoot(options?: UnifiedResponseModuleOptions): DynamicModule;
    static forRootAsync(options: {
        isGlobal?: boolean;
        imports?: any[];
        inject?: any[];
        useFactory: (...args: any[]) => Promise<UnifiedResponseConfig> | UnifiedResponseConfig;
    }): DynamicModule;
    static forRootWithEnv(options?: UnifiedResponseModuleOptions): DynamicModule;
}
export declare class UnifiedResponseManagerFeatureModule {
}
export declare class UnifiedResponseManagerTestModule {
}
