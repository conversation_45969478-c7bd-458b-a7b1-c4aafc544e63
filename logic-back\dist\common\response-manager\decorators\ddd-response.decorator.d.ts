import { ResponseFormat } from '../interfaces/response-manager.interface';
export declare const DDD_RESPONSE_METADATA = "ddd_response_metadata";
export interface DddResponseConfig {
    format?: ResponseFormat;
    module?: string;
    operation?: string;
    usePool?: boolean;
    enableMetrics?: boolean;
    enableDetailedLogging?: boolean;
    priority?: number;
    timeout?: number;
    enableCache?: boolean;
    cacheTTL?: number;
    cacheKeyPrefix?: string;
}
export declare function DddResponse(config?: DddResponseConfig): <TFunction extends Function, Y>(target: TFunction | object, propertyKey?: string | symbol, descriptor?: TypedPropertyDescriptor<Y>) => void;
export declare function DddCommandResponse(config?: Omit<DddResponseConfig, 'format'>): <TFunction extends Function, Y>(target: TFunction | object, propertyKey?: string | symbol, descriptor?: TypedPropertyDescriptor<Y>) => void;
export declare function DddQueryResponse(config?: Omit<DddResponseConfig, 'format'>): <TFunction extends Function, Y>(target: TFunction | object, propertyKey?: string | symbol, descriptor?: TypedPropertyDescriptor<Y>) => void;
export declare function DddEnhancedResponse(config?: Omit<DddResponseConfig, 'format'>): <TFunction extends Function, Y>(target: TFunction | object, propertyKey?: string | symbol, descriptor?: TypedPropertyDescriptor<Y>) => void;
export declare function DddHighPerformanceResponse(config?: Omit<DddResponseConfig, 'format' | 'usePool' | 'enableMetrics'>): <TFunction extends Function, Y>(target: TFunction | object, propertyKey?: string | symbol, descriptor?: TypedPropertyDescriptor<Y>) => void;
export declare function DddCachedResponse(config?: Omit<DddResponseConfig, 'enableCache'>): <TFunction extends Function, Y>(target: TFunction | object, propertyKey?: string | symbol, descriptor?: TypedPropertyDescriptor<Y>) => void;
export declare function DddBatchResponse(config?: Omit<DddResponseConfig, 'usePool' | 'priority'>): <TFunction extends Function, Y>(target: TFunction | object, propertyKey?: string | symbol, descriptor?: TypedPropertyDescriptor<Y>) => void;
export declare function DddRealtimeResponse(config?: Omit<DddResponseConfig, 'usePool' | 'priority' | 'timeout'>): <TFunction extends Function, Y>(target: TFunction | object, propertyKey?: string | symbol, descriptor?: TypedPropertyDescriptor<Y>) => void;
export declare function DddAsyncResponse(config?: Omit<DddResponseConfig, 'timeout'>): <TFunction extends Function, Y>(target: TFunction | object, propertyKey?: string | symbol, descriptor?: TypedPropertyDescriptor<Y>) => void;
export declare function getDddResponseConfig(target: any): DddResponseConfig | undefined;
export declare function isDddResponseEnabled(target: any): boolean;
export declare function mergeDddResponseConfig(base: DddResponseConfig, override: Partial<DddResponseConfig>): DddResponseConfig;
