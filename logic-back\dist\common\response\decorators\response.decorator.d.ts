import { ResponseFormat } from '../interfaces/response.interface';
export declare const RESPONSE_CONFIG_KEY = "response_config";
export interface ResponseDecoratorConfig {
    format?: ResponseFormat;
    includeExecutionTime?: boolean;
    includeRequestId?: boolean;
    successMessage?: string;
    swagger?: {
        description?: string;
        example?: any;
        type?: any;
    };
}
export declare function ResponseConfig(config?: ResponseDecoratorConfig): <TFunction extends Function, Y>(target: TFunction | object, propertyKey?: string | symbol, descriptor?: TypedPropertyDescriptor<Y>) => void;
export declare function ApiSuccessResponse(message?: string, example?: any): <TFunction extends Function, Y>(target: TFunction | object, propertyKey?: string | symbol, descriptor?: TypedPropertyDescriptor<Y>) => void;
export declare function ApiPaginatedResponse(message?: string, itemType?: any): <TFunction extends Function, Y>(target: TFunction | object, propertyKey?: string | symbol, descriptor?: TypedPropertyDescriptor<Y>) => void;
export declare function ApiSimpleResponse(message?: string): <TFunction extends Function, Y>(target: TFunction | object, propertyKey?: string | symbol, descriptor?: TypedPropertyDescriptor<Y>) => void;
export declare function ApiRestfulResponse(type?: any): <TFunction extends Function, Y>(target: TFunction | object, propertyKey?: string | symbol, descriptor?: TypedPropertyDescriptor<Y>) => void;
export declare function ApiErrorResponse(status: number, message: string, example?: any): <TFunction extends Function, Y>(target: TFunction | object, propertyKey?: string | symbol, descriptor?: TypedPropertyDescriptor<Y>) => void;
export declare function CommonErrorResponses(): <TFunction extends Function, Y>(target: TFunction | object, propertyKey?: string | symbol, descriptor?: TypedPropertyDescriptor<Y>) => void;
export declare function ApiResponseDocs(config?: {
    success?: {
        message?: string;
        example?: any;
        type?: any;
    };
    paginated?: boolean;
    simple?: boolean;
    restful?: boolean;
}): <TFunction extends Function, Y>(target: TFunction | object, propertyKey?: string | symbol, descriptor?: TypedPropertyDescriptor<Y>) => void;
