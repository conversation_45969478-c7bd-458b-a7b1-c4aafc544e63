"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.IntegrationExampleController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const unified_response_interceptor_1 = require("../interceptors/unified-response.interceptor");
let IntegrationExampleController = class IntegrationExampleController {
    responseManager;
    constructor(responseManager) {
        this.responseManager = responseManager;
    }
    getAutoString() {
        return 'Hello, 这是自动处理的字符串!';
    }
    getAutoObject() {
        return {
            id: 1,
            name: '张三',
            email: '<EMAIL>',
            createdAt: new Date()
        };
    }
    getAutoArray() {
        return [
            { id: 1, name: '用户1' },
            { id: 2, name: '用户2' },
            { id: 3, name: '用户3' }
        ];
    }
    getAutoError() {
        throw new Error('这是一个测试错误，会被自动处理');
    }
    async basicDecoratedOperation(data) {
        await new Promise(resolve => setTimeout(resolve, 100));
        return {
            message: '基础装饰器操作完成',
            input: data,
            processedAt: new Date()
        };
    }
    async highPerformanceOperation(data) {
        await new Promise(resolve => setTimeout(resolve, 200));
        return {
            message: '高性能操作完成',
            input: data,
            processedAt: new Date(),
            performance: 'optimized'
        };
    }
    async realtimeQuery(id) {
        return {
            id,
            status: 'active',
            timestamp: new Date(),
            realtime: true
        };
    }
    async batchProcess(items) {
        await new Promise(resolve => setTimeout(resolve, 500));
        return {
            message: `批量处理完成，共处理 ${items.length} 项`,
            processedCount: items.length,
            results: items.map((item, index) => ({
                index,
                item,
                processed: true,
                processedAt: new Date()
            }))
        };
    }
    async lowPriorityTask() {
        await new Promise(resolve => setTimeout(resolve, 300));
        return {
            message: '低优先级任务完成',
            priority: 'low',
            completedAt: new Date()
        };
    }
    async highPriorityTask(data) {
        return {
            message: '高优先级任务完成',
            priority: 'high',
            input: data,
            completedAt: new Date()
        };
    }
    async dddCommand(data) {
        return {
            success: true,
            data: {
                id: Date.now(),
                ...data
            },
            message: 'DDD命令执行成功',
            timestamp: new Date(),
            executionTime: 120
        };
    }
    async dddQuery(id) {
        return {
            success: true,
            data: {
                id,
                name: `实体${id}`,
                status: 'active'
            },
            message: 'DDD查询执行成功',
            timestamp: new Date(),
            executionTime: 80,
            fromCache: true,
            cacheKey: `entity:${id}`
        };
    }
    getRawResponse() {
        return {
            customFormat: true,
            message: '这是原始返回格式，不会被统一处理',
            timestamp: new Date(),
            note: '这个响应保持原始格式'
        };
    }
    async manualProcess(data) {
        const context = this.responseManager.createContext({ user: { id: 'manual_user' } }, 'Example', 'ManualProcess');
        try {
            const result = {
                message: '手动处理完成',
                data,
                processedAt: new Date()
            };
            return await this.responseManager.handleResponse(result, context, {
                usePool: true,
                priority: 6
            });
        }
        catch (error) {
            return await this.responseManager.handleError(error, context);
        }
    }
    async getPoolStatus() {
        return await this.responseManager.getPoolStatus();
    }
    getNull() {
        return null;
    }
    getUndefined() {
        return undefined;
    }
    getBoolean() {
        return true;
    }
    getNumber() {
        return 42;
    }
    async userRegistration(userData) {
        await new Promise(resolve => setTimeout(resolve, 150));
        return {
            userId: `user_${Date.now()}`,
            username: userData.username,
            email: userData.email,
            registeredAt: new Date(),
            status: 'active'
        };
    }
    async getOrderStatus(orderId) {
        return {
            orderId,
            status: 'processing',
            progress: 75,
            estimatedCompletion: new Date(Date.now() + 300000),
            lastUpdated: new Date()
        };
    }
    async dataExport(exportConfig) {
        await new Promise(resolve => setTimeout(resolve, 1000));
        return {
            exportId: `export_${Date.now()}`,
            config: exportConfig,
            status: 'completed',
            fileUrl: '/downloads/export_data.xlsx',
            recordCount: 1500,
            exportedAt: new Date()
        };
    }
};
exports.IntegrationExampleController = IntegrationExampleController;
__decorate([
    (0, common_1.Get)('auto/string'),
    (0, swagger_1.ApiOperation)({ summary: '自动处理字符串返回' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", String)
], IntegrationExampleController.prototype, "getAutoString", null);
__decorate([
    (0, common_1.Get)('auto/object'),
    (0, swagger_1.ApiOperation)({ summary: '自动处理对象返回' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Object)
], IntegrationExampleController.prototype, "getAutoObject", null);
__decorate([
    (0, common_1.Get)('auto/array'),
    (0, swagger_1.ApiOperation)({ summary: '自动处理数组返回' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Array)
], IntegrationExampleController.prototype, "getAutoArray", null);
__decorate([
    (0, common_1.Get)('auto/error'),
    (0, swagger_1.ApiOperation)({ summary: '自动处理错误' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], IntegrationExampleController.prototype, "getAutoError", null);
__decorate([
    (0, common_1.Post)('decorated/basic'),
    (0, swagger_1.ApiOperation)({ summary: '基础装饰器使用' }),
    (0, unified_response_interceptor_1.UnifiedResponse)({
        controller: 'Example',
        action: 'BasicOperation',
        usePool: true,
        priority: 5
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], IntegrationExampleController.prototype, "basicDecoratedOperation", null);
__decorate([
    (0, common_1.Post)('decorated/high-performance'),
    (0, swagger_1.ApiOperation)({ summary: '高性能装饰器使用' }),
    (0, unified_response_interceptor_1.HighPerformanceResponse)({
        controller: 'Example',
        action: 'HighPerformanceOperation'
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], IntegrationExampleController.prototype, "highPerformanceOperation", null);
__decorate([
    (0, common_1.Get)('decorated/realtime/:id'),
    (0, swagger_1.ApiOperation)({ summary: '实时响应装饰器使用' }),
    (0, unified_response_interceptor_1.RealtimeResponse)({
        controller: 'Example',
        action: 'RealtimeQuery'
    }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], IntegrationExampleController.prototype, "realtimeQuery", null);
__decorate([
    (0, common_1.Post)('decorated/batch'),
    (0, swagger_1.ApiOperation)({ summary: '批量处理装饰器使用' }),
    (0, unified_response_interceptor_1.BatchResponse)({
        controller: 'Example',
        action: 'BatchProcess'
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array]),
    __metadata("design:returntype", Promise)
], IntegrationExampleController.prototype, "batchProcess", null);
__decorate([
    (0, common_1.Get)('decorated/low-priority'),
    (0, swagger_1.ApiOperation)({ summary: '低优先级装饰器使用' }),
    (0, unified_response_interceptor_1.LowPriorityResponse)({
        controller: 'Example',
        action: 'LowPriorityTask'
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], IntegrationExampleController.prototype, "lowPriorityTask", null);
__decorate([
    (0, common_1.Post)('decorated/high-priority'),
    (0, swagger_1.ApiOperation)({ summary: '高优先级装饰器使用' }),
    (0, unified_response_interceptor_1.HighPriorityResponse)({
        controller: 'Example',
        action: 'HighPriorityTask'
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], IntegrationExampleController.prototype, "highPriorityTask", null);
__decorate([
    (0, common_1.Post)('ddd/command'),
    (0, swagger_1.ApiOperation)({ summary: 'DDD命令格式自动识别' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], IntegrationExampleController.prototype, "dddCommand", null);
__decorate([
    (0, common_1.Get)('ddd/query/:id'),
    (0, swagger_1.ApiOperation)({ summary: 'DDD查询格式自动识别' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], IntegrationExampleController.prototype, "dddQuery", null);
__decorate([
    (0, common_1.Get)('disabled/raw'),
    (0, swagger_1.ApiOperation)({ summary: '禁用统一响应处理' }),
    (0, unified_response_interceptor_1.DisableUnifiedResponse)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Object)
], IntegrationExampleController.prototype, "getRawResponse", null);
__decorate([
    (0, common_1.Post)('manual/process'),
    (0, swagger_1.ApiOperation)({ summary: '手动使用响应管理器' }),
    (0, unified_response_interceptor_1.DisableUnifiedResponse)(),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], IntegrationExampleController.prototype, "manualProcess", null);
__decorate([
    (0, common_1.Get)('monitor/pool-status'),
    (0, swagger_1.ApiOperation)({ summary: '获取请求池状态' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], IntegrationExampleController.prototype, "getPoolStatus", null);
__decorate([
    (0, common_1.Get)('types/null'),
    (0, swagger_1.ApiOperation)({ summary: '返回null' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], IntegrationExampleController.prototype, "getNull", null);
__decorate([
    (0, common_1.Get)('types/undefined'),
    (0, swagger_1.ApiOperation)({ summary: '返回undefined' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], IntegrationExampleController.prototype, "getUndefined", null);
__decorate([
    (0, common_1.Get)('types/boolean'),
    (0, swagger_1.ApiOperation)({ summary: '返回布尔值' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Boolean)
], IntegrationExampleController.prototype, "getBoolean", null);
__decorate([
    (0, common_1.Get)('types/number'),
    (0, swagger_1.ApiOperation)({ summary: '返回数字' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Number)
], IntegrationExampleController.prototype, "getNumber", null);
__decorate([
    (0, common_1.Post)('business/user-registration'),
    (0, swagger_1.ApiOperation)({ summary: '用户注册业务场景' }),
    (0, unified_response_interceptor_1.HighPriorityResponse)({
        controller: 'User',
        action: 'Register'
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], IntegrationExampleController.prototype, "userRegistration", null);
__decorate([
    (0, common_1.Get)('business/order-status/:orderId'),
    (0, swagger_1.ApiOperation)({ summary: '订单状态查询业务场景' }),
    (0, unified_response_interceptor_1.RealtimeResponse)({
        controller: 'Order',
        action: 'GetStatus'
    }),
    __param(0, (0, common_1.Param)('orderId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], IntegrationExampleController.prototype, "getOrderStatus", null);
__decorate([
    (0, common_1.Post)('business/data-export'),
    (0, swagger_1.ApiOperation)({ summary: '数据导出业务场景' }),
    (0, unified_response_interceptor_1.BatchResponse)({
        controller: 'Data',
        action: 'Export'
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], IntegrationExampleController.prototype, "dataExport", null);
exports.IntegrationExampleController = IntegrationExampleController = __decorate([
    (0, swagger_1.ApiTags)('统一响应管理器集成示例'),
    (0, common_1.Controller)('examples/integration'),
    __param(0, (0, common_1.Inject)('UNIFIED_RESPONSE_MANAGER')),
    __metadata("design:paramtypes", [Object])
], IntegrationExampleController);
//# sourceMappingURL=integration-example.controller.js.map