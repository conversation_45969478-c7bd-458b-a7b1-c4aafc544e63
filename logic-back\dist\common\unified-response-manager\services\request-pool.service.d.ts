import { OnM<PERSON><PERSON><PERSON><PERSON>roy } from '@nestjs/common';
import { IRequestPool, RequestTask, RequestPoolStatus, UnifiedResponseConfig } from '../interfaces/unified-response.interface';
import { RedisService } from '../../../util/database/redis/redis.service';
import { LoggerService } from '../../logger/logger.service';
import { PerformanceMonitorService } from './performance-monitor.service';
export declare class RequestPoolService implements IRequestPool, OnModuleDestroy {
    private readonly redisService;
    private readonly logger;
    private readonly performanceMonitor;
    private readonly config;
    private readonly processingTasks;
    private readonly completedTasks;
    private readonly maxHistorySize;
    private isProcessing;
    private processingInterval;
    constructor(redisService: RedisService, logger: LoggerService, performanceMonitor: PerformanceMonitorService, config: UnifiedResponseConfig);
    onModuleDestroy(): Promise<void>;
    enqueue<T>(task: RequestTask): Promise<T>;
    getStatus(): Promise<RequestPoolStatus>;
    cleanup(): Promise<void>;
    stop(): Promise<void>;
    private executeTask;
    private processTask;
    private addToQueue;
    private getNextTask;
    private getQueueSize;
    private waitForCompletion;
    private startProcessing;
    private cleanupExpiredQueueTasks;
    private calculateAverages;
}
