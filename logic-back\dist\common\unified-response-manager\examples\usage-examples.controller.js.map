{"version": 3, "file": "usage-examples.controller.js", "sourceRoot": "", "sources": ["../../../../src/common/unified-response-manager/examples/usage-examples.controller.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAKA,2CAAmF;AACnF,6CAAwD;AAGxD,+FAMsD;AAItD,iHAA2G;AAOpG,IAAM,gCAAgC,GAAtC,MAAM,gCAAgC;IAEY;IACpC;IAFnB,YACuD,eAAwC,EAC5E,mBAA8C;QADV,oBAAe,GAAf,eAAe,CAAyB;QAC5E,wBAAmB,GAAnB,mBAAmB,CAA2B;IAC9D,CAAC;IAOJ,SAAS;QACP,OAAO,eAAe,CAAC;IACzB,CAAC;IAOD,SAAS;QACP,OAAO,EAAE,CAAC;IACZ,CAAC;IAOD,UAAU;QACR,OAAO,IAAI,CAAC;IACd,CAAC;IAOD,SAAS;QACP,OAAO;YACL,EAAE,EAAE,CAAC;YACL,IAAI,EAAE,IAAI;YACV,KAAK,EAAE,sBAAsB;YAC7B,GAAG,EAAE,EAAE;YACP,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC;IACJ,CAAC;IAOD,QAAQ;QACN,OAAO;YACL,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE;YACrB,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE;YACrB,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE;SACtB,CAAC;IACJ,CAAC;IAOD,OAAO;QACL,OAAO,IAAI,CAAC;IACd,CAAC;IAOD,YAAY;QACV,OAAO,SAAS,CAAC;IACnB,CAAC;IAOD,eAAe;QACb,OAAO,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC;YACtC,EAAE,EAAE,CAAC;YACL,IAAI,EAAE,MAAM;SACb,EAAE,MAAM,CAAC,CAAC;IACb,CAAC;IAOD,gBAAgB,CAAgB,OAAe,CAAC,EAAiB,OAAe,EAAE;QAChF,MAAM,KAAK,GAAG,GAAG,CAAC;QAClB,MAAM,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC;YACvD,EAAE,EAAE,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,IAAI,GAAG,KAAK,GAAG,CAAC;YACjC,IAAI,EAAE,KAAK,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,IAAI,GAAG,KAAK,GAAG,CAAC,EAAE;YAC1C,KAAK,EAAE,OAAO,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,IAAI,GAAG,KAAK,GAAG,CAAC,cAAc;SAC1D,CAAC,CAAC,CAAC;QAEJ,OAAO;YACL,IAAI;YACJ,KAAK;YACL,IAAI;YACJ,QAAQ,EAAE,IAAI;YACd,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;SACpC,CAAC;IACJ,CAAC;IAOD,UAAU;QACR,MAAM,IAAI,KAAK,CAAC,UAAU,CAAC,CAAC;IAC9B,CAAC;IAOK,AAAN,KAAK,CAAC,YAAY;QAEhB,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC;QAEvD,OAAO;YACL,OAAO,EAAE,QAAQ;YACjB,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,IAAI,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE;SAC5B,CAAC;IACJ,CAAC;IAQK,AAAN,KAAK,CAAC,wBAAwB,CAAS,IAAS;QAE9C,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC;QAEvD,OAAO;YACL,OAAO,EAAE,SAAS;YAClB,KAAK,EAAE,IAAI;YACX,WAAW,EAAE,IAAI,IAAI,EAAE;SACxB,CAAC;IACJ,CAAC;IAQK,AAAN,KAAK,CAAC,iBAAiB,CAAS,IAAS;QACvC,OAAO;YACL,OAAO,EAAE,QAAQ;YACjB,KAAK,EAAE,IAAI;YACX,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC;IACJ,CAAC;IAQK,AAAN,KAAK,CAAC,cAAc,CAAS,KAAY;QAEvC,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC;QAEvD,OAAO;YACL,OAAO,EAAE,cAAc,KAAK,CAAC,MAAM,IAAI;YACvC,cAAc,EAAE,KAAK,CAAC,MAAM;YAC5B,OAAO,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC;gBACnC,KAAK;gBACL,IAAI;gBACJ,SAAS,EAAE,IAAI;aAChB,CAAC,CAAC;SACJ,CAAC;IACJ,CAAC;IAQD,mBAAmB;QAEjB,OAAO;YACL,YAAY,EAAE,IAAI;YAClB,OAAO,EAAE,UAAU;YACnB,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC;IACJ,CAAC;IAcK,AAAN,KAAK,CAAC,qBAAqB,CAAS,IAAS;QAE3C,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;QAExD,OAAO;YACL,OAAO,EAAE,WAAW;YACpB,MAAM,EAAE;gBACN,OAAO,EAAE,IAAI;gBACb,QAAQ,EAAE,CAAC;gBACX,OAAO,EAAE,KAAK;aACf;YACD,IAAI;SACL,CAAC;IACJ,CAAC;IAQK,AAAN,KAAK,CAAC,sBAAsB,CAAS,IAAS;QAE5C,MAAM,OAAO,GAAG,IAAI,CAAC,eAAe,CAAC,aAAa,CAChD,EAAE,IAAI,EAAE,EAAE,EAAE,EAAE,WAAW,EAAE,EAAS,EACpC,QAAQ,EACR,aAAa,CACd,CAAC;QAEF,IAAI,CAAC;YAEH,MAAM,MAAM,GAAG;gBACb,OAAO,EAAE,QAAQ;gBACjB,IAAI;gBACJ,WAAW,EAAE,IAAI,IAAI,EAAE;aACxB,CAAC;YAGF,OAAO,MAAM,IAAI,CAAC,eAAe,CAAC,cAAc,CAAC,MAAM,EAAE,OAAO,EAAE;gBAChE,OAAO,EAAE,IAAI;gBACb,QAAQ,EAAE,CAAC;aACZ,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YAEf,OAAO,MAAM,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;QAChE,CAAC;IACH,CAAC;IAOK,AAAN,KAAK,CAAC,aAAa;QACjB,OAAO,MAAM,IAAI,CAAC,eAAe,CAAC,aAAa,EAAE,CAAC;IACpD,CAAC;IAOK,AAAN,KAAK,CAAC,yBAAyB;QAE7B,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC;QACtD,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC;QACtD,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC;QAEtD,OAAO;YACL,OAAO,EAAE,SAAS;YAClB,eAAe,EAAE,CAAC;YAClB,SAAS,EAAE,OAAO;YAClB,OAAO,EAAE;gBACP,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,GAAG,EAAE;gBAC9B,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG,EAAE;gBAC/B,EAAE,KAAK,EAAE,UAAU,EAAE,KAAK,EAAE,EAAE,EAAE;aACjC;SACF,CAAC;IACJ,CAAC;IAOD,gBAAgB;QACd,OAAO;YACL,IAAI,EAAE;gBACJ,EAAE,EAAE,CAAC;gBACL,OAAO,EAAE;oBACP,IAAI,EAAE,IAAI;oBACV,OAAO,EAAE;wBACP,KAAK,EAAE,sBAAsB;wBAC7B,KAAK,EAAE,aAAa;wBACpB,OAAO,EAAE;4BACP,OAAO,EAAE,IAAI;4BACb,QAAQ,EAAE,KAAK;4BACf,IAAI,EAAE,KAAK;4BACX,QAAQ,EAAE,KAAK;4BACf,MAAM,EAAE,OAAO;yBAChB;qBACF;iBACF;gBACD,WAAW,EAAE;oBACX,KAAK,EAAE,MAAM;oBACb,QAAQ,EAAE,OAAO;oBACjB,aAAa,EAAE;wBACb,KAAK,EAAE,IAAI;wBACX,GAAG,EAAE,KAAK;wBACV,IAAI,EAAE,IAAI;qBACX;iBACF;gBACD,UAAU,EAAE;oBACV,UAAU,EAAE,GAAG;oBACf,WAAW,EAAE,IAAI,IAAI,EAAE;oBACvB,SAAS,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;oBACjC,SAAS,EAAE,IAAI,IAAI,EAAE;iBACtB;aACF;YACD,QAAQ,EAAE;gBACR,OAAO,EAAE,OAAO;gBAChB,UAAU,EAAE,IAAI;gBAChB,WAAW,EAAE,IAAI,IAAI,EAAE;gBACvB,UAAU,EAAE;oBACV,QAAQ,EAAE,eAAe;oBACzB,MAAM,EAAE,YAAY;oBACpB,WAAW,EAAE,YAAY;iBAC1B;aACF;SACF,CAAC;IACJ,CAAC;CACF,CAAA;AA5VY,4EAAgC;AAW3C;IAFC,IAAA,YAAG,EAAC,QAAQ,CAAC;IACb,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,SAAS,EAAE,CAAC;;;;iEAGpC;AAOD;IAFC,IAAA,YAAG,EAAC,QAAQ,CAAC;IACb,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,QAAQ,EAAE,CAAC;;;;iEAGnC;AAOD;IAFC,IAAA,YAAG,EAAC,SAAS,CAAC;IACd,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,SAAS,EAAE,CAAC;;;;kEAGpC;AAOD;IAFC,IAAA,YAAG,EAAC,QAAQ,CAAC;IACb,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,QAAQ,EAAE,CAAC;;;;iEASnC;AAOD;IAFC,IAAA,YAAG,EAAC,OAAO,CAAC;IACZ,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,QAAQ,EAAE,CAAC;;;;gEAOnC;AAOD;IAFC,IAAA,YAAG,EAAC,MAAM,CAAC;IACX,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,UAAU,EAAE,CAAC;;;;+DAGrC;AAOD;IAFC,IAAA,YAAG,EAAC,WAAW,CAAC;IAChB,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,eAAe,EAAE,CAAC;;;;oEAG1C;AAOD;IAFC,IAAA,YAAG,EAAC,eAAe,CAAC;IACpB,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,oBAAoB,EAAE,CAAC;;;;uEAM/C;AAOD;IAFC,IAAA,YAAG,EAAC,WAAW,CAAC;IAChB,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,UAAU,EAAE,CAAC;IACpB,WAAA,IAAA,cAAK,EAAC,MAAM,CAAC,CAAA;IAAoB,WAAA,IAAA,cAAK,EAAC,MAAM,CAAC,CAAA;;;;wEAe/D;AAOD;IAFC,IAAA,YAAG,EAAC,OAAO,CAAC;IACZ,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,QAAQ,EAAE,CAAC;;;;kEAGnC;AAOK;IAFL,IAAA,YAAG,EAAC,OAAO,CAAC;IACZ,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,QAAQ,EAAE,CAAC;;;;oEAUnC;AAQK;IAHL,IAAA,aAAI,EAAC,kBAAkB,CAAC;IACxB,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,SAAS,EAAE,CAAC;IACpC,IAAA,sDAAuB,GAAE;IACM,WAAA,IAAA,aAAI,GAAE,CAAA;;;;gFASrC;AAQK;IAHL,IAAA,aAAI,EAAC,UAAU,CAAC;IAChB,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,QAAQ,EAAE,CAAC;IACnC,IAAA,+CAAgB,GAAE;IACM,WAAA,IAAA,aAAI,GAAE,CAAA;;;;yEAM9B;AAQK;IAHL,IAAA,aAAI,EAAC,OAAO,CAAC;IACb,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,QAAQ,EAAE,CAAC;IACnC,IAAA,4CAAa,GAAE;IACM,WAAA,IAAA,aAAI,GAAE,CAAA;;;;sEAa3B;AAQD;IAHC,IAAA,YAAG,EAAC,UAAU,CAAC;IACf,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,UAAU,EAAE,CAAC;IACrC,IAAA,qDAAsB,GAAE;;;;2EAQxB;AAcK;IATL,IAAA,aAAI,EAAC,eAAe,CAAC;IACrB,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,SAAS,EAAE,CAAC;IACpC,IAAA,8CAAe,EAAC;QACf,OAAO,EAAE,IAAI;QACb,QAAQ,EAAE,CAAC;QACX,OAAO,EAAE,KAAK;QACd,UAAU,EAAE,eAAe;QAC3B,MAAM,EAAE,aAAa;KACtB,CAAC;IAC2B,WAAA,IAAA,aAAI,GAAE,CAAA;;;;6EAalC;AAQK;IAHL,IAAA,aAAI,EAAC,QAAQ,CAAC;IACd,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,aAAa,EAAE,CAAC;IACxC,IAAA,qDAAsB,GAAE;IACK,WAAA,IAAA,aAAI,GAAE,CAAA;;;;8EAyBnC;AAOK;IAFL,IAAA,YAAG,EAAC,aAAa,CAAC;IAClB,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,SAAS,EAAE,CAAC;;;;qEAGpC;AAOK;IAFL,IAAA,YAAG,EAAC,qBAAqB,CAAC;IAC1B,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,WAAW,EAAE,CAAC;;;;iFAiBtC;AAOD;IAFC,IAAA,YAAG,EAAC,gBAAgB,CAAC;IACrB,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,UAAU,EAAE,CAAC;;;;wEA8CrC;2CA3VU,gCAAgC;IAF5C,IAAA,iBAAO,EAAC,WAAW,CAAC;IACpB,IAAA,mBAAU,EAAC,2BAA2B,CAAC;IAGnC,WAAA,IAAA,eAAM,EAAC,0BAA0B,CAAC,CAAA;6CACG,wDAAyB;GAHtD,gCAAgC,CA4V5C"}