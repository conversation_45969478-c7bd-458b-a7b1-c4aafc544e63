{"version": 3, "file": "request-pool.service.js", "sourceRoot": "", "sources": ["../../../../src/common/unified-response-manager/services/request-pool.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAAqE;AAQrE,8EAA0E;AAC1E,gEAA4D;AAC5D,+EAA0E;AAOnE,IAAM,kBAAkB,GAAxB,MAAM,kBAAkB;IAQV;IACA;IACA;IACmC;IAVrC,eAAe,GAAG,IAAI,GAAG,EAAuB,CAAC;IACjD,cAAc,GAAG,IAAI,GAAG,EAA2D,CAAC;IACpF,cAAc,GAAG,IAAI,CAAC;IAC/B,YAAY,GAAG,KAAK,CAAC;IACrB,kBAAkB,GAA0B,IAAI,CAAC;IAEzD,YACmB,YAA0B,EAC1B,MAAqB,EACrB,kBAA6C,EACV,MAA6B;QAHhE,iBAAY,GAAZ,YAAY,CAAc;QAC1B,WAAM,GAAN,MAAM,CAAe;QACrB,uBAAkB,GAAlB,kBAAkB,CAA2B;QACV,WAAM,GAAN,MAAM,CAAuB;QAEjF,IAAI,CAAC,eAAe,EAAE,CAAC;IACzB,CAAC;IAED,KAAK,CAAC,eAAe;QACnB,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;IACpB,CAAC;IAKD,KAAK,CAAC,OAAO,CAAI,IAAiB;QAChC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,iBAAiB,EAAE,CAAC;YAEnC,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;QAChC,CAAC;QAGD,IAAI,IAAI,CAAC,eAAe,CAAC,IAAI,IAAI,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC;YAE5D,MAAM,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;YAG5B,OAAO,IAAI,CAAC,iBAAiB,CAAI,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;QACjE,CAAC;aAAM,CAAC;YAEN,OAAO,IAAI,CAAC,WAAW,CAAI,IAAI,CAAC,CAAC;QACnC,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,SAAS;QACb,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,YAAY,EAAE,CAAC;QAC5C,MAAM,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC;QAGlD,MAAM,EAAE,eAAe,EAAE,qBAAqB,EAAE,WAAW,EAAE,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAEzF,OAAO;YACL,SAAS;YACT,eAAe;YACf,cAAc,EAAE,IAAI,CAAC,MAAM,CAAC,cAAc;YAC1C,eAAe;YACf,qBAAqB;YACrB,WAAW;SACZ,CAAC;IACJ,CAAC;IAKD,KAAK,CAAC,OAAO;QACX,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACvB,MAAM,gBAAgB,GAAG,GAAG,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,cAAc,GAAG,CAAC,CAAC,CAAC;QAGhE,IAAI,gBAAgB,GAAG,CAAC,CAAC;QACzB,KAAK,MAAM,CAAC,SAAS,EAAE,EAAE,IAAI,EAAE,CAAC,IAAI,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,EAAE,CAAC;YAClE,IAAI,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,GAAG,gBAAgB,EAAE,CAAC;gBAC5D,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;gBACtC,gBAAgB,EAAE,CAAC;YACrB,CAAC;QACH,CAAC;QAGD,IAAI,iBAAiB,GAAG,CAAC,CAAC;QAC1B,KAAK,MAAM,CAAC,SAAS,EAAE,IAAI,CAAC,IAAI,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,EAAE,CAAC;YAC/D,IAAI,IAAI,CAAC,SAAS,GAAG,gBAAgB,EAAE,CAAC;gBACtC,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;gBACvC,iBAAiB,EAAE,CAAC;YACtB,CAAC;QACH,CAAC;QAGD,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,wBAAwB,EAAE,CAAC;QAG3D,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;YACnD,MAAM,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,CAAC;iBAC1D,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,IAAI,CAAC,CAAC,CAAC,CAAC;YAEjF,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC;YAC5B,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE,QAAQ,CAAC,EAAE,EAAE;gBACnE,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC;YACxC,CAAC,CAAC,CAAC;QACL,CAAC;QAED,IAAI,gBAAgB,GAAG,CAAC,IAAI,iBAAiB,GAAG,CAAC,IAAI,YAAY,GAAG,CAAC,EAAE,CAAC;YACtE,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,kBAAkB,gBAAgB,UAAU,iBAAiB,SAAS,YAAY,EAAE,EACpF,aAAa,CACd,CAAC;QACJ,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,IAAI;QACR,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;QAE1B,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC5B,aAAa,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;YACvC,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;QACjC,CAAC;QAGD,MAAM,WAAW,GAAG,KAAK,CAAC;QAC1B,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,OAAO,IAAI,CAAC,eAAe,CAAC,IAAI,GAAG,CAAC,IAAI,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,GAAG,WAAW,EAAE,CAAC;YAC7E,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC;QACzD,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,EAAE,aAAa,CAAC,CAAC;IAC3C,CAAC;IAKO,KAAK,CAAC,WAAW,CAAI,IAAiB;QAC5C,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC7B,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAE3B,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,IAAI,CAAC;gBAChC,IAAI,CAAC,OAAO,EAAE;gBACd,IAAI,OAAO,CAAC,CAAC,CAAC,EAAE,MAAM,EAAE,EAAE,CACxB,UAAU,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,CAC5D;aACF,CAAC,CAAC;YAEH,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAC9B,OAAO,MAAW,CAAC;QACrB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAC9B,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAKO,KAAK,CAAC,WAAW,CAAI,IAAiB;QAC5C,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;QAE/C,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,WAAW,CAAI,IAAI,CAAC,CAAC;YAG/C,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC,CAAC;YAE1D,OAAO,MAAM,CAAC;QAChB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YAEf,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,CAAC;YACvE,MAAM,KAAK,CAAC;QACd,CAAC;gBAAS,CAAC;YACT,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAC9C,CAAC;IACH,CAAC;IAKO,KAAK,CAAC,UAAU,CAAC,IAAiB;QACxC,MAAM,QAAQ,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,QAAQ,CAAC;QACvD,MAAM,QAAQ,GAAG;YACf,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,OAAO,EAAE,IAAI,CAAC,OAAO;SACtB,CAAC;QAGF,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,GAAG,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC;QACvD,MAAM,IAAI,CAAC,YAAY,CAAC,SAAS,EAAE,CAAC,IAAI,CAAC,QAAQ,EAAE,KAAK,EAAE,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC;IACtF,CAAC;IAKO,KAAK,CAAC,WAAW;QACvB,MAAM,QAAQ,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,QAAQ,CAAC;QAGvD,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,SAAS,EAAE,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAEzE,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACvB,OAAO,IAAI,CAAC;QACd,CAAC;QAGD,MAAM,IAAI,CAAC,YAAY,CAAC,SAAS,EAAE,CAAC,IAAI,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QAE7D,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9B,CAAC;IAKO,KAAK,CAAC,YAAY;QACxB,MAAM,QAAQ,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,QAAQ,CAAC;QACvD,OAAO,MAAM,IAAI,CAAC,YAAY,CAAC,SAAS,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;IAC7D,CAAC;IAKO,KAAK,CAAC,iBAAiB,CAAI,SAAiB,EAAE,OAAe;QACnE,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,MAAM,eAAe,GAAG,GAAG,EAAE;gBAC3B,MAAM,SAAS,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;gBAErD,IAAI,SAAS,EAAE,CAAC;oBACd,IAAI,SAAS,CAAC,KAAK,EAAE,CAAC;wBACpB,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;oBAC1B,CAAC;yBAAM,CAAC;wBACN,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;oBAC5B,CAAC;oBACD,OAAO;gBACT,CAAC;gBAGD,IAAI,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,GAAG,OAAO,EAAE,CAAC;oBACrC,MAAM,CAAC,IAAI,KAAK,CAAC,SAAS,SAAS,EAAE,CAAC,CAAC,CAAC;oBACxC,OAAO;gBACT,CAAC;gBAGD,UAAU,CAAC,eAAe,EAAE,GAAG,CAAC,CAAC;YACnC,CAAC,CAAC;YAEF,eAAe,EAAE,CAAC;QACpB,CAAC,CAAC,CAAC;IACL,CAAC;IAKO,eAAe;QACrB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QAEzB,IAAI,CAAC,kBAAkB,GAAG,WAAW,CAAC,KAAK,IAAI,EAAE;YAC/C,IAAI,CAAC,IAAI,CAAC,YAAY;gBAAE,OAAO;YAE/B,IAAI,CAAC;gBAEH,OAAO,IAAI,CAAC,eAAe,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC;oBAC9D,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,WAAW,EAAE,CAAC;oBAE1C,IAAI,CAAC,QAAQ;wBAAE,MAAM;oBAIrB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,QAAQ,CAAC,SAAS,cAAc,EAAE,aAAa,CAAC,CAAC;gBAC/E,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,cAAc,KAAK,CAAC,OAAO,EAAE,EAAE,aAAa,CAAC,CAAC;YAClE,CAAC;QACH,CAAC,EAAE,IAAI,CAAC,CAAC;IACX,CAAC;IAKO,KAAK,CAAC,wBAAwB;QACpC,MAAM,QAAQ,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,QAAQ,CAAC;QACvD,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACvB,MAAM,gBAAgB,GAAG,GAAG,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,cAAc,GAAG,CAAC,CAAC,CAAC;QAGhE,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,SAAS,EAAE,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAC1E,IAAI,YAAY,GAAG,CAAC,CAAC;QAErB,KAAK,MAAM,OAAO,IAAI,KAAK,EAAE,CAAC;YAC5B,IAAI,CAAC;gBACH,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;gBACrC,IAAI,QAAQ,CAAC,SAAS,GAAG,gBAAgB,EAAE,CAAC;oBAC1C,MAAM,IAAI,CAAC,YAAY,CAAC,SAAS,EAAE,CAAC,IAAI,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;oBAC5D,YAAY,EAAE,CAAC;gBACjB,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBAEf,MAAM,IAAI,CAAC,YAAY,CAAC,SAAS,EAAE,CAAC,IAAI,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;gBAC5D,YAAY,EAAE,CAAC;YACjB,CAAC;QACH,CAAC;QAED,OAAO,YAAY,CAAC;IACtB,CAAC;IAKO,iBAAiB;QACvB,MAAM,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC;aACzD,MAAM,CAAC,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC;aAChF,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC;QAEf,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC7B,OAAO,EAAE,eAAe,EAAE,CAAC,EAAE,qBAAqB,EAAE,CAAC,EAAE,WAAW,EAAE,GAAG,EAAE,CAAC;QAC5E,CAAC;QAED,MAAM,SAAS,GAAG,WAAW;aAC1B,MAAM,CAAC,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC;aACpC,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC,IAAI,CAAC,SAAU,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC;QAEvD,MAAM,eAAe,GAAG,WAAW;aAChC,MAAM,CAAC,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,WAAW,CAAC;aACxD,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC,IAAI,CAAC,WAAY,GAAG,IAAI,CAAC,SAAU,CAAC,CAAC;QAE1D,MAAM,YAAY,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;QAEtE,MAAM,eAAe,GAAG,SAAS,CAAC,MAAM,GAAG,CAAC;YAC1C,CAAC,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,CAAC,GAAG,GAAG,IAAI,EAAE,CAAC,CAAC,GAAG,SAAS,CAAC,MAAM;YACnE,CAAC,CAAC,CAAC,CAAC;QAEN,MAAM,qBAAqB,GAAG,eAAe,CAAC,MAAM,GAAG,CAAC;YACtD,CAAC,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,CAAC,GAAG,GAAG,IAAI,EAAE,CAAC,CAAC,GAAG,eAAe,CAAC,MAAM;YAC/E,CAAC,CAAC,CAAC,CAAC;QAEN,MAAM,WAAW,GAAG,CAAC,YAAY,GAAG,WAAW,CAAC,MAAM,CAAC,GAAG,GAAG,CAAC;QAE9D,OAAO,EAAE,eAAe,EAAE,qBAAqB,EAAE,WAAW,EAAE,CAAC;IACjE,CAAC;CACF,CAAA;AAvVY,gDAAkB;6BAAlB,kBAAkB;IAD9B,IAAA,mBAAU,GAAE;IAYR,WAAA,IAAA,eAAM,EAAC,yBAAyB,CAAC,CAAA;qCAHH,4BAAY;QAClB,8BAAa;QACD,uDAAyB;GAVrD,kBAAkB,CAuV9B"}