import { IUnifiedResponseManager } from '../interfaces/unified-response.interface';
export declare class IntegrationExampleController {
    private readonly responseManager;
    constructor(responseManager: IUnifiedResponseManager);
    getAutoString(): string;
    getAutoObject(): any;
    getAutoArray(): any[];
    getAutoError(): never;
    basicDecoratedOperation(data: any): Promise<any>;
    highPerformanceOperation(data: any): Promise<any>;
    realtimeQuery(id: string): Promise<any>;
    batchProcess(items: any[]): Promise<any>;
    lowPriorityTask(): Promise<any>;
    highPriorityTask(data: any): Promise<any>;
    dddCommand(data: any): Promise<any>;
    dddQuery(id: string): Promise<any>;
    getRawResponse(): any;
    manualProcess(data: any): Promise<any>;
    getPoolStatus(): Promise<import("../interfaces/unified-response.interface").RequestPoolStatus>;
    getNull(): null;
    getUndefined(): undefined;
    getBoolean(): boolean;
    getNumber(): number;
    userRegistration(userData: any): Promise<any>;
    getOrderStatus(orderId: string): Promise<any>;
    dataExport(exportConfig: any): Promise<any>;
}
