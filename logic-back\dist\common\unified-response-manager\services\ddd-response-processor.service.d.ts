import { CommandResult } from '../../../util/ip_location/application/interfaces/command-handler.interface';
import { QueryResult } from '../../../util/ip_location/application/interfaces/query-handler.interface';
import { UnifiedResponse, RequestContext, PerformanceMetrics } from '../interfaces/unified-response.interface';
export declare class DddResponseProcessorService {
    isCommandResult(result: any): result is CommandResult;
    isQueryResult(result: any): result is QueryResult<any>;
    processCommandResult<T>(commandResult: CommandResult<T>, context: RequestContext, metrics: PerformanceMetrics): UnifiedResponse<T>;
    processQueryResult<T>(queryResult: QueryResult<T>, context: RequestContext, metrics: PerformanceMetrics): UnifiedResponse<T>;
    processDddResult<T>(result: CommandResult<T> | QueryResult<T>, context: RequestContext, metrics: PerformanceMetrics): UnifiedResponse<T>;
    createDddSuccessResponse<T>(data: T, message: string, context: RequestContext, metrics: PerformanceMetrics, isQuery?: boolean): UnifiedResponse<T>;
    createDddErrorResponse(errors: string[], message: string, context: RequestContext, metrics: PerformanceMetrics, isQuery?: boolean): UnifiedResponse<never>;
    toDddCompatibleFormat<T>(unifiedResponse: UnifiedResponse<T>): any;
    enhanceDddResult<T>(originalResult: CommandResult<T> | QueryResult<T>, context: RequestContext, additionalMetrics?: Partial<PerformanceMetrics>): CommandResult<T> | QueryResult<T>;
    validateDddResult(result: any): {
        isValid: boolean;
        errors: string[];
    };
}
