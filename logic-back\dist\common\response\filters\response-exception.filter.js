"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var ResponseExceptionFilter_1, HttpExceptionFilter_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.HttpExceptionFilter = exports.ResponseExceptionFilter = exports.ValidationException = exports.BusinessException = void 0;
const common_1 = require("@nestjs/common");
const logger_service_1 = require("../../logger/logger.service");
const response_manager_service_1 = require("../services/response-manager.service");
const response_interface_1 = require("../interfaces/response.interface");
class BusinessException extends Error {
    errorCode;
    statusCode;
    constructor(message, errorCode, statusCode = response_interface_1.StatusCodes.BAD_REQUEST) {
        super(message);
        this.errorCode = errorCode;
        this.statusCode = statusCode;
        this.name = 'BusinessException';
    }
}
exports.BusinessException = BusinessException;
class ValidationException extends Error {
    errors;
    statusCode;
    constructor(message, errors = [], statusCode = response_interface_1.StatusCodes.VALIDATION_ERROR) {
        super(message);
        this.errors = errors;
        this.statusCode = statusCode;
        this.name = 'ValidationException';
    }
}
exports.ValidationException = ValidationException;
let ResponseExceptionFilter = ResponseExceptionFilter_1 = class ResponseExceptionFilter {
    responseManager;
    loggerService;
    logger = new common_1.Logger(ResponseExceptionFilter_1.name);
    constructor(responseManager, loggerService) {
        this.responseManager = responseManager;
        this.loggerService = loggerService;
    }
    catch(exception, host) {
        const ctx = host.switchToHttp();
        const response = ctx.getResponse();
        const request = ctx.getRequest();
        const startTime = Date.now();
        let requestId;
        if (!this.responseManager.getContext()) {
            requestId = this.generateRequestId();
            this.responseManager.setContext({
                requestId,
                startTime: Date.now(),
                path: request.path,
                method: request.method,
                ip: request.ip,
                userAgent: request.get('User-Agent'),
                userId: request.user?.id,
            });
        }
        else {
            requestId = this.responseManager.getContext()?.requestId || this.generateRequestId();
        }
        this.loggerService.logResponseProcessing({
            requestId,
            stage: 'filter',
            action: 'start_exception_handling',
            details: {
                exceptionType: exception?.constructor?.name || 'Unknown',
                path: request.path,
                method: request.method,
            }
        });
        let errorResponse;
        let httpStatus;
        let exceptionType;
        try {
            if (exception instanceof BusinessException) {
                exceptionType = 'BusinessException';
                errorResponse = this.responseManager.businessError(exception.message, exception.errorCode);
                httpStatus = exception.statusCode;
                this.loggerService.logResponseProcessing({
                    requestId,
                    stage: 'filter',
                    action: 'handle_business_exception',
                    details: {
                        errorCode: exception.errorCode,
                        statusCode: exception.statusCode,
                        message: exception.message
                    }
                });
            }
            else if (exception instanceof ValidationException) {
                exceptionType = 'ValidationException';
                errorResponse = this.responseManager.validationError(exception.errors, exception.message);
                httpStatus = exception.statusCode;
                this.loggerService.logResponseProcessing({
                    requestId,
                    stage: 'filter',
                    action: 'handle_validation_exception',
                    details: {
                        errorsCount: exception.errors?.length || 0,
                        statusCode: exception.statusCode,
                        message: exception.message
                    }
                });
            }
            else if (exception instanceof common_1.HttpException) {
                exceptionType = 'HttpException';
                httpStatus = exception.getStatus();
                const exceptionResponse = exception.getResponse();
                let message = exception.message;
                let errors = [];
                if (typeof exceptionResponse === 'object') {
                    const responseObj = exceptionResponse;
                    message = responseObj.message || exception.message;
                    if (Array.isArray(responseObj.message)) {
                        errors = responseObj.message;
                        message = '请求参数验证失败';
                    }
                }
                errorResponse = this.createHttpErrorResponse(httpStatus, message, errors);
                this.loggerService.logResponseProcessing({
                    requestId,
                    stage: 'filter',
                    action: 'handle_http_exception',
                    details: {
                        httpStatus,
                        message,
                        errorsCount: errors.length,
                        hasNestedResponse: typeof exceptionResponse === 'object'
                    }
                });
            }
            else if (exception instanceof Error) {
                exceptionType = 'Error';
                errorResponse = this.responseManager.systemError(exception.message, exception.stack);
                httpStatus = response_interface_1.StatusCodes.INTERNAL_SERVER_ERROR;
                this.loggerService.logResponseProcessing({
                    requestId,
                    stage: 'filter',
                    action: 'handle_system_error',
                    details: {
                        errorName: exception.name,
                        message: exception.message,
                        hasStack: !!exception.stack
                    }
                });
            }
            else {
                exceptionType = 'Unknown';
                errorResponse = this.responseManager.systemError('未知系统错误');
                httpStatus = response_interface_1.StatusCodes.INTERNAL_SERVER_ERROR;
                this.loggerService.logResponseProcessing({
                    requestId,
                    stage: 'filter',
                    action: 'handle_unknown_exception',
                    details: {
                        exceptionType: typeof exception,
                        exceptionValue: String(exception)
                    }
                });
            }
            this.logError(exception, request, errorResponse, exceptionType);
            this.loggerService.logResponseProcessing({
                requestId,
                stage: 'filter',
                action: 'complete_exception_handling',
                duration: Date.now() - startTime,
                details: {
                    finalHttpStatus: httpStatus,
                    errorResponseCode: errorResponse.code,
                    exceptionType
                }
            });
            response.status(httpStatus).json(errorResponse);
        }
        catch (filterError) {
            this.loggerService.logResponseProcessing({
                requestId,
                stage: 'filter',
                action: 'filter_processing_error',
                duration: Date.now() - startTime,
                error: filterError
            });
            response.status(500).json({
                success: false,
                code: 500,
                message: '系统内部错误',
                timestamp: new Date(),
                requestId
            });
        }
    }
    createHttpErrorResponse(status, message, errors = []) {
        switch (status) {
            case common_1.HttpStatus.BAD_REQUEST:
                return errors.length > 0
                    ? this.responseManager.validationError(errors, message)
                    : this.responseManager.businessError(message);
            case common_1.HttpStatus.UNAUTHORIZED:
                return this.responseManager.authError(message);
            case common_1.HttpStatus.FORBIDDEN:
                return this.responseManager.forbiddenError(message);
            case common_1.HttpStatus.NOT_FOUND:
                return this.responseManager.notFoundError(message);
            default:
                return this.responseManager.systemError(message);
        }
    }
    logError(exception, request, errorResponse, exceptionType) {
        const context = this.responseManager.getContext();
        this.loggerService.logApiResponse({
            requestId: context?.requestId,
            method: request.method,
            path: request.path,
            statusCode: errorResponse.code,
            success: false,
            message: errorResponse.message,
            userId: request.user?.id,
            ip: request.ip,
            userAgent: request.get('User-Agent'),
            requestBody: this.sanitizeRequestBody(request.body),
            errors: errorResponse.errors,
            errorType: errorResponse.errorType,
        });
        const logData = {
            requestId: context?.requestId,
            method: request.method,
            url: request.url,
            ip: request.ip,
            userAgent: request.get('User-Agent'),
            userId: request.user?.id,
            errorCode: errorResponse.code,
            errorMessage: errorResponse.message,
            errorType: errorResponse.errorType,
            exceptionType,
            timestamp: new Date().toISOString(),
        };
        if (exception instanceof Error) {
            this.loggerService.logBusiness('ExceptionFilter', 'handle_exception', {
                ...logData,
                exceptionName: exception.name,
                exceptionMessage: exception.message,
            }, exception);
            this.logger.error(`${request.method} ${request.url} - ${exception.message}`, {
                ...logData,
                stack: exception.stack,
            });
        }
        else {
            this.loggerService.logBusiness('ExceptionFilter', 'handle_unknown_exception', {
                ...logData,
                exceptionValue: String(exception),
            });
            this.logger.error(`${request.method} ${request.url} - Unknown error`, logData);
        }
        if (errorResponse.code === 401 || errorResponse.code === 403) {
            this.loggerService.logSecurity('unauthorized_access_attempt', {
                requestId: context?.requestId,
                method: request.method,
                path: request.path,
                ip: request.ip,
                userAgent: request.get('User-Agent'),
                userId: request.user?.id,
                errorCode: errorResponse.code,
                errorMessage: errorResponse.message,
            }, errorResponse.code === 403 ? 'high' : 'medium');
        }
        if (process.env.NODE_ENV === 'development') {
            console.error('Exception Details:', {
                exception,
                request: {
                    method: request.method,
                    url: request.url,
                    headers: this.sanitizeHeaders(request.headers),
                    body: this.sanitizeRequestBody(request.body),
                },
                response: errorResponse,
                context: context,
            });
        }
    }
    sanitizeRequestBody(body) {
        if (!body || typeof body !== 'object') {
            return body;
        }
        const sensitiveFields = ['password', 'token', 'secret', 'key', 'authorization'];
        const sanitized = { ...body };
        for (const field of sensitiveFields) {
            if (field in sanitized) {
                sanitized[field] = '***REDACTED***';
            }
        }
        return sanitized;
    }
    sanitizeHeaders(headers) {
        if (!headers || typeof headers !== 'object') {
            return headers;
        }
        const sensitiveHeaders = ['authorization', 'cookie', 'x-api-key', 'x-auth-token'];
        const sanitized = { ...headers };
        for (const header of sensitiveHeaders) {
            if (header in sanitized) {
                sanitized[header] = '***REDACTED***';
            }
        }
        return sanitized;
    }
    generateRequestId() {
        return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
};
exports.ResponseExceptionFilter = ResponseExceptionFilter;
exports.ResponseExceptionFilter = ResponseExceptionFilter = ResponseExceptionFilter_1 = __decorate([
    (0, common_1.Catch)(),
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [response_manager_service_1.ResponseManagerService,
        logger_service_1.LoggerService])
], ResponseExceptionFilter);
let HttpExceptionFilter = HttpExceptionFilter_1 = class HttpExceptionFilter {
    responseManager;
    logger = new common_1.Logger(HttpExceptionFilter_1.name);
    constructor(responseManager) {
        this.responseManager = responseManager;
    }
    catch(exception, host) {
        const ctx = host.switchToHttp();
        const response = ctx.getResponse();
        const request = ctx.getRequest();
        const status = exception.getStatus();
        const exceptionResponse = exception.getResponse();
        let message = exception.message;
        let errors = [];
        if (typeof exceptionResponse === 'object') {
            const responseObj = exceptionResponse;
            message = responseObj.message || exception.message;
            if (Array.isArray(responseObj.message)) {
                errors = responseObj.message;
                message = '请求参数验证失败';
            }
        }
        if (!this.responseManager.getContext()) {
            this.responseManager.setContext({
                requestId: this.generateRequestId(),
                startTime: Date.now(),
                path: request.path,
                method: request.method,
                ip: request.ip,
                userAgent: request.get('User-Agent'),
                userId: request.user?.id,
            });
        }
        let errorResponse;
        switch (status) {
            case common_1.HttpStatus.BAD_REQUEST:
                errorResponse = errors.length > 0
                    ? this.responseManager.validationError(errors, message)
                    : this.responseManager.businessError(message);
                break;
            case common_1.HttpStatus.UNAUTHORIZED:
                errorResponse = this.responseManager.authError(message);
                break;
            case common_1.HttpStatus.FORBIDDEN:
                errorResponse = this.responseManager.forbiddenError(message);
                break;
            case common_1.HttpStatus.NOT_FOUND:
                errorResponse = this.responseManager.notFoundError(message);
                break;
            default:
                errorResponse = this.responseManager.systemError(message);
                break;
        }
        this.logger.warn(`HTTP Exception: ${request.method} ${request.url} - ${status} ${message}`, {
            requestId: this.responseManager.getContext()?.requestId,
            status,
            message,
            errors,
        });
        response.status(status).json(errorResponse);
    }
    generateRequestId() {
        return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
};
exports.HttpExceptionFilter = HttpExceptionFilter;
exports.HttpExceptionFilter = HttpExceptionFilter = HttpExceptionFilter_1 = __decorate([
    (0, common_1.Catch)(common_1.HttpException),
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [response_manager_service_1.ResponseManagerService])
], HttpExceptionFilter);
//# sourceMappingURL=response-exception.filter.js.map