"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DDD_RESPONSE_METADATA = void 0;
exports.DddResponse = DddResponse;
exports.DddCommandResponse = DddCommandResponse;
exports.DddQueryResponse = DddQueryResponse;
exports.DddEnhancedResponse = DddEnhancedResponse;
exports.DddHighPerformanceResponse = DddHighPerformanceResponse;
exports.DddCachedResponse = DddCachedResponse;
exports.DddBatchResponse = DddBatchResponse;
exports.DddRealtimeResponse = DddRealtimeResponse;
exports.DddAsyncResponse = DddAsyncResponse;
exports.getDddResponseConfig = getDddResponseConfig;
exports.isDddResponseEnabled = isDddResponseEnabled;
exports.mergeDddResponseConfig = mergeDddResponseConfig;
const common_1 = require("@nestjs/common");
const common_2 = require("@nestjs/common");
const response_manager_interface_1 = require("../interfaces/response-manager.interface");
const ddd_response_interceptor_1 = require("../interceptors/ddd-response.interceptor");
exports.DDD_RESPONSE_METADATA = 'ddd_response_metadata';
function DddResponse(config = {}) {
    return (0, common_1.applyDecorators)((0, common_1.SetMetadata)(exports.DDD_RESPONSE_METADATA, config), (0, common_2.UseInterceptors)(ddd_response_interceptor_1.DddResponseInterceptor));
}
function DddCommandResponse(config = {}) {
    return DddResponse({
        ...config,
        format: response_manager_interface_1.ResponseFormat.COMMAND
    });
}
function DddQueryResponse(config = {}) {
    return DddResponse({
        ...config,
        format: response_manager_interface_1.ResponseFormat.QUERY
    });
}
function DddEnhancedResponse(config = {}) {
    return DddResponse({
        ...config,
        format: response_manager_interface_1.ResponseFormat.ENHANCED,
        enableMetrics: true,
        enableDetailedLogging: true
    });
}
function DddHighPerformanceResponse(config = {}) {
    return DddResponse({
        ...config,
        format: response_manager_interface_1.ResponseFormat.ENHANCED,
        usePool: true,
        enableMetrics: true,
        priority: config.priority || 1
    });
}
function DddCachedResponse(config = {}) {
    return DddResponse({
        ...config,
        enableCache: true,
        cacheTTL: config.cacheTTL || 300,
        cacheKeyPrefix: config.cacheKeyPrefix || 'ddd_cache'
    });
}
function DddBatchResponse(config = {}) {
    return DddResponse({
        ...config,
        usePool: true,
        priority: -1,
        timeout: config.timeout || 30000,
        enableMetrics: true
    });
}
function DddRealtimeResponse(config = {}) {
    return DddResponse({
        ...config,
        usePool: false,
        priority: 10,
        timeout: 5000,
        enableMetrics: true
    });
}
function DddAsyncResponse(config = {}) {
    return DddResponse({
        ...config,
        timeout: 300000,
        usePool: true,
        priority: config.priority || 0,
        enableMetrics: true,
        enableDetailedLogging: true
    });
}
function getDddResponseConfig(target) {
    return Reflect.getMetadata(exports.DDD_RESPONSE_METADATA, target);
}
function isDddResponseEnabled(target) {
    return !!Reflect.getMetadata(exports.DDD_RESPONSE_METADATA, target);
}
function mergeDddResponseConfig(base, override) {
    return {
        ...base,
        ...override
    };
}
//# sourceMappingURL=ddd-response.decorator.js.map