import { DynamicModule } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { ResponseManagerConfig } from './interfaces/response-manager.interface';
export interface ResponseManagerModuleOptions {
    isGlobal?: boolean;
    config?: Partial<ResponseManagerConfig>;
    configFactory?: (configService: ConfigService) => ResponseManagerConfig;
}
export declare class ResponseManagerModule {
    static forRoot(options?: ResponseManagerModuleOptions): DynamicModule;
    static forRootAsync(options: {
        isGlobal?: boolean;
        imports?: any[];
        inject?: any[];
        useFactory: (...args: any[]) => Promise<ResponseManagerConfig> | ResponseManagerConfig;
    }): DynamicModule;
    static forRootWithEnv(options?: ResponseManagerModuleOptions): DynamicModule;
}
export declare class ResponseManagerFeatureModule {
}
export declare class ResponseManagerTestModule {
}
