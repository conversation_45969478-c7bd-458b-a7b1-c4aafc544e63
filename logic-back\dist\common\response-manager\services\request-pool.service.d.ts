import { IRequestPool, RequestPoolStatus, ResponseManagerConfig } from '../interfaces/response-manager.interface';
import { LoggerService } from '../../logger/logger.service';
export declare class RequestPoolService implements IRequestPool {
    private readonly logger;
    private readonly config;
    private readonly queue;
    private readonly worker;
    private readonly redis;
    private readonly activeTasks;
    private readonly completedTasks;
    private readonly maxHistorySize;
    constructor(logger: LoggerService, config: ResponseManagerConfig);
    enqueue<T>(requestId: string, handler: () => Promise<T>, priority?: number, timeout?: number): Promise<T>;
    getPoolStatus(): Promise<RequestPoolStatus>;
    cleanup(): Promise<void>;
    private enqueueWithPool;
    private executeTask;
    private processJob;
    private setupEventListeners;
    private startCleanupTimer;
    private calculateAverages;
}
