{"version": 3, "file": "usage-examples.js", "sourceRoot": "", "sources": ["../../../../src/common/response-manager/examples/usage-examples.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAKA,2CAA4E;AAC5E,6CAAwD;AAGxD,iFAS8C;AAG9C,yFAA4F;AAyCrF,IAAM,4BAA4B,GAAlC,MAAM,4BAA4B;IAEQ;IAD/C,YAC+C,eAAiC;QAAjC,oBAAe,GAAf,eAAe,CAAkB;IAC7E,CAAC;IAaE,AAAN,KAAK,CAAC,UAAU,CAAS,OAA0B;QAEjD,MAAM,IAAI,GAAS;YACjB,EAAE,EAAE,QAAQ,IAAI,CAAC,GAAG,EAAE,EAAE;YACxB,IAAI,EAAE,OAAO,CAAC,IAAI;YAClB,KAAK,EAAE,OAAO,CAAC,KAAK;YACpB,GAAG,EAAE,OAAO,CAAC,GAAG;YAChB,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC;QAGF,OAAO;YACL,OAAO,EAAE,IAAI;YACb,IAAI,EAAE,IAAI;YACV,OAAO,EAAE,QAAQ;YACjB,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,aAAa,EAAE,GAAG;SACnB,CAAC;IACJ,CAAC;IAaK,AAAN,KAAK,CAAC,OAAO,CAAc,EAAU;QAEnC,MAAM,IAAI,GAAS;YACjB,EAAE;YACF,IAAI,EAAE,IAAI;YACV,KAAK,EAAE,sBAAsB;YAC7B,GAAG,EAAE,EAAE;YACP,SAAS,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;YACjC,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC;QAEF,OAAO;YACL,OAAO,EAAE,IAAI;YACb,IAAI,EAAE,IAAI;YACV,OAAO,EAAE,QAAQ;YACjB,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,aAAa,EAAE,EAAE;YACjB,SAAS,EAAE,IAAI;YACf,QAAQ,EAAE,QAAQ,EAAE,EAAE;SACvB,CAAC;IACJ,CAAC;IAaK,AAAN,KAAK,CAAC,cAAc,CAAc,EAAU;QAE1C,MAAM,OAAO,GAAG;YACd,IAAI,EAAE;gBACJ,EAAE;gBACF,IAAI,EAAE,IAAI;gBACV,KAAK,EAAE,sBAAsB;gBAC7B,GAAG,EAAE,EAAE;aACR;YACD,WAAW,EAAE;gBACX,KAAK,EAAE,MAAM;gBACb,QAAQ,EAAE,OAAO;aAClB;YACD,UAAU,EAAE;gBACV,UAAU,EAAE,GAAG;gBACf,WAAW,EAAE,IAAI,IAAI,EAAE;aACxB;SACF,CAAC;QAEF,OAAO;YACL,OAAO,EAAE,IAAI;YACb,IAAI,EAAE,OAAO;YACb,OAAO,EAAE,UAAU;YACnB,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,aAAa,EAAE,GAAG;SACnB,CAAC;IACJ,CAAC;IAcK,AAAN,KAAK,CAAC,gBAAgB,CAAS,QAA6B;QAE1D,MAAM,KAAK,GAAW,QAAQ,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC;YACtD,EAAE,EAAE,QAAQ,IAAI,CAAC,GAAG,EAAE,IAAI,KAAK,EAAE;YACjC,IAAI,EAAE,OAAO,CAAC,IAAI;YAClB,KAAK,EAAE,OAAO,CAAC,KAAK;YACpB,GAAG,EAAE,OAAO,CAAC,GAAG;YAChB,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC,CAAC,CAAC;QAEJ,OAAO;YACL,OAAO,EAAE,IAAI;YACb,IAAI,EAAE,KAAK;YACX,OAAO,EAAE,QAAQ,KAAK,CAAC,MAAM,MAAM;YACnC,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,aAAa,EAAE,QAAQ,CAAC,MAAM,GAAG,EAAE;SACpC,CAAC;IACJ,CAAC;IAcK,AAAN,KAAK,CAAC,WAAW;QAEf,MAAM,KAAK,GAAW;YACpB;gBACE,EAAE,EAAE,QAAQ;gBACZ,IAAI,EAAE,IAAI;gBACV,KAAK,EAAE,sBAAsB;gBAC7B,GAAG,EAAE,EAAE;gBACP,SAAS,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;gBACjC,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB;YACD;gBACE,EAAE,EAAE,QAAQ;gBACZ,IAAI,EAAE,IAAI;gBACV,KAAK,EAAE,kBAAkB;gBACzB,GAAG,EAAE,EAAE;gBACP,SAAS,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;gBACjC,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB;SACF,CAAC;QAEF,OAAO;YACL,OAAO,EAAE,IAAI;YACb,IAAI,EAAE,KAAK;YACX,OAAO,EAAE,UAAU;YACnB,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,aAAa,EAAE,GAAG;YAClB,SAAS,EAAE,KAAK;YAChB,QAAQ,EAAE,eAAe;SAC1B,CAAC;IACJ,CAAC;IAYK,AAAN,KAAK,CAAC,gBAAgB,CACP,EAAU,EACf,IAAwB;QAEhC,OAAO;YACL,OAAO,EAAE,IAAI;YACb,IAAI,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE;YACjC,OAAO,EAAE,UAAU;YACnB,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,aAAa,EAAE,EAAE;SAClB,CAAC;IACJ,CAAC;IAaK,AAAN,KAAK,CAAC,WAAW;QACf,MAAM,MAAM,GAAG,UAAU,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC;QAEtC,OAAO;YACL,OAAO,EAAE,IAAI;YACb,IAAI,EAAE;gBACJ,MAAM;gBACN,aAAa,EAAE,MAAM;aACtB;YACD,OAAO,EAAE,SAAS;YAClB,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,aAAa,EAAE,GAAG;SACnB,CAAC;IACJ,CAAC;IAQK,AAAN,KAAK,CAAC,sBAAsB,CACb,EAAU,EACf,aAAgC;QAGxC,MAAM,OAAO,GAAG,IAAI,CAAC,eAAe,CAAC,aAAa,CAChD,EAAE,IAAI,EAAE,EAAE,EAAE,EAAE,cAAc,EAAE,EAAS,EACvC,MAAM,EACN,cAAc,CACf,CAAC;QAGF,MAAM,aAAa,GAAwB;YACzC,OAAO,EAAE,IAAI;YACb,IAAI,EAAE;gBACJ,EAAE;gBACF,IAAI,EAAE,aAAa,CAAC,IAAI,IAAI,IAAI;gBAChC,KAAK,EAAE,aAAa,CAAC,KAAK,IAAI,sBAAsB;gBACpD,GAAG,EAAE,aAAa,CAAC,GAAG,IAAI,EAAE;gBAC5B,SAAS,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;gBACjC,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB;YACD,OAAO,EAAE,QAAQ;YACjB,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,aAAa,EAAE,GAAG;SACnB,CAAC;QAGF,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,mBAAmB,CACrE,aAAa,EACb,OAAO,EACP,2CAAc,CAAC,IAAI,CACpB,CAAC;QAGF,OAAO,IAAI,CAAC,eAAe,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC;IAC/D,CAAC;IAWK,AAAN,KAAK,CAAC,YAAY;QAEhB,OAAO;YACL,OAAO,EAAE,KAAK;YACd,OAAO,EAAE,QAAQ;YACjB,MAAM,EAAE,CAAC,OAAO,EAAE,UAAU,CAAC;YAC7B,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,aAAa,EAAE,EAAE;SAClB,CAAC;IACJ,CAAC;IAOK,AAAN,KAAK,CAAC,aAAa;QACjB,OAAO,MAAM,IAAI,CAAC,eAAe,CAAC,aAAa,EAAE,CAAC;IACpD,CAAC;CACF,CAAA;AAlTY,oEAA4B;AAgBjC;IAPL,IAAA,aAAI,EAAC,OAAO,CAAC;IACb,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,iBAAiB,EAAE,CAAC;IAC5C,IAAA,2CAAkB,EAAC;QAClB,MAAM,EAAE,MAAM;QACd,SAAS,EAAE,YAAY;QACvB,aAAa,EAAE,IAAI;KACpB,CAAC;IACgB,WAAA,IAAA,aAAI,GAAE,CAAA;;;;8DAmBvB;AAaK;IAPL,IAAA,YAAG,EAAC,WAAW,CAAC;IAChB,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,iBAAiB,EAAE,CAAC;IAC5C,IAAA,yCAAgB,EAAC;QAChB,MAAM,EAAE,MAAM;QACd,SAAS,EAAE,SAAS;QACpB,aAAa,EAAE,IAAI;KACpB,CAAC;IACa,WAAA,IAAA,cAAK,EAAC,IAAI,CAAC,CAAA;;;;2DAoBzB;AAaK;IAPL,IAAA,YAAG,EAAC,mBAAmB,CAAC;IACxB,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,iBAAiB,EAAE,CAAC;IAC5C,IAAA,4CAAmB,EAAC;QACnB,MAAM,EAAE,MAAM;QACd,SAAS,EAAE,gBAAgB;QAC3B,qBAAqB,EAAE,IAAI;KAC5B,CAAC;IACoB,WAAA,IAAA,cAAK,EAAC,IAAI,CAAC,CAAA;;;;kEA0BhC;AAcK;IARL,IAAA,aAAI,EAAC,aAAa,CAAC;IACnB,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,kBAAkB,EAAE,CAAC;IAC7C,IAAA,mDAA0B,EAAC;QAC1B,MAAM,EAAE,MAAM;QACd,SAAS,EAAE,kBAAkB;QAC7B,QAAQ,EAAE,CAAC;QACX,OAAO,EAAE,KAAK;KACf,CAAC;IACsB,WAAA,IAAA,aAAI,GAAE,CAAA;;;;oEAkB7B;AAcK;IARL,IAAA,YAAG,EAAC,OAAO,CAAC;IACZ,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,iBAAiB,EAAE,CAAC;IAC5C,IAAA,0CAAiB,EAAC;QACjB,MAAM,EAAE,MAAM;QACd,SAAS,EAAE,aAAa;QACxB,QAAQ,EAAE,GAAG;QACb,cAAc,EAAE,WAAW;KAC5B,CAAC;;;;+DA+BD;AAYK;IANL,IAAA,aAAI,EAAC,kBAAkB,CAAC;IACxB,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,iBAAiB,EAAE,CAAC;IAC5C,IAAA,4CAAmB,EAAC;QACnB,MAAM,EAAE,MAAM;QACd,SAAS,EAAE,kBAAkB;KAC9B,CAAC;IAEC,WAAA,IAAA,cAAK,EAAC,IAAI,CAAC,CAAA;IACX,WAAA,IAAA,aAAI,GAAE,CAAA;;;;oEASR;AAaK;IAPL,IAAA,aAAI,EAAC,cAAc,CAAC;IACpB,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,iBAAiB,EAAE,CAAC;IAC5C,IAAA,yCAAgB,EAAC;QAChB,MAAM,EAAE,MAAM;QACd,SAAS,EAAE,aAAa;QACxB,QAAQ,EAAE,CAAC,CAAC;KACb,CAAC;;;;+DAcD;AAQK;IAFL,IAAA,aAAI,EAAC,kBAAkB,CAAC;IACxB,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,aAAa,EAAE,CAAC;IAEtC,WAAA,IAAA,cAAK,EAAC,IAAI,CAAC,CAAA;IACX,WAAA,IAAA,aAAI,GAAE,CAAA;;;;0EAkCR;AAWK;IANL,IAAA,aAAI,EAAC,qBAAqB,CAAC;IAC3B,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,QAAQ,EAAE,CAAC;IACnC,IAAA,2CAAkB,EAAC;QAClB,MAAM,EAAE,MAAM;QACd,SAAS,EAAE,cAAc;KAC1B,CAAC;;;;gEAUD;AAOK;IAFL,IAAA,YAAG,EAAC,aAAa,CAAC;IAClB,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,SAAS,EAAE,CAAC;;;;iEAGpC;uCAjTU,4BAA4B;IAFxC,IAAA,iBAAO,EAAC,YAAY,CAAC;IACrB,IAAA,mBAAU,EAAC,uBAAuB,CAAC;IAG/B,WAAA,IAAA,eAAM,EAAC,kBAAkB,CAAC,CAAA;;GAFlB,4BAA4B,CAkTxC"}