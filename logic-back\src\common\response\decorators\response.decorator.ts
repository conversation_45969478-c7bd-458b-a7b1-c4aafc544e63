import { SetMetadata, applyDecorators } from '@nestjs/common';
import { ApiResponse } from '@nestjs/swagger';
import { ResponseFormat } from '../interfaces/response.interface';

/**
 * 响应配置元数据键
 */
export const RESPONSE_CONFIG_KEY = 'response_config';

/**
 * 响应配置接口
 */
export interface ResponseDecoratorConfig {
  /** 响应格式 */
  format?: ResponseFormat;
  /** 是否包含执行时间 */
  includeExecutionTime?: boolean;
  /** 是否包含请求ID */
  includeRequestId?: boolean;
  /** 成功消息 */
  successMessage?: string;
  /** Swagger文档配置 */
  swagger?: {
    /** 响应描述 */
    description?: string;
    /** 响应示例 */
    example?: any;
    /** 响应类型 */
    type?: any;
  };
}

/**
 * 响应装饰器
 * 用于配置控制器方法的响应格式
 */
export function ResponseConfig(config?: ResponseDecoratorConfig) {
  const decorators: any[] = [SetMetadata(RESPONSE_CONFIG_KEY, config || {})];

  // 添加Swagger文档
  if (config?.swagger) {
    decorators.push(
      ApiResponse({
        status: 200,
        description: config.swagger.description || '操作成功',
        schema: config.swagger.example ? {
          example: config.swagger.example
        } : undefined,
        type: config.swagger.type,
      })
    );
  }

  return applyDecorators(...decorators);
}

/**
 * 成功响应装饰器
 */
export function ApiSuccessResponse(message?: string, example?: any) {
  return ResponseConfig({
    successMessage: message,
    swagger: {
      description: message || '操作成功',
      example: example || {
        success: true,
        code: 200,
        message: message || '操作成功',
        data: {},
        timestamp: new Date().toISOString(),
        requestId: 'uuid-example'
      }
    }
  });
}

/**
 * 分页响应装饰器
 */
export function ApiPaginatedResponse(message?: string, itemType?: any) {
  return ResponseConfig({
    successMessage: message,
    swagger: {
      description: message || '获取分页数据成功',
      example: {
        success: true,
        code: 200,
        message: message || '获取分页数据成功',
        data: [],
        pagination: {
          page: 1,
          size: 10,
          total: 100,
          totalPages: 10,
          hasNext: true,
          hasPrev: false
        },
        timestamp: new Date().toISOString(),
        requestId: 'uuid-example'
      },
      type: itemType
    }
  });
}

/**
 * 简化响应装饰器
 */
export function ApiSimpleResponse(message?: string) {
  return ResponseConfig({
    format: ResponseFormat.SIMPLE,
    successMessage: message,
    swagger: {
      description: message || '操作成功',
      example: {
        code: 200,
        msg: message || '操作成功',
        data: {}
      }
    }
  });
}

/**
 * RESTful响应装饰器
 */
export function ApiRestfulResponse(type?: any) {
  return ResponseConfig({
    format: ResponseFormat.RESTFUL,
    swagger: {
      description: '操作成功',
      type: type
    }
  });
}

/**
 * 错误响应装饰器
 */
export function ApiErrorResponse(status: number, message: string, example?: any) {
  return applyDecorators(
    ApiResponse({
      status,
      description: message,
      schema: {
        example: example || {
          success: false,
          code: status,
          message,
          timestamp: new Date().toISOString(),
          requestId: 'uuid-example'
        }
      }
    })
  );
}

/**
 * 常用错误响应装饰器组合
 */
export function CommonErrorResponses() {
  return applyDecorators(
    ApiErrorResponse(400, '请求参数错误', {
      success: false,
      code: 400,
      message: '请求参数错误',
      errors: ['参数验证失败'],
      errorType: 'VALIDATION',
      timestamp: new Date().toISOString(),
      requestId: 'uuid-example'
    }),
    ApiErrorResponse(401, '未授权访问', {
      success: false,
      code: 401,
      message: '未授权访问',
      errorType: 'AUTH',
      timestamp: new Date().toISOString(),
      requestId: 'uuid-example'
    }),
    ApiErrorResponse(403, '权限不足', {
      success: false,
      code: 403,
      message: '权限不足',
      errorType: 'AUTH',
      timestamp: new Date().toISOString(),
      requestId: 'uuid-example'
    }),
    ApiErrorResponse(404, '资源不存在', {
      success: false,
      code: 404,
      message: '资源不存在',
      errorType: 'BUSINESS',
      timestamp: new Date().toISOString(),
      requestId: 'uuid-example'
    }),
    ApiErrorResponse(500, '系统内部错误', {
      success: false,
      code: 500,
      message: '系统内部错误',
      errorType: 'SYSTEM',
      timestamp: new Date().toISOString(),
      requestId: 'uuid-example'
    })
  );
}

/**
 * API响应装饰器组合
 * 包含成功响应和常见错误响应
 */
export function ApiResponseDocs(config?: {
  success?: { message?: string; example?: any; type?: any };
  paginated?: boolean;
  simple?: boolean;
  restful?: boolean;
}) {
  const decorators: any[] = [];

  if (config?.paginated) {
    decorators.push(ApiPaginatedResponse(config.success?.message, config.success?.type));
  } else if (config?.simple) {
    decorators.push(ApiSimpleResponse(config.success?.message));
  } else if (config?.restful) {
    decorators.push(ApiRestfulResponse(config.success?.type));
  } else {
    decorators.push(ApiSuccessResponse(config?.success?.message, config?.success?.example));
  }

  decorators.push(CommonErrorResponses());

  return applyDecorators(...decorators);
}
