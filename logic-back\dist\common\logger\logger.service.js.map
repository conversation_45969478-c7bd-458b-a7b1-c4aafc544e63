{"version": 3, "file": "logger.service.js", "sourceRoot": "", "sources": ["../../../src/common/logger/logger.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAAgF;AAChF,qCAAiC;AACjC,+CAAuD;AACvD,2CAAwC;AAGjC,IAAM,aAAa,GAAnB,MAAM,aAAa;IAE4B;IADpD,YACoD,MAAc;QAAd,WAAM,GAAN,MAAM,CAAQ;IAC/D,CAAC;IAKJ,GAAG,CAAC,OAAY,EAAE,OAAgB;QAChC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,OAAO,EAAE,CAAC,CAAC;IACzC,CAAC;IAKD,KAAK,CAAC,OAAY,EAAE,KAAc,EAAE,OAAgB;QAClD,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,CAAC;IACjD,CAAC;IAKD,IAAI,CAAC,OAAY,EAAE,OAAgB;QACjC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,OAAO,EAAE,CAAC,CAAC;IACzC,CAAC;IAKD,KAAK,CAAC,OAAY,EAAE,OAAgB;QAClC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE,OAAO,EAAE,CAAC,CAAC;IAC1C,CAAC;IAKD,OAAO,CAAC,OAAY,EAAE,OAAgB;QACpC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,OAAO,EAAE,CAAC,CAAC;IAC5C,CAAC;IAKD,cAAc,CAAC,GAAQ,EAAE,GAAQ,EAAE,YAAoB;QACrD,MAAM,OAAO,GAAG;YACd,MAAM,EAAE,GAAG,CAAC,MAAM;YAClB,GAAG,EAAE,GAAG,CAAC,GAAG;YACZ,UAAU,EAAE,GAAG,CAAC,UAAU;YAC1B,YAAY,EAAE,GAAG,YAAY,IAAI;YACjC,SAAS,EAAE,GAAG,CAAC,GAAG,CAAC,YAAY,CAAC;YAChC,EAAE,EAAE,GAAG,CAAC,EAAE,IAAI,GAAG,CAAC,UAAU,CAAC,aAAa;YAC1C,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC;QAEF,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE;YAC/B,OAAO,EAAE,MAAM;YACf,GAAG,OAAO;SACX,CAAC,CAAC;IACL,CAAC;IAKD,WAAW,CAAC,SAAiB,EAAE,KAAa,EAAE,IAAU,EAAE,KAAW;QACnE,MAAM,OAAO,GAAG;YACd,SAAS;YACT,KAAK;YACL,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS;YAC7C,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS;YACxC,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC;QAEF,IAAI,KAAK,EAAE,CAAC;YACV,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,YAAY,SAAS,cAAc,KAAK,EAAE,EAAE;gBAC5D,OAAO,EAAE,UAAU;gBACnB,GAAG,OAAO;gBACV,KAAK,EAAE,KAAK,CAAC,KAAK;aACnB,CAAC,CAAC;QACL,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,SAAS,OAAO,KAAK,EAAE,EAAE;gBACpD,OAAO,EAAE,UAAU;gBACnB,GAAG,OAAO;aACX,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAKD,UAAU,CAAC,MAAc,EAAE,IAAS,EAAE,KAAW;QAC/C,MAAM,OAAO,GAAG;YACd,MAAM;YACN,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;YAC1B,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS;YACxC,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC;QAEF,IAAI,KAAK,EAAE,CAAC;YACV,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,WAAW,MAAM,SAAS,EAAE;gBAC5C,OAAO,EAAE,SAAS;gBAClB,GAAG,OAAO;gBACV,KAAK,EAAE,KAAK,CAAC,KAAK;aACnB,CAAC,CAAC;QACL,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,MAAM,EAAE,EAAE;gBACpC,OAAO,EAAE,SAAS;gBAClB,GAAG,OAAO;aACX,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAKD,OAAO,CAAC,MAAc,EAAE,MAAe,EAAE,OAAa,EAAE,KAAW;QACjE,MAAM,OAAO,GAAG;YACd,MAAM;YACN,MAAM;YACN,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,SAAS;YACtD,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS;YACxC,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC;QAEF,IAAI,KAAK,EAAE,CAAC;YACV,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,MAAM,SAAS,EAAE;gBACzC,OAAO,EAAE,MAAM;gBACf,GAAG,OAAO;gBACV,KAAK,EAAE,KAAK,CAAC,KAAK;aACnB,CAAC,CAAC;QACL,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,MAAM,EAAE,EAAE;gBACjC,OAAO,EAAE,MAAM;gBACf,GAAG,OAAO;aACX,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAKD,WAAW,CAAC,MAAc,EAAE,MAAc,EAAE,IAAU,EAAE,KAAW;QACjE,MAAM,OAAO,GAAG;YACd,MAAM;YACN,MAAM;YACN,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS;YAC7C,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS;YACxC,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC;QAEF,IAAI,KAAK,EAAE,CAAC;YACV,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,YAAY,MAAM,IAAI,MAAM,SAAS,EAAE;gBACvD,OAAO,EAAE,UAAU;gBACnB,GAAG,OAAO;gBACV,KAAK,EAAE,KAAK,CAAC,KAAK;aACnB,CAAC,CAAC;QACL,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,MAAM,IAAI,MAAM,EAAE,EAAE;gBAC/C,OAAO,EAAE,UAAU;gBACnB,GAAG,OAAO;aACX,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAKD,UAAU,CAAC,OAAe,EAAE,OAAa;QACvC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE;YACxB,OAAO,EAAE,SAAS;YAClB,GAAG,OAAO;YACV,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC,CAAC;IACL,CAAC;IAKD,cAAc,CAAC,SAAiB,EAAE,QAAgB,EAAE,OAAa;QAC/D,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,gBAAgB,SAAS,SAAS,QAAQ,IAAI,EAAE;YAC/D,OAAO,EAAE,aAAa;YACtB,SAAS;YACT,QAAQ;YACR,GAAG,OAAO;YACV,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC,CAAC;IACL,CAAC;IAKD,WAAW,CAAC,KAAa,EAAE,OAAY,EAAE,WAAsC,QAAQ;QACrF,MAAM,OAAO,GAAG;YACd,KAAK;YACL,QAAQ;YACR,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;YAChC,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC;QAEF,IAAI,QAAQ,KAAK,MAAM,EAAE,CAAC;YACxB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,mBAAmB,KAAK,EAAE,EAAE;gBAC5C,OAAO,EAAE,UAAU;gBACnB,GAAG,OAAO;aACX,CAAC,CAAC;QACL,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,mBAAmB,KAAK,EAAE,EAAE;gBAC3C,OAAO,EAAE,UAAU;gBACnB,GAAG,OAAO;aACX,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAKD,cAAc,CAAC,IAiBd;QACC,MAAM,OAAO,GAAG;YACd,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,aAAa,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,aAAa,IAAI,CAAC,CAAC,CAAC,SAAS;YACzE,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,EAAE,EAAE,IAAI,CAAC,EAAE;YACX,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC;QAGF,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACjB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,iBAAiB,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,IAAI,MAAM,IAAI,CAAC,OAAO,EAAE,EAAE;gBAC9E,OAAO,EAAE,aAAa;gBACtB,GAAG,OAAO;gBACV,WAAW,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,SAAS;gBAC/E,YAAY,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,SAAS;aACnF,CAAC,CAAC;QACL,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,cAAc,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,IAAI,MAAM,IAAI,CAAC,OAAO,EAAE,EAAE;gBAC5E,OAAO,EAAE,aAAa;gBACtB,GAAG,OAAO;gBACV,MAAM,EAAE,IAAI,CAAC,MAAM;gBACnB,SAAS,EAAE,IAAI,CAAC,SAAS;gBACzB,WAAW,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,SAAS;aAChF,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAKD,qBAAqB,CAAC,IAOrB;QACC,MAAM,OAAO,GAAG;YACd,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,QAAQ,IAAI,CAAC,CAAC,CAAC,SAAS;YAC1D,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,SAAS;YACnE,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC;QAEF,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,8BAA8B,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,MAAM,EAAE,EAAE;gBAC3E,OAAO,EAAE,oBAAoB;gBAC7B,GAAG,OAAO;gBACV,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,OAAO;gBACzB,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK;aACxB,CAAC,CAAC;QACL,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,wBAAwB,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,MAAM,EAAE,EAAE;gBACrE,OAAO,EAAE,oBAAoB;gBAC7B,GAAG,OAAO;aACX,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAKD,oBAAoB,CAAC,IAOpB;QACC,MAAM,OAAO,GAAG;YACd,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,QAAQ,IAAI,CAAC,CAAC,CAAC,SAAS;YAC1D,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC;QAEF,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,uBAAuB,IAAI,CAAC,UAAU,OAAO,IAAI,CAAC,QAAQ,EAAE,EAAE;YAC9E,OAAO,EAAE,mBAAmB;YAC5B,GAAG,OAAO;YACV,YAAY,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,SAAS;YAClF,eAAe,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,SAAS;SAC5F,CAAC,CAAC;IACL,CAAC;IAKD,gBAAgB,CAAC,IAMhB;QACC,MAAM,OAAO,GAAG;YACd,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,GAAG,EAAE,IAAI,CAAC,GAAG;YACb,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC;QAEF,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,mBAAmB,IAAI,CAAC,MAAM,EAAE,EAAE;YAClD,OAAO,EAAE,eAAe;YACxB,GAAG,OAAO;SACX,CAAC,CAAC;IACL,CAAC;IAKO,YAAY,CAAC,IAAS;QAC5B,IAAI,CAAC,IAAI,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE,CAAC;YACtC,OAAO,IAAI,CAAC;QACd,CAAC;QAED,MAAM,eAAe,GAAG;YACtB,UAAU,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,eAAe;YACrD,aAAa,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,SAAS;SAClD,CAAC;QAEF,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;QAEnD,MAAM,cAAc,GAAG,CAAC,GAAQ,EAAO,EAAE;YACvC,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC;gBACvB,OAAO,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC;YAC/C,CAAC;YAED,IAAI,GAAG,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE,CAAC;gBACnC,MAAM,MAAM,GAAQ,EAAE,CAAC;gBACvB,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC;oBAC/C,MAAM,QAAQ,GAAG,GAAG,CAAC,WAAW,EAAE,CAAC;oBACnC,IAAI,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;wBAC5D,MAAM,CAAC,GAAG,CAAC,GAAG,gBAAgB,CAAC;oBACjC,CAAC;yBAAM,CAAC;wBACN,MAAM,CAAC,GAAG,CAAC,GAAG,cAAc,CAAC,KAAK,CAAC,CAAC;oBACtC,CAAC;gBACH,CAAC;gBACD,OAAO,MAAM,CAAC;YAChB,CAAC;YAED,OAAO,GAAG,CAAC;QACb,CAAC,CAAC;QAEF,OAAO,cAAc,CAAC,SAAS,CAAC,CAAC;IACnC,CAAC;CACF,CAAA;AAxYY,sCAAa;wBAAb,aAAa;IADzB,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,eAAM,EAAC,sCAAuB,CAAC,CAAA;qCAA0B,gBAAM;GAFvD,aAAa,CAwYzB"}