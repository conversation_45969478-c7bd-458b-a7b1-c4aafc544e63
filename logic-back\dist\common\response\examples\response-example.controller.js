"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ResponseExampleController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const response_manager_service_1 = require("../services/response-manager.service");
const response_decorator_1 = require("../decorators/response.decorator");
const response_exception_filter_1 = require("../filters/response-exception.filter");
const response_interface_1 = require("../interfaces/response.interface");
let ResponseExampleController = class ResponseExampleController {
    responseManager;
    constructor(responseManager) {
        this.responseManager = responseManager;
    }
    async basicSuccess() {
        return {
            id: 1,
            name: '测试数据',
            createTime: new Date(),
        };
    }
    async manualSuccess() {
        const data = {
            id: 1,
            name: '手动创建的数据',
            createTime: new Date(),
        };
        return this.responseManager.success(data, '手动创建成功响应');
    }
    async paginatedData(page = 1, size = 10) {
        const mockData = Array.from({ length: size }, (_, index) => ({
            id: (page - 1) * size + index + 1,
            name: `数据项 ${(page - 1) * size + index + 1}`,
            createTime: new Date(),
        }));
        return {
            data: mockData,
            pagination: {
                page,
                size,
                total: 100,
            },
        };
    }
    async manualPaginated(page = 1, size = 10) {
        const mockData = Array.from({ length: size }, (_, index) => ({
            id: (page - 1) * size + index + 1,
            name: `手动分页数据 ${(page - 1) * size + index + 1}`,
            createTime: new Date(),
        }));
        return this.responseManager.paginated(mockData, { page, size, total: 100 }, '手动创建分页响应成功');
    }
    async simpleFormat() {
        return {
            message: '这是简化格式的响应',
            value: 42,
        };
    }
    async restfulFormat() {
        return {
            id: 1,
            name: 'RESTful格式数据',
            createTime: new Date(),
        };
    }
    async customFormat() {
        return {
            customField: '自定义字段',
            data: {
                id: 1,
                name: '自定义格式数据',
            },
        };
    }
    async businessError() {
        throw new response_exception_filter_1.BusinessException('这是一个业务异常示例', 'BUSINESS_ERROR_001');
    }
    async validationError(body) {
        const errors = [];
        if (!body.name) {
            errors.push('姓名不能为空');
        }
        if (!body.email) {
            errors.push('邮箱不能为空');
        }
        else if (!/\S+@\S+\.\S+/.test(body.email)) {
            errors.push('邮箱格式不正确');
        }
        if (errors.length > 0) {
            throw new response_exception_filter_1.ValidationException('数据验证失败', errors);
        }
        return this.responseManager.success(body, '数据验证通过');
    }
    async httpError(id) {
        if (id === 'not-found') {
            throw new common_1.HttpException('资源不存在', common_1.HttpStatus.NOT_FOUND);
        }
        if (id === 'forbidden') {
            throw new common_1.HttpException('权限不足', common_1.HttpStatus.FORBIDDEN);
        }
        if (id === 'unauthorized') {
            throw new common_1.HttpException('未授权访问', common_1.HttpStatus.UNAUTHORIZED);
        }
        return this.responseManager.success({ id }, '获取资源成功');
    }
    async systemError() {
        throw new Error('这是一个系统异常示例');
    }
    async nullData() {
        return null;
    }
    async manualError() {
        return this.responseManager.businessError('这是手动创建的业务错误', 'MANUAL_ERROR_001');
    }
    async fullDocs() {
        return {
            id: 1,
            title: '完整API文档示例',
            content: '这是一个包含完整API文档的示例接口',
            createTime: new Date(),
        };
    }
};
exports.ResponseExampleController = ResponseExampleController;
__decorate([
    (0, common_1.Get)('basic-success'),
    (0, swagger_1.ApiOperation)({ summary: '基础成功响应示例' }),
    (0, response_decorator_1.SuccessResponse)('获取数据成功'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ResponseExampleController.prototype, "basicSuccess", null);
__decorate([
    (0, common_1.Get)('manual-success'),
    (0, swagger_1.ApiOperation)({ summary: '手动创建成功响应示例' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ResponseExampleController.prototype, "manualSuccess", null);
__decorate([
    (0, common_1.Get)('paginated'),
    (0, swagger_1.ApiOperation)({ summary: '分页响应示例' }),
    (0, response_decorator_1.PaginatedResponse)('获取分页数据成功'),
    __param(0, (0, common_1.Query)('page')),
    __param(1, (0, common_1.Query)('size')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Number]),
    __metadata("design:returntype", Promise)
], ResponseExampleController.prototype, "paginatedData", null);
__decorate([
    (0, common_1.Get)('manual-paginated'),
    (0, swagger_1.ApiOperation)({ summary: '手动创建分页响应示例' }),
    __param(0, (0, common_1.Query)('page')),
    __param(1, (0, common_1.Query)('size')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Number]),
    __metadata("design:returntype", Promise)
], ResponseExampleController.prototype, "manualPaginated", null);
__decorate([
    (0, common_1.Get)('simple'),
    (0, swagger_1.ApiOperation)({ summary: '简化响应格式示例' }),
    (0, response_decorator_1.SimpleResponse)('简化格式响应'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ResponseExampleController.prototype, "simpleFormat", null);
__decorate([
    (0, common_1.Get)('restful'),
    (0, swagger_1.ApiOperation)({ summary: 'RESTful响应格式示例' }),
    (0, response_decorator_1.RestfulResponse)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ResponseExampleController.prototype, "restfulFormat", null);
__decorate([
    (0, common_1.Get)('custom'),
    (0, swagger_1.ApiOperation)({ summary: '自定义响应格式示例' }),
    (0, response_decorator_1.Response)({
        format: response_interface_1.ResponseFormat.CUSTOM,
        successMessage: '自定义响应成功',
        includeExecutionTime: true,
        includeRequestId: true,
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ResponseExampleController.prototype, "customFormat", null);
__decorate([
    (0, common_1.Get)('business-error'),
    (0, swagger_1.ApiOperation)({ summary: '业务异常示例' }),
    (0, response_decorator_1.ApiResponseDocs)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ResponseExampleController.prototype, "businessError", null);
__decorate([
    (0, common_1.Post)('validation-error'),
    (0, swagger_1.ApiOperation)({ summary: '验证异常示例' }),
    (0, response_decorator_1.ApiResponseDocs)(),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], ResponseExampleController.prototype, "validationError", null);
__decorate([
    (0, common_1.Get)('http-error/:id'),
    (0, swagger_1.ApiOperation)({ summary: 'HTTP异常示例' }),
    (0, response_decorator_1.ApiResponseDocs)(),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ResponseExampleController.prototype, "httpError", null);
__decorate([
    (0, common_1.Get)('system-error'),
    (0, swagger_1.ApiOperation)({ summary: '系统异常示例' }),
    (0, response_decorator_1.ApiResponseDocs)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ResponseExampleController.prototype, "systemError", null);
__decorate([
    (0, common_1.Get)('null-data'),
    (0, swagger_1.ApiOperation)({ summary: '空数据响应示例' }),
    (0, response_decorator_1.SuccessResponse)('操作成功，无返回数据'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ResponseExampleController.prototype, "nullData", null);
__decorate([
    (0, common_1.Get)('manual-error'),
    (0, swagger_1.ApiOperation)({ summary: '手动错误响应示例' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ResponseExampleController.prototype, "manualError", null);
__decorate([
    (0, common_1.Get)('full-docs'),
    (0, swagger_1.ApiOperation)({ summary: '完整API文档示例' }),
    (0, response_decorator_1.ApiResponseDocs)({
        success: {
            message: '获取完整文档数据成功',
            example: {
                id: 1,
                title: '文档标题',
                content: '文档内容',
                createTime: new Date().toISOString(),
            },
        },
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ResponseExampleController.prototype, "fullDocs", null);
exports.ResponseExampleController = ResponseExampleController = __decorate([
    (0, swagger_1.ApiTags)('响应管理器示例'),
    (0, common_1.Controller)('response-examples'),
    __metadata("design:paramtypes", [response_manager_service_1.ResponseManagerService])
], ResponseExampleController);
//# sourceMappingURL=response-example.controller.js.map