"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ResponseExampleController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const response_manager_service_1 = require("../services/response-manager.service");
const response_cache_service_1 = require("../services/response-cache.service");
const response_decorator_1 = require("../decorators/response.decorator");
const cache_decorator_1 = require("../decorators/cache.decorator");
const response_exception_filter_1 = require("../filters/response-exception.filter");
const response_interface_1 = require("../interfaces/response.interface");
let ResponseExampleController = class ResponseExampleController {
    responseManager;
    cacheService;
    constructor(responseManager, cacheService) {
        this.responseManager = responseManager;
        this.cacheService = cacheService;
    }
    async basicSuccess() {
        return {
            id: 1,
            name: '测试数据',
            createTime: new Date(),
        };
    }
    async manualSuccess() {
        const data = {
            id: 1,
            name: '手动创建的数据',
            createTime: new Date(),
        };
        return this.responseManager.success(data, '手动创建成功响应');
    }
    async paginatedData(page = 1, size = 10) {
        const mockData = Array.from({ length: size }, (_, index) => ({
            id: (page - 1) * size + index + 1,
            name: `数据项 ${(page - 1) * size + index + 1}`,
            createTime: new Date(),
        }));
        return {
            data: mockData,
            pagination: {
                page,
                size,
                total: 100,
            },
        };
    }
    async manualPaginated(page = 1, size = 10) {
        const mockData = Array.from({ length: size }, (_, index) => ({
            id: (page - 1) * size + index + 1,
            name: `手动分页数据 ${(page - 1) * size + index + 1}`,
            createTime: new Date(),
        }));
        return this.responseManager.paginated(mockData, { page, size, total: 100 }, '手动创建分页响应成功');
    }
    async simpleFormat() {
        return {
            message: '这是简化格式的响应',
            value: 42,
        };
    }
    async restfulFormat() {
        return {
            id: 1,
            name: 'RESTful格式数据',
            createTime: new Date(),
        };
    }
    async customFormat() {
        return {
            customField: '自定义字段',
            data: {
                id: 1,
                name: '自定义格式数据',
            },
        };
    }
    async businessError() {
        throw new response_exception_filter_1.BusinessException('这是一个业务异常示例', 'BUSINESS_ERROR_001');
    }
    async validationError(body) {
        const errors = [];
        if (!body.name) {
            errors.push('姓名不能为空');
        }
        if (!body.email) {
            errors.push('邮箱不能为空');
        }
        else if (!/\S+@\S+\.\S+/.test(body.email)) {
            errors.push('邮箱格式不正确');
        }
        if (errors.length > 0) {
            throw new response_exception_filter_1.ValidationException('数据验证失败', errors);
        }
        return this.responseManager.success(body, '数据验证通过');
    }
    async httpError(id) {
        if (id === 'not-found') {
            throw new common_1.HttpException('资源不存在', common_1.HttpStatus.NOT_FOUND);
        }
        if (id === 'forbidden') {
            throw new common_1.HttpException('权限不足', common_1.HttpStatus.FORBIDDEN);
        }
        if (id === 'unauthorized') {
            throw new common_1.HttpException('未授权访问', common_1.HttpStatus.UNAUTHORIZED);
        }
        return this.responseManager.success({ id }, '获取资源成功');
    }
    async systemError() {
        throw new Error('这是一个系统异常示例');
    }
    async nullData() {
        return null;
    }
    async manualError() {
        return this.responseManager.businessError('这是手动创建的业务错误', 'MANUAL_ERROR_001');
    }
    async fullDocs() {
        return {
            id: 1,
            title: '完整API文档示例',
            content: '这是一个包含完整API文档的示例接口',
            createTime: new Date(),
        };
    }
    async loggingExample() {
        await new Promise(resolve => setTimeout(resolve, 100));
        this.responseManager.logResponseCache('hit', 'user:123', 3600);
        return {
            id: 1,
            message: '这是一个日志记录示例',
            timestamp: new Date(),
            features: [
                '自动记录API响应日志',
                '记录请求处理过程',
                '性能监控',
                '错误追踪',
                '缓存操作记录',
                '敏感数据脱敏'
            ]
        };
    }
    async performanceTest(delay = 1500) {
        await new Promise(resolve => setTimeout(resolve, delay));
        return {
            message: '性能测试完成',
            delay: `${delay}ms`,
            note: '这个接口会触发性能日志记录（超过阈值时）'
        };
    }
    async sensitiveData(data) {
        const processedData = {
            id: 1,
            username: data.username,
            email: data.email,
            password: data.password,
            token: 'jwt-token-example',
            credit_card: '1234-5678-9012-3456',
            phone: data.phone,
        };
        return {
            message: '敏感数据处理成功',
            note: '敏感字段在日志中已被自动脱敏',
            processedFields: Object.keys(processedData)
        };
    }
    async shortCacheExample() {
        await new Promise(resolve => setTimeout(resolve, 100));
        return {
            message: '这是短期缓存示例',
            timestamp: new Date(),
            cacheInfo: '缓存时间：60秒',
            data: {
                randomValue: Math.random(),
                processedAt: new Date().toISOString(),
            }
        };
    }
    async mediumCacheExample() {
        await new Promise(resolve => setTimeout(resolve, 200));
        return {
            message: '这是中期缓存示例',
            timestamp: new Date(),
            cacheInfo: '缓存时间：5分钟',
            data: {
                expensiveCalculation: Math.pow(Math.random() * 1000, 2),
                processedAt: new Date().toISOString(),
            }
        };
    }
    async longCacheExample() {
        await new Promise(resolve => setTimeout(resolve, 500));
        return {
            message: '这是长期缓存示例',
            timestamp: new Date(),
            cacheInfo: '缓存时间：1小时',
            data: {
                staticData: '这是相对静态的数据',
                processedAt: new Date().toISOString(),
            }
        };
    }
    async userCacheExample() {
        return {
            message: '这是用户相关缓存示例',
            note: '缓存键包含用户ID，不同用户有独立缓存',
            timestamp: new Date(),
            userData: {
                preferences: '用户偏好设置',
                lastLogin: new Date().toISOString(),
            }
        };
    }
    async paramCacheExample(category = 'default') {
        await new Promise(resolve => setTimeout(resolve, 150));
        return {
            message: '这是参数相关缓存示例',
            note: '缓存键包含查询参数，不同参数有独立缓存',
            category,
            timestamp: new Date(),
            data: {
                categoryData: `${category}分类的数据`,
                processedAt: new Date().toISOString(),
            }
        };
    }
    async fullCacheExample(filter = 'all') {
        await new Promise(resolve => setTimeout(resolve, 300));
        return {
            message: '这是完整缓存示例',
            note: '缓存键包含用户ID和查询参数',
            filter,
            timestamp: new Date(),
            data: {
                filteredData: `按${filter}过滤的数据`,
                processedAt: new Date().toISOString(),
            }
        };
    }
    async staticCacheExample() {
        return {
            message: '这是静态数据缓存示例',
            note: '适用于很少变化的配置数据',
            timestamp: new Date(),
            staticData: {
                appConfig: {
                    version: '1.0.0',
                    features: ['缓存', '日志', '响应管理'],
                },
                constants: {
                    maxFileSize: '10MB',
                    supportedFormats: ['jpg', 'png', 'pdf'],
                }
            }
        };
    }
    async disabledCacheExample() {
        return {
            message: '这是禁用缓存示例',
            note: '此接口明确禁用了缓存',
            timestamp: new Date(),
            realTimeData: {
                currentTime: new Date().toISOString(),
                randomValue: Math.random(),
            }
        };
    }
    async manualCacheExample(action = 'get') {
        switch (action) {
            case 'set':
                const data = {
                    message: '手动设置的缓存数据',
                    timestamp: new Date(),
                    value: Math.random(),
                };
                const response = this.responseManager.success(data, '手动缓存数据');
                await this.cacheService.set('GET', '/manual-cache', response, undefined, undefined, 300);
                return {
                    action: 'set',
                    message: '缓存数据已设置',
                    data,
                };
            case 'get':
                const cachedData = await this.cacheService.get('GET', '/manual-cache');
                return {
                    action: 'get',
                    message: cachedData ? '获取到缓存数据' : '缓存数据不存在',
                    cached: !!cachedData,
                    data: cachedData,
                };
            case 'delete':
                const deleted = await this.cacheService.delete('GET', '/manual-cache');
                return {
                    action: 'delete',
                    message: deleted ? '缓存已删除' : '缓存不存在',
                    deleted,
                };
            case 'stats':
                const stats = await this.cacheService.getStats();
                return {
                    action: 'stats',
                    message: '缓存统计信息',
                    stats,
                };
            default:
                return {
                    message: '支持的操作：set, get, delete, stats',
                    currentAction: action,
                };
        }
    }
};
exports.ResponseExampleController = ResponseExampleController;
__decorate([
    (0, common_1.Get)('basic-success'),
    (0, swagger_1.ApiOperation)({ summary: '基础成功响应示例' }),
    (0, response_decorator_1.ApiSuccessResponse)('获取数据成功'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ResponseExampleController.prototype, "basicSuccess", null);
__decorate([
    (0, common_1.Get)('manual-success'),
    (0, swagger_1.ApiOperation)({ summary: '手动创建成功响应示例' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ResponseExampleController.prototype, "manualSuccess", null);
__decorate([
    (0, common_1.Get)('paginated'),
    (0, swagger_1.ApiOperation)({ summary: '分页响应示例' }),
    (0, response_decorator_1.ApiPaginatedResponse)('获取分页数据成功'),
    __param(0, (0, common_1.Query)('page')),
    __param(1, (0, common_1.Query)('size')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Number]),
    __metadata("design:returntype", Promise)
], ResponseExampleController.prototype, "paginatedData", null);
__decorate([
    (0, common_1.Get)('manual-paginated'),
    (0, swagger_1.ApiOperation)({ summary: '手动创建分页响应示例' }),
    __param(0, (0, common_1.Query)('page')),
    __param(1, (0, common_1.Query)('size')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Number]),
    __metadata("design:returntype", Promise)
], ResponseExampleController.prototype, "manualPaginated", null);
__decorate([
    (0, common_1.Get)('simple'),
    (0, swagger_1.ApiOperation)({ summary: '简化响应格式示例' }),
    (0, response_decorator_1.ApiSimpleResponse)('简化格式响应'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ResponseExampleController.prototype, "simpleFormat", null);
__decorate([
    (0, common_1.Get)('restful'),
    (0, swagger_1.ApiOperation)({ summary: 'RESTful响应格式示例' }),
    (0, response_decorator_1.ApiRestfulResponse)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ResponseExampleController.prototype, "restfulFormat", null);
__decorate([
    (0, common_1.Get)('custom'),
    (0, swagger_1.ApiOperation)({ summary: '自定义响应格式示例' }),
    (0, response_decorator_1.ResponseConfig)({
        format: response_interface_1.ResponseFormat.CUSTOM,
        successMessage: '自定义响应成功',
        includeExecutionTime: true,
        includeRequestId: true,
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ResponseExampleController.prototype, "customFormat", null);
__decorate([
    (0, common_1.Get)('business-error'),
    (0, swagger_1.ApiOperation)({ summary: '业务异常示例' }),
    (0, response_decorator_1.ApiResponseDocs)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ResponseExampleController.prototype, "businessError", null);
__decorate([
    (0, common_1.Post)('validation-error'),
    (0, swagger_1.ApiOperation)({ summary: '验证异常示例' }),
    (0, response_decorator_1.ApiResponseDocs)(),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], ResponseExampleController.prototype, "validationError", null);
__decorate([
    (0, common_1.Get)('http-error/:id'),
    (0, swagger_1.ApiOperation)({ summary: 'HTTP异常示例' }),
    (0, response_decorator_1.ApiResponseDocs)(),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ResponseExampleController.prototype, "httpError", null);
__decorate([
    (0, common_1.Get)('system-error'),
    (0, swagger_1.ApiOperation)({ summary: '系统异常示例' }),
    (0, response_decorator_1.ApiResponseDocs)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ResponseExampleController.prototype, "systemError", null);
__decorate([
    (0, common_1.Get)('null-data'),
    (0, swagger_1.ApiOperation)({ summary: '空数据响应示例' }),
    (0, response_decorator_1.ApiSuccessResponse)('操作成功，无返回数据'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ResponseExampleController.prototype, "nullData", null);
__decorate([
    (0, common_1.Get)('manual-error'),
    (0, swagger_1.ApiOperation)({ summary: '手动错误响应示例' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ResponseExampleController.prototype, "manualError", null);
__decorate([
    (0, common_1.Get)('full-docs'),
    (0, swagger_1.ApiOperation)({ summary: '完整API文档示例' }),
    (0, response_decorator_1.ApiResponseDocs)({
        success: {
            message: '获取完整文档数据成功',
            example: {
                id: 1,
                title: '文档标题',
                content: '文档内容',
                createTime: new Date().toISOString(),
            },
        },
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ResponseExampleController.prototype, "fullDocs", null);
__decorate([
    (0, common_1.Get)('logging-example'),
    (0, swagger_1.ApiOperation)({ summary: '日志记录示例' }),
    (0, response_decorator_1.ApiSuccessResponse)('日志记录示例成功'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ResponseExampleController.prototype, "loggingExample", null);
__decorate([
    (0, common_1.Get)('performance-test'),
    (0, swagger_1.ApiOperation)({ summary: '性能测试示例' }),
    (0, response_decorator_1.ApiSuccessResponse)('性能测试完成'),
    __param(0, (0, common_1.Query)('delay')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], ResponseExampleController.prototype, "performanceTest", null);
__decorate([
    (0, common_1.Post)('sensitive-data'),
    (0, swagger_1.ApiOperation)({ summary: '敏感数据处理示例' }),
    (0, response_decorator_1.ApiSuccessResponse)('敏感数据处理成功'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], ResponseExampleController.prototype, "sensitiveData", null);
__decorate([
    (0, common_1.Get)('cache/short'),
    (0, swagger_1.ApiOperation)({ summary: '短期缓存示例' }),
    (0, cache_decorator_1.ShortCache)(60),
    (0, response_decorator_1.ApiSuccessResponse)('短期缓存示例成功'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ResponseExampleController.prototype, "shortCacheExample", null);
__decorate([
    (0, common_1.Get)('cache/medium'),
    (0, swagger_1.ApiOperation)({ summary: '中期缓存示例' }),
    (0, cache_decorator_1.MediumCache)(300),
    (0, response_decorator_1.ApiSuccessResponse)('中期缓存示例成功'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ResponseExampleController.prototype, "mediumCacheExample", null);
__decorate([
    (0, common_1.Get)('cache/long'),
    (0, swagger_1.ApiOperation)({ summary: '长期缓存示例' }),
    (0, cache_decorator_1.LongCache)(3600),
    (0, response_decorator_1.ApiSuccessResponse)('长期缓存示例成功'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ResponseExampleController.prototype, "longCacheExample", null);
__decorate([
    (0, common_1.Get)('cache/user'),
    (0, swagger_1.ApiOperation)({ summary: '用户相关缓存示例' }),
    (0, cache_decorator_1.UserCache)(300),
    (0, response_decorator_1.ApiSuccessResponse)('用户相关缓存示例成功'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ResponseExampleController.prototype, "userCacheExample", null);
__decorate([
    (0, common_1.Get)('cache/param'),
    (0, swagger_1.ApiOperation)({ summary: '参数相关缓存示例' }),
    (0, cache_decorator_1.ParamCache)(300),
    (0, response_decorator_1.ApiSuccessResponse)('参数相关缓存示例成功'),
    __param(0, (0, common_1.Query)('category')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ResponseExampleController.prototype, "paramCacheExample", null);
__decorate([
    (0, common_1.Get)('cache/full'),
    (0, swagger_1.ApiOperation)({ summary: '完整缓存示例' }),
    (0, cache_decorator_1.FullCache)(300),
    (0, response_decorator_1.ApiSuccessResponse)('完整缓存示例成功'),
    __param(0, (0, common_1.Query)('filter')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ResponseExampleController.prototype, "fullCacheExample", null);
__decorate([
    (0, common_1.Get)('cache/static'),
    (0, swagger_1.ApiOperation)({ summary: '静态数据缓存示例' }),
    (0, cache_decorator_1.StaticCache)(86400),
    (0, response_decorator_1.ApiSuccessResponse)('静态数据缓存示例成功'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ResponseExampleController.prototype, "staticCacheExample", null);
__decorate([
    (0, common_1.Get)('cache/disabled'),
    (0, swagger_1.ApiOperation)({ summary: '禁用缓存示例' }),
    (0, cache_decorator_1.NoCache)(),
    (0, response_decorator_1.ApiSuccessResponse)('禁用缓存示例成功'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ResponseExampleController.prototype, "disabledCacheExample", null);
__decorate([
    (0, common_1.Get)('cache/manual'),
    (0, swagger_1.ApiOperation)({ summary: '手动缓存操作示例' }),
    (0, response_decorator_1.ApiSuccessResponse)('手动缓存操作示例成功'),
    __param(0, (0, common_1.Query)('action')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ResponseExampleController.prototype, "manualCacheExample", null);
exports.ResponseExampleController = ResponseExampleController = __decorate([
    (0, swagger_1.ApiTags)('响应管理器示例'),
    (0, common_1.Controller)('response-examples'),
    __metadata("design:paramtypes", [response_manager_service_1.ResponseManagerService,
        response_cache_service_1.ResponseCacheService])
], ResponseExampleController);
//# sourceMappingURL=response-example.controller.js.map