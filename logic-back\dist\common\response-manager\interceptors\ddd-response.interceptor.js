"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DddResponseInterceptor = void 0;
const common_1 = require("@nestjs/common");
const core_1 = require("@nestjs/core");
const operators_1 = require("rxjs/operators");
const response_manager_interface_1 = require("../interfaces/response-manager.interface");
const ddd_response_decorator_1 = require("../decorators/ddd-response.decorator");
let DddResponseInterceptor = class DddResponseInterceptor {
    reflector;
    responseManager;
    constructor(reflector, responseManager) {
        this.reflector = reflector;
        this.responseManager = responseManager;
    }
    intercept(context, next) {
        const config = this.reflector.get(ddd_response_decorator_1.DDD_RESPONSE_METADATA, context.getHandler());
        if (!config) {
            return next.handle();
        }
        const request = context.switchToHttp().getRequest();
        const requestContext = this.responseManager.createContext(request, config.module, config.operation);
        return next.handle().pipe((0, operators_1.switchMap)(async (result) => {
            try {
                const enhancedResponse = await this.processResult(result, requestContext, config);
                return this.formatResponse(enhancedResponse, config.format);
            }
            catch (error) {
                return this.handleError(error, requestContext, config);
            }
        }), (0, operators_1.catchError)(async (error) => {
            return this.handleError(error, requestContext, config);
        }));
    }
    async processResult(result, requestContext, config) {
        if (this.isCommandResult(result)) {
            return this.responseManager.handleCommandResult(result, requestContext, config.format);
        }
        if (this.isQueryResult(result)) {
            return this.responseManager.handleQueryResult(result, requestContext, config.format);
        }
        return this.handlePlainResult(result, requestContext, config);
    }
    async handlePlainResult(result, requestContext, config) {
        const commandResult = {
            success: true,
            data: result,
            message: '操作成功',
            timestamp: new Date(),
            executionTime: Date.now() - requestContext.startTime
        };
        return this.responseManager.handleCommandResult(commandResult, requestContext, config.format);
    }
    formatResponse(enhancedResponse, format) {
        switch (format) {
            case response_manager_interface_1.ResponseFormat.HTTP:
                return this.responseManager.toHttpResponse(enhancedResponse);
            case response_manager_interface_1.ResponseFormat.COMMAND:
                return {
                    success: enhancedResponse.success,
                    data: enhancedResponse.data,
                    message: enhancedResponse.message,
                    errors: enhancedResponse.errors,
                    timestamp: enhancedResponse.timestamp,
                    executionTime: enhancedResponse.metrics.executionTime
                };
            case response_manager_interface_1.ResponseFormat.QUERY:
                return {
                    success: enhancedResponse.success,
                    data: enhancedResponse.data,
                    message: enhancedResponse.message,
                    errors: enhancedResponse.errors,
                    timestamp: enhancedResponse.timestamp,
                    executionTime: enhancedResponse.metrics.executionTime,
                    fromCache: enhancedResponse.fromCache,
                    cacheKey: enhancedResponse.cacheKey
                };
            case response_manager_interface_1.ResponseFormat.ENHANCED:
                return enhancedResponse;
            case response_manager_interface_1.ResponseFormat.API:
                return {
                    success: enhancedResponse.success,
                    data: enhancedResponse.data,
                    message: enhancedResponse.message
                };
            default:
                return this.responseManager.toHttpResponse(enhancedResponse);
        }
    }
    async handleError(error, requestContext, config) {
        const errorResult = {
            success: false,
            message: error.message || '系统错误',
            errors: [error.message || '未知错误'],
            timestamp: new Date(),
            executionTime: Date.now() - requestContext.startTime
        };
        const enhancedResponse = await this.responseManager.handleCommandResult(errorResult, requestContext, config.format);
        return this.formatResponse(enhancedResponse, config.format);
    }
    isCommandResult(result) {
        return (result &&
            typeof result === 'object' &&
            'success' in result &&
            'timestamp' in result &&
            typeof result.success === 'boolean');
    }
    isQueryResult(result) {
        return (result &&
            typeof result === 'object' &&
            'success' in result &&
            'timestamp' in result &&
            typeof result.success === 'boolean' &&
            ('fromCache' in result || 'cacheKey' in result));
    }
    isHttpResponse(result) {
        return (result &&
            typeof result === 'object' &&
            'code' in result &&
            'msg' in result &&
            'data' in result);
    }
};
exports.DddResponseInterceptor = DddResponseInterceptor;
exports.DddResponseInterceptor = DddResponseInterceptor = __decorate([
    (0, common_1.Injectable)(),
    __param(1, (0, common_1.Inject)('RESPONSE_MANAGER')),
    __metadata("design:paramtypes", [core_1.Reflector, Object])
], DddResponseInterceptor);
//# sourceMappingURL=ddd-response.interceptor.js.map