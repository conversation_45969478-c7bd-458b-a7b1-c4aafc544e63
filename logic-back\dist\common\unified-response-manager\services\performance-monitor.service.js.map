{"version": 3, "file": "performance-monitor.service.js", "sourceRoot": "", "sources": ["../../../../src/common/unified-response-manager/services/performance-monitor.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAAoD;AACpD,mCAAmC;AAOnC,gEAA4D;AAoBrD,IAAM,yBAAyB,GAA/B,MAAM,yBAAyB;IAKjB;IACmC;IALrC,cAAc,GAAG,IAAI,GAAG,EAA0B,CAAC;IACnD,cAAc,GAAG,IAAI,CAAC;IAEvC,YACmB,MAAqB,EACc,MAA6B;QADhE,WAAM,GAAN,MAAM,CAAe;QACc,WAAM,GAAN,MAAM,CAAuB;QAGjF,IAAI,CAAC,iBAAiB,EAAE,CAAC;IAC3B,CAAC;IAKD,eAAe,CAAC,SAAiB;QAC/B,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE,CAAC;YAC/B,OAAO;QACT,CAAC;QAED,MAAM,cAAc,GAAmB;YACrC,SAAS;YACT,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;YACrB,WAAW,EAAE,OAAO,CAAC,WAAW,EAAE;YAClC,YAAY,EAAE,CAAC;YACf,SAAS,EAAE,CAAC;YACZ,WAAW,EAAE,CAAC;SACf,CAAC;QAEF,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,SAAS,EAAE,cAAc,CAAC,CAAC;QAEnD,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,WAAW,SAAS,EAAE,EAAE,oBAAoB,CAAC,CAAC;IAClE,CAAC;IAKD,aAAa,CAAC,SAAiB;QAC7B,MAAM,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QAEhD,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE,CAAC;YACxC,OAAO,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAClC,CAAC;QAED,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC3B,MAAM,SAAS,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;QAExC,MAAM,OAAO,GAAuB;YAClC,aAAa,EAAE,OAAO,GAAG,IAAI,CAAC,SAAS;YACvC,WAAW,EAAE,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,WAAW,EAAE,SAAS,CAAC;YACnE,YAAY,EAAE,IAAI,CAAC,YAAY;YAC/B,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,aAAa,EAAE,IAAI,CAAC,aAAa,IAAI,CAAC;SACvC,CAAC;QAGF,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;QAGtC,IAAI,CAAC,qBAAqB,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;QAE/C,OAAO,OAAO,CAAC;IACjB,CAAC;IAKD,aAAa,CAAC,SAAiB;QAC7B,MAAM,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QAChD,IAAI,IAAI,EAAE,CAAC;YACT,IAAI,CAAC,YAAY,EAAE,CAAC;QACtB,CAAC;IACH,CAAC;IAKD,oBAAoB,CAAC,SAAiB,EAAE,GAAY;QAClD,MAAM,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QAChD,IAAI,IAAI,EAAE,CAAC;YACT,IAAI,GAAG,EAAE,CAAC;gBACR,IAAI,CAAC,SAAS,EAAE,CAAC;YACnB,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,WAAW,EAAE,CAAC;YACrB,CAAC;QACH,CAAC;IACH,CAAC;IAKD,mBAAmB,CAAC,SAAiB,EAAE,QAAgB;QACrD,MAAM,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QAChD,IAAI,IAAI,EAAE,CAAC;YACT,IAAI,CAAC,aAAa,GAAG,QAAQ,CAAC;QAChC,CAAC;IACH,CAAC;IAKD,wBAAwB;QACtB,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC;IAClC,CAAC;IAKD,gBAAgB;QAKd,OAAO;YACL,WAAW,EAAE,OAAO,CAAC,WAAW,EAAE;YAClC,MAAM,EAAE,OAAO,CAAC,MAAM,EAAE;YACxB,gBAAgB,EAAE,IAAI,CAAC,cAAc,CAAC,IAAI;SAC3C,CAAC;IACJ,CAAC;IAKO,oBAAoB,CAC1B,WAA+B,EAC/B,SAA6B;QAG7B,MAAM,YAAY,GAAG,SAAS,CAAC,QAAQ,GAAG,WAAW,CAAC,QAAQ,CAAC;QAC/D,MAAM,YAAY,GAAG,SAAS,CAAC,QAAQ,GAAG,WAAW,CAAC,QAAQ,CAAC;QAE/D,OAAO,YAAY,GAAG,YAAY,CAAC;IACrC,CAAC;IAKO,iBAAiB;QACvB,OAAO;YACL,aAAa,EAAE,CAAC;YAChB,WAAW,EAAE,CAAC;YACd,YAAY,EAAE,CAAC;YACf,SAAS,EAAE,CAAC;YACZ,WAAW,EAAE,CAAC;YACd,aAAa,EAAE,CAAC;SACjB,CAAC;IACJ,CAAC;IAKO,qBAAqB,CAAC,SAAiB,EAAE,OAA2B;QAC1E,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,qBAAqB,EAAE,CAAC;YACvC,OAAO;QACT,CAAC;QAED,MAAM,OAAO,GAAG;YACd,SAAS;YACT,aAAa,EAAE,OAAO,CAAC,aAAa;YACpC,WAAW,EAAE,OAAO,CAAC,WAAW;YAChC,YAAY,EAAE,OAAO,CAAC,YAAY;YAClC,SAAS,EAAE,OAAO,CAAC,SAAS;YAC5B,WAAW,EAAE,OAAO,CAAC,WAAW;YAChC,aAAa,EAAE,OAAO,CAAC,aAAa;YACpC,YAAY,EAAE,CAAC,OAAO,CAAC,SAAS,IAAI,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,WAAW,IAAI,CAAC,CAAC,GAAG,CAAC;gBACrE,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,SAAS,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,SAAS,IAAI,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,WAAW,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG;gBAC5F,CAAC,CAAC,CAAC;SACN,CAAC;QAGF,IAAI,QAAQ,GAA8B,MAAM,CAAC;QAEjD,IAAI,OAAO,CAAC,aAAa,GAAG,IAAI,EAAE,CAAC;YACjC,QAAQ,GAAG,OAAO,CAAC;QACrB,CAAC;aAAM,IAAI,OAAO,CAAC,aAAa,GAAG,IAAI,EAAE,CAAC;YACxC,QAAQ,GAAG,MAAM,CAAC;QACpB,CAAC;QAED,MAAM,OAAO,GAAG,SAAS,SAAS,aAAa,OAAO,CAAC,aAAa,WAAW,CAAC,CAAC,OAAO,CAAC,WAAW,IAAI,CAAC,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,aAAa,OAAO,CAAC,YAAY,IAAI,CAAC,GAAG,CAAC;QAE9K,QAAQ,QAAQ,EAAE,CAAC;YACjB,KAAK,MAAM;gBACT,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC;gBAClD,MAAM;YACR,KAAK,MAAM;gBACT,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC;gBACnD,MAAM;YACR,KAAK,OAAO;gBACV,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC;gBACpD,MAAM;QACV,CAAC;IACH,CAAC;IAKO,iBAAiB;QACvB,WAAW,CAAC,GAAG,EAAE;YACf,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAChC,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,eAAe,IAAI,MAAM,CAAC,CAAC;IAC5C,CAAC;IAKO,sBAAsB;QAC5B,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACvB,MAAM,cAAc,GAAG,MAAM,CAAC;QAE9B,IAAI,YAAY,GAAG,CAAC,CAAC;QACrB,KAAK,MAAM,CAAC,SAAS,EAAE,IAAI,CAAC,IAAI,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,EAAE,CAAC;YAC9D,IAAI,GAAG,GAAG,IAAI,CAAC,SAAS,GAAG,cAAc,EAAE,CAAC;gBAC1C,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;gBACtC,YAAY,EAAE,CAAC;YACjB,CAAC;QACH,CAAC;QAED,IAAI,YAAY,GAAG,CAAC,EAAE,CAAC;YACrB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,YAAY,aAAa,EAAE,oBAAoB,CAAC,CAAC;QAC3E,CAAC;QAGD,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;YACnD,MAAM,aAAa,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,CAAC;iBAC5D,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,SAAS,CAAC,CAAC;YAErD,MAAM,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC;YAChE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,EAAE,CAAC,EAAE,EAAE,CAAC;gBAClC,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAClD,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,QAAQ,aAAa,EAAE,oBAAoB,CAAC,CAAC;QACvE,CAAC;IACH,CAAC;CACF,CAAA;AA3OY,8DAAyB;oCAAzB,yBAAyB;IADrC,IAAA,mBAAU,GAAE;IAOR,WAAA,IAAA,eAAM,EAAC,yBAAyB,CAAC,CAAA;qCADT,8BAAa;GAL7B,yBAAyB,CA2OrC"}