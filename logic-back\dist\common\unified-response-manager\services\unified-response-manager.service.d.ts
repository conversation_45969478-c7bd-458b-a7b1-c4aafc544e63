import { IUnifiedResponseManager, RequestContext, UnifiedResponse, UnifiedResponseConfig, IRequestPool, IPerformanceMonitor, IResponseTypeDetector } from '../interfaces/unified-response.interface';
import { HttpResponse } from '../../../web/http_response_result/http-response.interface';
import { LoggerService } from '../../logger/logger.service';
export declare class UnifiedResponseManagerService implements IUnifiedResponseManager {
    private readonly logger;
    private readonly requestPool;
    private readonly performanceMonitor;
    private readonly typeDetector;
    private readonly config;
    constructor(logger: LoggerService, requestPool: IRequestPool, performanceMonitor: IPerformanceMonitor, typeDetector: IResponseTypeDetector, config: UnifiedResponseConfig);
    handleResponse<T>(result: any, context: Partial<RequestContext>, options?: {
        usePool?: boolean;
        priority?: number;
        timeout?: number;
    }): Promise<HttpResponse<T>>;
    handleError(error: any, context: Partial<RequestContext>): Promise<HttpResponse<never>>;
    createContext(request: any, controller?: string, action?: string): RequestContext;
    getPoolStatus(): Promise<import("../interfaces/unified-response.interface").RequestPoolStatus>;
    logResponse<T>(unifiedResponse: UnifiedResponse<T>, level?: 'info' | 'warn' | 'error'): void;
    private processWithPool;
    private processDirectly;
    private createUnifiedResponse;
    private toHttpResponse;
    private generateRequestId;
    private enrichContext;
    private sanitizeHeaders;
}
