import { RedisService } from '../../../util/database/redis/redis.service';
import { LoggerService } from '../../logger/logger.service';
import { BaseResponse } from '../interfaces/response.interface';
export interface CacheConfig {
    prefix?: string;
    defaultTtl?: number;
    enabled?: boolean;
    compress?: boolean;
    maxSize?: number;
}
export interface CacheKeyGenerator {
    (method: string, path: string, params?: any, userId?: string | number): string;
}
export declare class ResponseCacheService {
    private readonly redisService;
    private readonly loggerService;
    private readonly defaultConfig;
    constructor(redisService: RedisService, loggerService: LoggerService);
    private generateCacheKey;
    private hashObject;
    private compressData;
    private decompressData;
    private serializeResponse;
    private deserializeResponse;
    private checkDataSize;
    get(method: string, path: string, params?: any, userId?: string | number, config?: CacheConfig): Promise<BaseResponse | null>;
    set(method: string, path: string, response: BaseResponse, params?: any, userId?: string | number, ttl?: number, config?: CacheConfig): Promise<boolean>;
    delete(method: string, path: string, params?: any, userId?: string | number, config?: CacheConfig): Promise<boolean>;
    deleteByPattern(pattern: string, config?: CacheConfig): Promise<number>;
    exists(method: string, path: string, params?: any, userId?: string | number, config?: CacheConfig): Promise<boolean>;
    getTtl(method: string, path: string, params?: any, userId?: string | number, config?: CacheConfig): Promise<number>;
    getStats(config?: CacheConfig): Promise<{
        totalKeys: number;
        totalSize: number;
        avgTtl: number;
    }>;
    clear(config?: CacheConfig): Promise<number>;
}
