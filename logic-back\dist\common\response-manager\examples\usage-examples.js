"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DddResponseExampleController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const ddd_response_decorator_1 = require("../decorators/ddd-response.decorator");
const response_manager_interface_1 = require("../interfaces/response-manager.interface");
let DddResponseExampleController = class DddResponseExampleController {
    responseManager;
    constructor(responseManager) {
        this.responseManager = responseManager;
    }
    async createUser(command) {
        const user = {
            id: `user_${Date.now()}`,
            name: command.name,
            email: command.email,
            age: command.age,
            createdAt: new Date(),
            updatedAt: new Date()
        };
        return {
            success: true,
            data: user,
            message: '用户创建成功',
            timestamp: new Date(),
            executionTime: 150
        };
    }
    async getUser(id) {
        const user = {
            id,
            name: '张三',
            email: '<EMAIL>',
            age: 25,
            createdAt: new Date('2024-01-01'),
            updatedAt: new Date()
        };
        return {
            success: true,
            data: user,
            message: '用户获取成功',
            timestamp: new Date(),
            executionTime: 50,
            fromCache: true,
            cacheKey: `user:${id}`
        };
    }
    async getUserProfile(id) {
        const profile = {
            user: {
                id,
                name: '张三',
                email: '<EMAIL>',
                age: 25
            },
            preferences: {
                theme: 'dark',
                language: 'zh-CN'
            },
            statistics: {
                loginCount: 156,
                lastLoginAt: new Date()
            }
        };
        return {
            success: true,
            data: profile,
            message: '用户详情获取成功',
            timestamp: new Date(),
            executionTime: 200
        };
    }
    async batchCreateUsers(commands) {
        const users = commands.map((command, index) => ({
            id: `user_${Date.now()}_${index}`,
            name: command.name,
            email: command.email,
            age: command.age,
            createdAt: new Date(),
            updatedAt: new Date()
        }));
        return {
            success: true,
            data: users,
            message: `成功创建 ${users.length} 个用户`,
            timestamp: new Date(),
            executionTime: commands.length * 50
        };
    }
    async getUserList() {
        const users = [
            {
                id: 'user_1',
                name: '张三',
                email: '<EMAIL>',
                age: 25,
                createdAt: new Date('2024-01-01'),
                updatedAt: new Date()
            },
            {
                id: 'user_2',
                name: '李四',
                email: '<EMAIL>',
                age: 30,
                createdAt: new Date('2024-01-02'),
                updatedAt: new Date()
            }
        ];
        return {
            success: true,
            data: users,
            message: '用户列表获取成功',
            timestamp: new Date(),
            executionTime: 100,
            fromCache: false,
            cacheKey: 'user_list:all'
        };
    }
    async updateUserStatus(id, body) {
        return {
            success: true,
            data: { id, status: body.status },
            message: '用户状态更新成功',
            timestamp: new Date(),
            executionTime: 25
        };
    }
    async exportUsers() {
        const taskId = `export_${Date.now()}`;
        return {
            success: true,
            data: {
                taskId,
                estimatedTime: 300000
            },
            message: '导出任务已启动',
            timestamp: new Date(),
            executionTime: 100
        };
    }
    async manualResponseHandling(id, updateCommand) {
        const context = this.responseManager.createContext({ user: { id: 'current_user' } }, 'User', 'ManualUpdate');
        const commandResult = {
            success: true,
            data: {
                id,
                name: updateCommand.name || '张三',
                email: updateCommand.email || '<EMAIL>',
                age: updateCommand.age || 25,
                createdAt: new Date('2024-01-01'),
                updatedAt: new Date()
            },
            message: '用户更新成功',
            timestamp: new Date(),
            executionTime: 120
        };
        const enhancedResponse = await this.responseManager.handleCommandResult(commandResult, context, response_manager_interface_1.ResponseFormat.HTTP);
        return this.responseManager.toHttpResponse(enhancedResponse);
    }
    async errorExample() {
        return {
            success: false,
            message: '用户创建失败',
            errors: ['邮箱已存在', '用户名不符合规范'],
            timestamp: new Date(),
            executionTime: 50
        };
    }
    async getPoolStatus() {
        return await this.responseManager.getPoolStatus();
    }
};
exports.DddResponseExampleController = DddResponseExampleController;
__decorate([
    (0, common_1.Post)('users'),
    (0, swagger_1.ApiOperation)({ summary: '创建用户 - 基本命令响应示例' }),
    (0, ddd_response_decorator_1.DddCommandResponse)({
        module: 'User',
        operation: 'CreateUser',
        enableMetrics: true
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], DddResponseExampleController.prototype, "createUser", null);
__decorate([
    (0, common_1.Get)('users/:id'),
    (0, swagger_1.ApiOperation)({ summary: '获取用户 - 基本查询响应示例' }),
    (0, ddd_response_decorator_1.DddQueryResponse)({
        module: 'User',
        operation: 'GetUser',
        enableMetrics: true
    }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], DddResponseExampleController.prototype, "getUser", null);
__decorate([
    (0, common_1.Get)('users/:id/profile'),
    (0, swagger_1.ApiOperation)({ summary: '获取用户详情 - 增强响应示例' }),
    (0, ddd_response_decorator_1.DddEnhancedResponse)({
        module: 'User',
        operation: 'GetUserProfile',
        enableDetailedLogging: true
    }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], DddResponseExampleController.prototype, "getUserProfile", null);
__decorate([
    (0, common_1.Post)('users/batch'),
    (0, swagger_1.ApiOperation)({ summary: '批量创建用户 - 高性能响应示例' }),
    (0, ddd_response_decorator_1.DddHighPerformanceResponse)({
        module: 'User',
        operation: 'BatchCreateUsers',
        priority: 1,
        timeout: 60000
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array]),
    __metadata("design:returntype", Promise)
], DddResponseExampleController.prototype, "batchCreateUsers", null);
__decorate([
    (0, common_1.Get)('users'),
    (0, swagger_1.ApiOperation)({ summary: '获取用户列表 - 缓存响应示例' }),
    (0, ddd_response_decorator_1.DddCachedResponse)({
        module: 'User',
        operation: 'GetUserList',
        cacheTTL: 600,
        cacheKeyPrefix: 'user_list'
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], DddResponseExampleController.prototype, "getUserList", null);
__decorate([
    (0, common_1.Post)('users/:id/status'),
    (0, swagger_1.ApiOperation)({ summary: '更新用户状态 - 实时响应示例' }),
    (0, ddd_response_decorator_1.DddRealtimeResponse)({
        module: 'User',
        operation: 'UpdateUserStatus'
    }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], DddResponseExampleController.prototype, "updateUserStatus", null);
__decorate([
    (0, common_1.Post)('users/export'),
    (0, swagger_1.ApiOperation)({ summary: '导出用户数据 - 异步响应示例' }),
    (0, ddd_response_decorator_1.DddAsyncResponse)({
        module: 'User',
        operation: 'ExportUsers',
        priority: -1
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], DddResponseExampleController.prototype, "exportUsers", null);
__decorate([
    (0, common_1.Post)('users/:id/manual'),
    (0, swagger_1.ApiOperation)({ summary: '手动使用响应管理器示例' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], DddResponseExampleController.prototype, "manualResponseHandling", null);
__decorate([
    (0, common_1.Post)('users/error-example'),
    (0, swagger_1.ApiOperation)({ summary: '错误处理示例' }),
    (0, ddd_response_decorator_1.DddCommandResponse)({
        module: 'User',
        operation: 'ErrorExample'
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], DddResponseExampleController.prototype, "errorExample", null);
__decorate([
    (0, common_1.Get)('pool/status'),
    (0, swagger_1.ApiOperation)({ summary: '获取请求池状态' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], DddResponseExampleController.prototype, "getPoolStatus", null);
exports.DddResponseExampleController = DddResponseExampleController = __decorate([
    (0, swagger_1.ApiTags)('DDD响应管理器示例'),
    (0, common_1.Controller)('examples/ddd-response'),
    __param(0, (0, common_1.Inject)('RESPONSE_MANAGER')),
    __metadata("design:paramtypes", [Object])
], DddResponseExampleController);
//# sourceMappingURL=usage-examples.js.map