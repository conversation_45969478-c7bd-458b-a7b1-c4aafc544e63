import { NestInterceptor, ExecutionContext, CallHandler } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { Observable } from 'rxjs';
import { ResponseManagerService } from '../services/response-manager.service';
export declare class ResponseInterceptor implements NestInterceptor {
    private readonly responseManager;
    private readonly reflector;
    constructor(responseManager: ResponseManagerService, reflector: Reflector);
    intercept(context: ExecutionContext, next: CallHandler): Observable<any>;
    private isStandardResponse;
    private isPaginatedData;
    private generateRequestId;
}
export declare class GlobalResponseInterceptor implements NestInterceptor {
    private readonly responseManager;
    constructor(responseManager: ResponseManagerService);
    intercept(context: ExecutionContext, next: CallHandler): Observable<any>;
    private isStandardResponse;
    private generateRequestId;
}
