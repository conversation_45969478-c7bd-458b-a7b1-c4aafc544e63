import { NestInterceptor, ExecutionContext, CallHandler } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { Observable } from 'rxjs';
import { LoggerService } from '../../logger/logger.service';
import { ResponseManagerService } from '../services/response-manager.service';
export declare class ResponseInterceptor implements NestInterceptor {
    private readonly responseManager;
    private readonly reflector;
    private readonly loggerService;
    constructor(responseManager: ResponseManagerService, reflector: Reflector, loggerService: LoggerService);
    intercept(context: ExecutionContext, next: CallHandler): Observable<any>;
    private isStandardResponse;
    private isPaginatedData;
    private generateRequestId;
}
export declare class GlobalResponseInterceptor implements NestInterceptor {
    private readonly responseManager;
    private readonly loggerService;
    constructor(responseManager: ResponseManagerService, loggerService: LoggerService);
    intercept(context: ExecutionContext, next: Call<PERSON>and<PERSON>): Observable<any>;
    private isStandardResponse;
    private generateRequestId;
}
