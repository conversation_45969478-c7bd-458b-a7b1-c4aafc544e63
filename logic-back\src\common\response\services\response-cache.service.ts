import { Injectable } from '@nestjs/common';
import { RedisService } from '../../../util/database/redis/redis.service';
import { LoggerService } from '../../logger/logger.service';
import { ResponseLoggingUtils } from '../config/response-logging.config';
import { BaseResponse } from '../interfaces/response.interface';

/**
 * 缓存配置接口
 */
export interface CacheConfig {
  /** 缓存键前缀 */
  prefix?: string;
  /** 默认TTL（秒） */
  defaultTtl?: number;
  /** 是否启用缓存 */
  enabled?: boolean;
  /** 是否压缩数据 */
  compress?: boolean;
  /** 最大缓存大小（字节） */
  maxSize?: number;
}

/**
 * 缓存键生成器接口
 */
export interface CacheKeyGenerator {
  (method: string, path: string, params?: any, userId?: string | number): string;
}

/**
 * 响应缓存服务
 * 提供基于Redis的响应缓存功能
 */
@Injectable()
export class ResponseCacheService {
  private readonly defaultConfig: CacheConfig = {
    prefix: 'response_cache',
    defaultTtl: 300, // 5分钟
    enabled: true,
    compress: false,
    maxSize: 1024 * 1024, // 1MB
  };

  constructor(
    private readonly redisService: RedisService,
    private readonly loggerService: LoggerService,
  ) {}

  /**
   * 生成缓存键
   */
  private generateCacheKey(
    method: string,
    path: string,
    params?: any,
    userId?: string | number,
    config?: CacheConfig
  ): string {
    const prefix = config?.prefix || this.defaultConfig.prefix;
    const paramsHash = params ? this.hashObject(params) : '';
    const userPart = userId ? `_user:${userId}` : '';
    
    return `${prefix}:${method}:${path}${userPart}${paramsHash ? `_${paramsHash}` : ''}`;
  }

  /**
   * 对象哈希
   */
  private hashObject(obj: any): string {
    const str = JSON.stringify(obj, Object.keys(obj).sort());
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32bit integer
    }
    return Math.abs(hash).toString(36);
  }

  /**
   * 压缩数据
   */
  private compressData(data: string): string {
    // 简单的压缩实现，实际项目中可以使用更好的压缩算法
    return data;
  }

  /**
   * 解压数据
   */
  private decompressData(data: string): string {
    return data;
  }

  /**
   * 序列化响应数据
   */
  private serializeResponse(response: BaseResponse): string {
    return JSON.stringify({
      ...response,
      cached: true,
      cachedAt: new Date().toISOString(),
    });
  }

  /**
   * 反序列化响应数据
   */
  private deserializeResponse(data: string): BaseResponse | null {
    try {
      return JSON.parse(data);
    } catch (error) {
      this.loggerService.error('Failed to deserialize cached response', error.stack, 'ResponseCacheService');
      return null;
    }
  }

  /**
   * 检查数据大小
   */
  private checkDataSize(data: string, config?: CacheConfig): boolean {
    const maxSize = config?.maxSize || this.defaultConfig.maxSize;
    const dataSize = Buffer.byteLength(data, 'utf8');
    
    if (dataSize > maxSize!) {
      this.loggerService.warn(`Response data too large for cache: ${dataSize} bytes > ${maxSize} bytes`, 'ResponseCacheService');
      return false;
    }
    
    return true;
  }

  /**
   * 获取缓存的响应
   */
  async get(
    method: string,
    path: string,
    params?: any,
    userId?: string | number,
    config?: CacheConfig
  ): Promise<BaseResponse | null> {
    const finalConfig = { ...this.defaultConfig, ...config };
    
    if (!finalConfig.enabled) {
      return null;
    }

    try {
      const cacheKey = this.generateCacheKey(method, path, params, userId, finalConfig);
      const cachedData = await this.redisService.get(cacheKey);
      
      if (!cachedData) {
        // 记录缓存未命中
        if (ResponseLoggingUtils.getConfig().logCacheOperations) {
          this.loggerService.logResponseCache({
            action: 'miss',
            cacheKey,
          });
        }
        return null;
      }

      // 解压和反序列化数据
      const decompressedData = finalConfig.compress ? this.decompressData(cachedData) : cachedData;
      const response = this.deserializeResponse(decompressedData);
      
      if (response) {
        // 记录缓存命中
        if (ResponseLoggingUtils.getConfig().logCacheOperations) {
          this.loggerService.logResponseCache({
            action: 'hit',
            cacheKey,
            size: Buffer.byteLength(cachedData, 'utf8'),
          });
        }
      }
      
      return response;
    } catch (error) {
      this.loggerService.error('Failed to get cached response', error.stack, 'ResponseCacheService');
      return null;
    }
  }

  /**
   * 设置响应缓存
   */
  async set(
    method: string,
    path: string,
    response: BaseResponse,
    params?: any,
    userId?: string | number,
    ttl?: number,
    config?: CacheConfig
  ): Promise<boolean> {
    const finalConfig = { ...this.defaultConfig, ...config };
    
    if (!finalConfig.enabled) {
      return false;
    }

    try {
      const cacheKey = this.generateCacheKey(method, path, params, userId, finalConfig);
      const serializedData = this.serializeResponse(response);
      
      // 检查数据大小
      if (!this.checkDataSize(serializedData, finalConfig)) {
        return false;
      }
      
      // 压缩数据
      const finalData = finalConfig.compress ? this.compressData(serializedData) : serializedData;
      const finalTtl = ttl || finalConfig.defaultTtl!;
      
      await this.redisService.set(cacheKey, finalData, finalTtl);
      
      // 记录缓存设置
      if (ResponseLoggingUtils.getConfig().logCacheOperations) {
        this.loggerService.logResponseCache({
          action: 'set',
          cacheKey,
          ttl: finalTtl,
          size: Buffer.byteLength(finalData, 'utf8'),
        });
      }
      
      return true;
    } catch (error) {
      this.loggerService.error('Failed to set response cache', error.stack, 'ResponseCacheService');
      return false;
    }
  }

  /**
   * 删除缓存
   */
  async delete(
    method: string,
    path: string,
    params?: any,
    userId?: string | number,
    config?: CacheConfig
  ): Promise<boolean> {
    const finalConfig = { ...this.defaultConfig, ...config };
    
    try {
      const cacheKey = this.generateCacheKey(method, path, params, userId, finalConfig);
      const result = await this.redisService.del(cacheKey);
      
      // 记录缓存删除
      if (ResponseLoggingUtils.getConfig().logCacheOperations) {
        this.loggerService.logResponseCache({
          action: 'invalidate',
          cacheKey,
        });
      }
      
      return result > 0;
    } catch (error) {
      this.loggerService.error('Failed to delete response cache', error.stack, 'ResponseCacheService');
      return false;
    }
  }

  /**
   * 批量删除缓存（按模式）
   */
  async deleteByPattern(pattern: string, config?: CacheConfig): Promise<number> {
    const finalConfig = { ...this.defaultConfig, ...config };
    const prefix = finalConfig.prefix || this.defaultConfig.prefix;
    const fullPattern = `${prefix}:${pattern}`;
    
    try {
      const keys = await this.redisService.keys(fullPattern);
      
      if (keys.length === 0) {
        return 0;
      }
      
      // 批量删除
      const pipeline = this.redisService.getClient().pipeline();
      keys.forEach(key => pipeline.del(key));
      await pipeline.exec();
      
      // 记录批量删除
      if (ResponseLoggingUtils.getConfig().logCacheOperations) {
        this.loggerService.logResponseCache({
          action: 'invalidate',
          cacheKey: fullPattern,
        });
      }
      
      return keys.length;
    } catch (error) {
      this.loggerService.error('Failed to delete cache by pattern', error.stack, 'ResponseCacheService');
      return 0;
    }
  }

  /**
   * 检查缓存是否存在
   */
  async exists(
    method: string,
    path: string,
    params?: any,
    userId?: string | number,
    config?: CacheConfig
  ): Promise<boolean> {
    const finalConfig = { ...this.defaultConfig, ...config };
    
    try {
      const cacheKey = this.generateCacheKey(method, path, params, userId, finalConfig);
      const result = await this.redisService.exists(cacheKey);
      return result > 0;
    } catch (error) {
      this.loggerService.error('Failed to check cache existence', error.stack, 'ResponseCacheService');
      return false;
    }
  }

  /**
   * 获取缓存TTL
   */
  async getTtl(
    method: string,
    path: string,
    params?: any,
    userId?: string | number,
    config?: CacheConfig
  ): Promise<number> {
    const finalConfig = { ...this.defaultConfig, ...config };
    
    try {
      const cacheKey = this.generateCacheKey(method, path, params, userId, finalConfig);
      return await this.redisService.ttl(cacheKey);
    } catch (error) {
      this.loggerService.error('Failed to get cache TTL', error.stack, 'ResponseCacheService');
      return -1;
    }
  }

  /**
   * 获取缓存统计信息
   */
  async getStats(config?: CacheConfig): Promise<{
    totalKeys: number;
    totalSize: number;
    avgTtl: number;
  }> {
    const finalConfig = { ...this.defaultConfig, ...config };
    const prefix = finalConfig.prefix || this.defaultConfig.prefix;
    
    try {
      const keys = await this.redisService.keys(`${prefix}:*`);
      let totalSize = 0;
      let totalTtl = 0;
      let validTtlCount = 0;
      
      for (const key of keys) {
        // 获取键的大小（近似）
        const value = await this.redisService.get(key);
        if (value) {
          totalSize += Buffer.byteLength(value, 'utf8');
        }
        
        // 获取TTL
        const ttl = await this.redisService.ttl(key);
        if (ttl > 0) {
          totalTtl += ttl;
          validTtlCount++;
        }
      }
      
      return {
        totalKeys: keys.length,
        totalSize,
        avgTtl: validTtlCount > 0 ? Math.round(totalTtl / validTtlCount) : 0,
      };
    } catch (error) {
      this.loggerService.error('Failed to get cache stats', error.stack, 'ResponseCacheService');
      return {
        totalKeys: 0,
        totalSize: 0,
        avgTtl: 0,
      };
    }
  }

  /**
   * 清空所有缓存
   */
  async clear(config?: CacheConfig): Promise<number> {
    const finalConfig = { ...this.defaultConfig, ...config };
    const prefix = finalConfig.prefix || this.defaultConfig.prefix;
    
    return await this.deleteByPattern('*', finalConfig);
  }
}
