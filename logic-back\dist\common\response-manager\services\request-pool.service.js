"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RequestPoolService = void 0;
const common_1 = require("@nestjs/common");
const ioredis_1 = require("ioredis");
const bullmq_1 = require("bullmq");
const logger_service_1 = require("../../logger/logger.service");
let RequestPoolService = class RequestPoolService {
    logger;
    config;
    queue;
    worker;
    redis;
    activeTasks = new Map();
    completedTasks = new Map();
    maxHistorySize = 1000;
    constructor(logger, config) {
        this.logger = logger;
        this.config = config;
        this.redis = new ioredis_1.Redis({
            host: config.redis.host,
            port: config.redis.port,
            password: config.redis.password,
            db: config.redis.db,
            retryDelayOnFailover: 100,
            maxRetriesPerRequest: 3
        });
        this.queue = new bullmq_1.Queue('response-manager-queue', {
            connection: this.redis,
            defaultJobOptions: {
                removeOnComplete: 100,
                removeOnFail: 50,
                attempts: 3,
                backoff: {
                    type: 'exponential',
                    delay: 2000,
                },
            },
        });
        this.worker = new bullmq_1.Worker('response-manager-queue', this.processJob.bind(this), {
            connection: this.redis,
            concurrency: config.maxConcurrency,
            limiter: {
                max: config.maxConcurrency,
                duration: 1000,
            },
        });
        this.setupEventListeners();
        this.startCleanupTimer();
    }
    async enqueue(requestId, handler, priority = 0, timeout = this.config.requestTimeout) {
        const task = {
            requestId,
            handler,
            priority,
            timeout,
            createdAt: Date.now()
        };
        if (this.config.enableRequestPool) {
            return this.enqueueWithPool(task);
        }
        else {
            return this.executeTask(task);
        }
    }
    async getPoolStatus() {
        const waiting = await this.queue.getWaiting();
        const active = await this.queue.getActive();
        const completed = await this.queue.getCompleted();
        const failed = await this.queue.getFailed();
        const { averageWaitTime, averageProcessingTime } = this.calculateAverages();
        const totalCompleted = completed.length + failed.length;
        const successRate = totalCompleted > 0 ? (completed.length / totalCompleted) * 100 : 100;
        return {
            queueSize: waiting.length,
            processingCount: active.length,
            maxConcurrency: this.config.maxConcurrency,
            averageWaitTime,
            averageProcessingTime,
            successRate
        };
    }
    async cleanup() {
        const now = Date.now();
        const expiredThreshold = now - (this.config.requestTimeout * 2);
        for (const [requestId, task] of this.completedTasks.entries()) {
            if (task.completedAt && task.completedAt < expiredThreshold) {
                this.completedTasks.delete(requestId);
            }
        }
        for (const [requestId, task] of this.activeTasks.entries()) {
            if (task.createdAt < expiredThreshold) {
                this.activeTasks.delete(requestId);
                this.logger.warn(`清理超时任务: ${requestId}`, 'RequestPool');
            }
        }
        if (this.completedTasks.size > this.maxHistorySize) {
            const sortedTasks = Array.from(this.completedTasks.entries())
                .sort(([, a], [, b]) => (b.completedAt || 0) - (a.completedAt || 0));
            this.completedTasks.clear();
            sortedTasks.slice(0, this.maxHistorySize).forEach(([id, task]) => {
                this.completedTasks.set(id, task);
            });
        }
        this.logger.debug(`请求池清理完成 - 活跃任务: ${this.activeTasks.size}, 历史任务: ${this.completedTasks.size}`, 'RequestPool');
    }
    async enqueueWithPool(task) {
        return new Promise((resolve, reject) => {
            this.activeTasks.set(task.requestId, task);
            const timeoutId = setTimeout(() => {
                this.activeTasks.delete(task.requestId);
                reject(new Error(`请求超时: ${task.requestId}`));
            }, task.timeout);
            this.queue.add('execute-request', {
                requestId: task.requestId,
                createdAt: task.createdAt
            }, {
                priority: task.priority,
                delay: 0,
                jobId: task.requestId
            }).then(() => {
                const checkCompletion = () => {
                    const completedTask = this.completedTasks.get(task.requestId);
                    if (completedTask) {
                        clearTimeout(timeoutId);
                        this.activeTasks.delete(task.requestId);
                        if (completedTask.handler) {
                            resolve(completedTask.handler);
                        }
                        else {
                            reject(new Error(`任务执行失败: ${task.requestId}`));
                        }
                    }
                    else {
                        setTimeout(checkCompletion, 100);
                    }
                };
                checkCompletion();
            }).catch(reject);
        });
    }
    async executeTask(task) {
        task.startedAt = Date.now();
        try {
            const result = await Promise.race([
                task.handler(),
                new Promise((_, reject) => setTimeout(() => reject(new Error('任务执行超时')), task.timeout))
            ]);
            task.completedAt = Date.now();
            this.completedTasks.set(task.requestId, task);
            return result;
        }
        catch (error) {
            task.completedAt = Date.now();
            this.completedTasks.set(task.requestId, { ...task, handler: undefined });
            throw error;
        }
    }
    async processJob(job) {
        const { requestId } = job.data;
        const task = this.activeTasks.get(requestId);
        if (!task) {
            throw new Error(`未找到任务: ${requestId}`);
        }
        task.startedAt = Date.now();
        try {
            const result = await task.handler();
            task.completedAt = Date.now();
            this.completedTasks.set(requestId, { ...task, handler: result });
            return result;
        }
        catch (error) {
            task.completedAt = Date.now();
            this.completedTasks.set(requestId, { ...task, handler: undefined });
            throw error;
        }
    }
    setupEventListeners() {
        this.worker.on('completed', (job) => {
            this.logger.debug(`任务完成: ${job.data.requestId}`, 'RequestPool');
        });
        this.worker.on('failed', (job, err) => {
            this.logger.error(`任务失败: ${job?.data?.requestId} - ${err.message}`, 'RequestPool');
        });
        this.worker.on('error', (err) => {
            this.logger.error(`Worker错误: ${err.message}`, 'RequestPool');
        });
    }
    startCleanupTimer() {
        setInterval(() => {
            this.cleanup().catch(err => {
                this.logger.error(`请求池清理失败: ${err.message}`, 'RequestPool');
            });
        }, 60000);
    }
    calculateAverages() {
        const recentTasks = Array.from(this.completedTasks.values())
            .filter(task => task.completedAt && task.completedAt > Date.now() - 300000)
            .slice(-100);
        if (recentTasks.length === 0) {
            return { averageWaitTime: 0, averageProcessingTime: 0 };
        }
        const waitTimes = recentTasks
            .filter(task => task.startedAt)
            .map(task => task.startedAt - task.createdAt);
        const processingTimes = recentTasks
            .filter(task => task.startedAt && task.completedAt)
            .map(task => task.completedAt - task.startedAt);
        const averageWaitTime = waitTimes.length > 0
            ? waitTimes.reduce((sum, time) => sum + time, 0) / waitTimes.length
            : 0;
        const averageProcessingTime = processingTimes.length > 0
            ? processingTimes.reduce((sum, time) => sum + time, 0) / processingTimes.length
            : 0;
        return { averageWaitTime, averageProcessingTime };
    }
};
exports.RequestPoolService = RequestPoolService;
exports.RequestPoolService = RequestPoolService = __decorate([
    (0, common_1.Injectable)(),
    __param(1, (0, common_1.Inject)('RESPONSE_MANAGER_CONFIG')),
    __metadata("design:paramtypes", [logger_service_1.LoggerService, Object])
], RequestPoolService);
//# sourceMappingURL=request-pool.service.js.map