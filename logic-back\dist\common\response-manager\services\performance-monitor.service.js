"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PerformanceMonitorService = void 0;
const common_1 = require("@nestjs/common");
const os = require("os");
const process = require("process");
const logger_service_1 = require("../../logger/logger.service");
let PerformanceMonitorService = class PerformanceMonitorService {
    logger;
    config;
    monitoringData = new Map();
    maxHistorySize = 10000;
    constructor(logger, config) {
        this.logger = logger;
        this.config = config;
        this.startCleanupTimer();
    }
    startMonitoring(requestId) {
        if (!this.config.enableMetrics) {
            return;
        }
        const monitoringData = {
            requestId,
            startTime: Date.now(),
            startMemory: process.memoryUsage(),
            startCpuUsage: process.cpuUsage(),
            dbQueryCount: 0,
            cacheHits: 0,
            cacheMisses: 0,
            externalApiCalls: 0
        };
        this.monitoringData.set(requestId, monitoringData);
        this.logger.debug(`开始性能监控: ${requestId}`, 'PerformanceMonitor');
    }
    endMonitoring(requestId) {
        const data = this.monitoringData.get(requestId);
        if (!data || !this.config.enableMetrics) {
            return this.getDefaultMetrics();
        }
        const endTime = Date.now();
        const endMemory = process.memoryUsage();
        const endCpuUsage = process.cpuUsage(data.startCpuUsage);
        const metrics = {
            executionTime: endTime - data.startTime,
            memoryUsage: this.calculateMemoryUsage(data.startMemory, endMemory),
            cpuUsage: this.calculateCpuUsage(endCpuUsage),
            dbQueryCount: data.dbQueryCount,
            cacheHits: data.cacheHits,
            cacheMisses: data.cacheMisses,
            externalApiCalls: data.externalApiCalls
        };
        this.monitoringData.delete(requestId);
        this.logPerformanceMetrics(requestId, metrics);
        return metrics;
    }
    recordDbQuery(requestId) {
        const data = this.monitoringData.get(requestId);
        if (data) {
            data.dbQueryCount++;
        }
    }
    recordCacheOperation(requestId, hit) {
        const data = this.monitoringData.get(requestId);
        if (data) {
            if (hit) {
                data.cacheHits++;
            }
            else {
                data.cacheMisses++;
            }
        }
    }
    recordExternalApiCall(requestId) {
        const data = this.monitoringData.get(requestId);
        if (data) {
            data.externalApiCalls++;
        }
    }
    getSystemMetrics() {
        const totalMemory = os.totalmem();
        const freeMemory = os.freemem();
        const usedMemory = totalMemory - freeMemory;
        return {
            cpuUsage: this.getCurrentCpuUsage(),
            memoryUsage: (usedMemory / totalMemory) * 100,
            freeMemory,
            totalMemory,
            loadAverage: os.loadavg(),
            uptime: os.uptime()
        };
    }
    getActiveMonitoringCount() {
        return this.monitoringData.size;
    }
    calculateMemoryUsage(startMemory, endMemory) {
        const heapUsedDiff = endMemory.heapUsed - startMemory.heapUsed;
        const externalDiff = endMemory.external - startMemory.external;
        return heapUsedDiff + externalDiff;
    }
    calculateCpuUsage(cpuUsage) {
        const totalTime = cpuUsage.user + cpuUsage.system;
        return totalTime / 1000;
    }
    getCurrentCpuUsage() {
        const cpus = os.cpus();
        let totalIdle = 0;
        let totalTick = 0;
        cpus.forEach(cpu => {
            for (const type in cpu.times) {
                totalTick += cpu.times[type];
            }
            totalIdle += cpu.times.idle;
        });
        return 100 - (totalIdle / totalTick) * 100;
    }
    getDefaultMetrics() {
        return {
            executionTime: 0,
            memoryUsage: 0,
            cpuUsage: 0,
            dbQueryCount: 0,
            cacheHits: 0,
            cacheMisses: 0,
            externalApiCalls: 0
        };
    }
    logPerformanceMetrics(requestId, metrics) {
        if (!this.config.enableDetailedLogging) {
            return;
        }
        const logData = {
            requestId,
            executionTime: metrics.executionTime,
            memoryUsage: metrics.memoryUsage,
            cpuUsage: metrics.cpuUsage,
            dbQueryCount: metrics.dbQueryCount,
            cacheHits: metrics.cacheHits,
            cacheMisses: metrics.cacheMisses,
            externalApiCalls: metrics.externalApiCalls,
            cacheHitRate: metrics.cacheHits + metrics.cacheMisses > 0
                ? (metrics.cacheHits / (metrics.cacheHits + metrics.cacheMisses)) * 100
                : 0
        };
        let logLevel = 'info';
        if (metrics.executionTime > 5000) {
            logLevel = 'error';
        }
        else if (metrics.executionTime > 2000) {
            logLevel = 'warn';
        }
        const message = `性能指标 [${requestId}] - 执行时间: ${metrics.executionTime}ms, 内存: ${(metrics.memoryUsage / 1024 / 1024).toFixed(2)}MB, DB查询: ${metrics.dbQueryCount}次`;
        switch (logLevel) {
            case 'info':
                this.logger.log(message, JSON.stringify(logData));
                break;
            case 'warn':
                this.logger.warn(message, JSON.stringify(logData));
                break;
            case 'error':
                this.logger.error(message, JSON.stringify(logData));
                break;
        }
    }
    startCleanupTimer() {
        setInterval(() => {
            this.cleanupStaleMonitoring();
        }, 300000);
    }
    cleanupStaleMonitoring() {
        const now = Date.now();
        const staleThreshold = 600000;
        let cleanedCount = 0;
        for (const [requestId, data] of this.monitoringData.entries()) {
            if (now - data.startTime > staleThreshold) {
                this.monitoringData.delete(requestId);
                cleanedCount++;
            }
        }
        if (cleanedCount > 0) {
            this.logger.warn(`清理了 ${cleanedCount} 个过期的性能监控数据`, 'PerformanceMonitor');
        }
        if (this.monitoringData.size > this.maxHistorySize) {
            const sortedEntries = Array.from(this.monitoringData.entries())
                .sort(([, a], [, b]) => a.startTime - b.startTime);
            const toRemove = this.monitoringData.size - this.maxHistorySize;
            for (let i = 0; i < toRemove; i++) {
                this.monitoringData.delete(sortedEntries[i][0]);
            }
            this.logger.warn(`清理了 ${toRemove} 个最旧的性能监控数据`, 'PerformanceMonitor');
        }
    }
};
exports.PerformanceMonitorService = PerformanceMonitorService;
exports.PerformanceMonitorService = PerformanceMonitorService = __decorate([
    (0, common_1.Injectable)(),
    __param(1, (0, common_1.Inject)('RESPONSE_MANAGER_CONFIG')),
    __metadata("design:paramtypes", [logger_service_1.LoggerService, Object])
], PerformanceMonitorService);
//# sourceMappingURL=performance-monitor.service.js.map